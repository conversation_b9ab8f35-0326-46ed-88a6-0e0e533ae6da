// src/validations/vendorSchema.ts
import * as yup from "yup";

export const vendorSchema = yup.object({
  businessName: yup.string().required("Business name is required"),
  email: yup.string().email("Invalid email").required("Email is required"),
  taxId: yup.string().required("Tax ID is required"),
  contactPerson: yup.string().required("Contact person name is required"),
  phoneNumber: yup
    .string()
    .matches(/^[0-9]{10}$/, "Invalid phone number")
    .required("Phone number is required"),
  address: yup.string().required("Address is required"),
  street: yup.string().required("Street is required"),
  city: yup.string().required("City is required"),
  state: yup.string().required("State is required"),
  postalCode: yup
    .string()
    .matches(/^[0-9]{6}$/, "Invalid postal code")
    .required("Postal code is required"),
  currentDealers: yup.string().optional(),
});

export type VendorFormData = yup.InferType<typeof vendorSchema>;
