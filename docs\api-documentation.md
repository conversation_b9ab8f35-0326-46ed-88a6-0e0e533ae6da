# Metropolis B2B Application API documentation

The API documentation providing routes being used in the application.

## Table of Contents

- [API version control](#api-version-control)
- [Authentication](#authentication)
- [Vendor Features](#vendor-features)
- [Admin Features](#admin-features)

## API version control

For the API version control we need to suffix `v1` in the `/api/` route, E.g.

```http
/api/v1/vendor/products
```

## Authentication

### Vendor Authentication

```http
POST /api/auth/vendor/register
POST /api/auth/vendor/login
POST /api/auth/vendor/logout
```

### Admin Authentication

```http
POST /api/auth/admin/login
POST /api/auth/admin/logout
```

## Vendor Features

### Profile Management

```http
GET    /api/vendor/profile
PUT    /api/vendor/profile
PUT    /api/vendor/profile/password
POST   /api/vendor/profile/documents
GET    /api/vendor/profile/documents
```

### Categories Access

```http
GET /api/vendor/categories
GET /api/vendor/categories/{id}
```

## Product Access based on Categories

```http
GET /api/vendor/categories
GET /api/vendor/categories/{categoryId}
GET /api/vendor/categories/{categoryId}/products
```

### Product Access

```http
GET    /api/vendor/products
GET    /api/vendor/products/{id}
```

### Cart Management

```http
POST   /api/vendor/cart
GET    /api/vendor/cart
PUT    /api/vendor/cart/{itemId}
DELETE /api/vendor/cart/{itemId}
DELETE /api/vendor/cart
```

### Order Management

```http
POST   /api/vendor/orders
GET    /api/vendor/orders
GET    /api/vendor/orders/{id}
```

### Dashboard

```http
GET    /api/vendor/dashboard/stats
```

### Notifications

```http
GET    /api/vendor/notifications
PUT    /api/vendor/notifications/{id}/read
```

## Admin Features

### Vendor Management

```http
GET    /api/admin/vendors
GET    /api/admin/vendors/{id}
PUT    /api/admin/vendors/{id}/status
GET    /api/admin/vendors/{id}/documents
```

### Category Management

```http
POST   /api/admin/categories
GET    /api/admin/categories
GET    /api/admin/categories/{id}
PUT    /api/admin/categories/{id}
DELETE /api/admin/categories/{id}
```

## Product Access based on Categories

```http
POST /api/admin/categories/{categoryId}/products
PUT /api/admin/categories/{categoryId}/products/{id}
DELETE /api/admin/categories/{categoryId}/products/{id}
GET /api/admin/categories/{categoryId}/products
GET /api/admin/categories/{categoryId}/brands
GET /api/admin/categories/{categoryId}/brands/{brandId}/products
```

### Product Management

```http
POST   /api/admin/products
PUT    /api/admin/products/{id}
DELETE /api/admin/products/{id}
```

### Order Management

```http
GET    /api/admin/orders
GET    /api/admin/orders/{id}
PUT    /api/admin/orders/{id}/status
POST   /api/admin/orders/{id}/approve
POST   /api/admin/orders/{id}/reject
```

### Dashboard

```http
GET    /api/admin/dashboard/stats
GET    /api/admin/dashboard/recent-orders
GET    /api/admin/dashboard/top-vendors
```

### Reports

```http
GET    /api/admin/reports/sales
GET    /api/admin/reports/vendors
```

### Settings

```http
GET    /api/admin/settings
PUT    /api/admin/settings
```

### Filters

```http
GET    /api/admin/filters
```

## Request/Response Examples

### Vendor Registration

```javascript
// Request
POST /api/auth/vendor/register
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "businessName": "Vendor Company",
  "businessAddress": "123 Business St",
  "taxId": "TAX123456",
  "contactNumber": "+1234567890"
}

// Response
{
  "status": "success",
  "message": "Registration successful",
  "data": {
    "vendorId": "v123",
    "businessName": "Vendor Company",
    "email": "<EMAIL>"
  }
}
```

### Add Product (Admin)

```javascript
// Request
POST /api/admin/products
{
  "name": "Product Name",
  "description": "Product description",
  "price": 99.99,
  "categoryId": "cat123",
  "sku": "SKU123",
  "packagingSize": 10,
  "packagingUnit": "piece"
}

// Response
{
  "status": "success",
  "message": "Product created successfully",
  "data": {
    "productId": "p123",
    "name": "Product Name",
    "sku": "SKU123"
  }
}
```

### Place Order (Vendor)

```javascript
// Request
POST /api/vendor/orders
{
  "shippingAddress": {
    "street": "123 Shipping St",
    "city": "City",
    "state": "State",
    "zip": "12345"
  },
  "billingAddress": {
    "street": "123 Billing St",
    "city": "City",
    "state": "State",
    "zip": "12345"
  },
  "paymentMethod": "credit_card",
  "items": [
    {
      "productId": "p123",
      "quantity": 15
    }
  ]
}

// Response
{
  "status": "success",
  "message": "Order placed successfully",
  "data": {
    "orderId": "ord123",
    "status": "pending",
    "total": 1499.85
  }
}
```

### Sample API
```json
{
  "name": "Product Name",
  "description": "Product description",
  "price": 99.99,
  "categoryId": "cat123",
  "sku": "SKU123",
  "packagingSize": 10,
  "packagingUnit": "piece"
}
```