"use client";
import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { CheckCircle } from "lucide-react";
import { vendorAPI as api } from "@/lib/axios";

export default function OrderSuccess() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get("orderId");
  const [isAuthenticated, setIsAuthenticated] = useState(true);

  useEffect(() => {
    if (!orderId) {
      router.push("/vendor/dashboard");
      return;
    }

    // Check authentication status
    const checkAuth = async () => {
      try {
        // Make a simple request to check auth
        await api.get("/auth/check");
        setIsAuthenticated(true);
      } catch (error) {
        console.error("Auth check failed:", error);
        setIsAuthenticated(false);
        router.push("/vendor/login?redirect=/vendor/orders");
      }
    };

    checkAuth();
  }, [orderId, router]);

  if (!isAuthenticated) {
    return <div>Checking authentication...</div>;
  }

  return (
    <div className="max-w-2xl mx-auto mt-10 p-6 bg-white rounded-lg shadow">
      <div className="text-center">
        <CheckCircle className="w-16 h-16 text-green-500 mx-auto" />
        <h1 className="mt-4 text-2xl font-bold text-gray-900">
          Order Placed Successfully!
        </h1>
        <p className="mt-2 text-gray-600">
          Your order #{orderId} has been placed and is pending approval.
        </p>
      </div>

      <div className="mt-8 space-y-4">
        <p className="text-sm text-gray-600">
          • Your order is currently pending approval from the admin
        </p>
        <p className="text-sm text-gray-600">
          • You will receive an email notification once your order is approved
        </p>
        <p className="text-sm text-gray-600">
          • You can track your order status in the Orders section
        </p>
      </div>

      <div className="mt-8 flex justify-center space-x-4">
        <button
          onClick={() => router.push("/vendor/orders")}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          View Orders
        </button>
        <button
          onClick={() => router.push("/vendor/dashboard")}
          className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50"
        >
          Continue Shopping
        </button>
      </div>
    </div>
  );
}
