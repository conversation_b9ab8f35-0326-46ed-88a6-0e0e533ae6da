# Packaging Size Image Feature Documentation

## Overview
This feature adds packaging size banner images to subcategories (brands) that will be displayed as banners on product pages. When users click on a subcategory, the packaging size image will be shown as a banner on the product listing page.

## Database Changes

### Brand Model Update
Added `packagingSizeImage` field to the Brand model:

```prisma
model Brand {
  id              String    @id @default(cuid())
  name            String    @unique
  description     String?
  imageUrl        String?
  imageAspectRatio String    @default("1:1")
  packagingSizeImage String? // 1000x1000 packaging size image for banner display
  price           Float?
  materialId      String?
  sizeId          String?
  isActive        Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  products        Product[]
  priceMasters    PriceMaster[]
}
```

## API Updates

### Admin Brands API
**Endpoint:** `GET /api/v1/admin/brands`
**Endpoint:** `POST /api/v1/admin/brands`

**Updated Response Format:**
```json
{
  "message": "Brands fetched successfully",
  "success": true,
  "data": [
    {
      "id": "brand_id",
      "name": "Brand Name",
      "description": "Brand description",
      "imageUrl": "brand_image_url",
      "imageAspectRatio": "1:1",
      "packagingSizeImage": "packaging_size_image_url",
      "price": 100.00,
      "materialId": "material_id",
      "sizeId": "size_id",
      "isActive": true,
      "createdAt": "2025-01-15T10:30:00Z",
      "updatedAt": "2025-01-15T10:30:00Z"
    }
  ]
}
```

**Create Brand Request:**
```json
{
  "name": "Brand Name",
  "imageUrl": "brand_image_url",
  "packagingSizeImage": "packaging_size_image_url",
  "imageAspectRatio": "1:1",
  "price": 100.00,
  "materialId": "material_id",
  "sizeId": "size_id",
  "isActive": true
}
```

### Vendor Brands API
**Endpoint:** `GET /api/v1/vendor/brands`

**Updated Response Format:**
Same as admin API, includes `packagingSizeImage` field.

### Category Brands API
**Admin:** `GET /api/v1/admin/categories/{id}/brands`
**Vendor:** `GET /api/v1/vendor/categories/{id}/brands`

Both APIs now include the `packagingSizeImage` field in the response.

## Frontend Integration

### Admin Brand Upload Form
The `AddBrandModal` component has been updated to include:

1. **Packaging Size Image Upload Section:**
   - Separate image uploader for packaging size images
   - Fixed 1000x1000px dimensions
   - Optional field (not required)

2. **Form Fields:**
   - Sub Category Image (required) - 1000x1000px
   - Packaging Size Banner Image (optional) - 1000x1000px

### Usage in Product Pages
The packaging size image can be used as a banner on product pages:

```typescript
// Example usage in product page component
interface Brand {
  id: string;
  name: string;
  packagingSizeImage?: string;
  // ... other fields
}

// In your product page component
const ProductPage = ({ categoryId, brandId }: { categoryId: string, brandId?: string }) => {
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);

  // When user selects a brand/subcategory
  useEffect(() => {
    if (brandId) {
      // Fetch brand details including packagingSizeImage
      fetchBrandDetails(brandId).then(setSelectedBrand);
    }
  }, [brandId]);

  return (
    <div>
      {/* Display packaging size image as banner */}
      {selectedBrand?.packagingSizeImage && (
        <div className="w-full mb-6">
          <img
            src={selectedBrand.packagingSizeImage}
            alt={`${selectedBrand.name} packaging size`}
            className="w-full h-48 object-cover rounded-lg"
          />
        </div>
      )}
      
      {/* Product listings */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {/* Product cards */}
      </div>
    </div>
  );
};
```

## Image Specifications

### Packaging Size Images
- **Dimensions:** 1000x1000 pixels
- **Aspect Ratio:** 1:1 (Square)
- **Format:** JPG, PNG, WebP
- **Usage:** Banner display on product pages
- **Upload Location:** Admin panel > Categories > Subcategories > Add/Edit

### Regular Brand Images
- **Dimensions:** 1000x1000 pixels
- **Aspect Ratio:** 1:1 or 9:16 (configurable)
- **Format:** JPG, PNG, WebP
- **Usage:** Brand/subcategory display in listings

## Implementation Flow

1. **Admin uploads subcategory with packaging size image:**
   - Navigate to Categories > Select Category > Subcategories
   - Click "Add New Sub Category"
   - Upload regular subcategory image (required)
   - Upload packaging size banner image (optional)
   - Save subcategory

2. **Vendor/User navigates to products:**
   - Select category from dashboard
   - Select subcategory (brand)
   - Product page displays with packaging size image as banner
   - Products are listed below the banner

3. **API data flow:**
   - Dashboard API returns categories
   - Category selection API returns subcategories with `packagingSizeImage`
   - Product page receives subcategory data via props
   - Banner is displayed if `packagingSizeImage` exists

## Database Migration

To add the new field to existing database:

```sql
ALTER TABLE "Brand" ADD COLUMN "packagingSizeImage" TEXT;
```

Or using Prisma:

```bash
npx prisma db push
```

## Benefits

1. **Enhanced Visual Experience:** Product pages have attractive banners showing packaging information
2. **Better Brand Recognition:** Packaging size images help users understand product sizing
3. **Improved Navigation:** Visual cues help users identify the right subcategory
4. **Flexible Implementation:** Optional field doesn't break existing functionality
5. **Consistent Sizing:** Fixed 1000x1000px ensures consistent display across all devices

## Future Enhancements

1. **Multiple Banner Images:** Support for multiple packaging size images per subcategory
2. **Dynamic Sizing:** Allow different banner dimensions based on content
3. **Banner Management:** Separate admin interface for managing banner images
4. **Analytics:** Track banner click-through rates and effectiveness
5. **A/B Testing:** Test different banner images for conversion optimization
