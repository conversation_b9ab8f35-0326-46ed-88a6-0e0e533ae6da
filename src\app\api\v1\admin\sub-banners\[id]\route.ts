import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

// Interface for updating a sub-banner
interface UpdateSubBannerRequest {
  imageUrl?: string;
  name?: string;
  isActive?: boolean;
}

// GET a specific sub-banner by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const subBanner = await prisma.subBanner.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!subBanner) {
      return NextResponse.json(
        { message: "Sub-banner not found", success: false },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: "Sub-banner fetched successfully",
      success: true,
      data: subBanner,
    });
  } catch (error) {
    console.error("Error fetching sub-banner:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch sub-banner",
        success: false,
      },
      { status: 500 }
    );
  }
}

// UPDATE a sub-banner
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json() as UpdateSubBannerRequest;
    const { imageUrl, name, isActive } = body;

    // Check if sub-banner exists
    const existingSubBanner = await prisma.subBanner.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!existingSubBanner) {
      return NextResponse.json(
        { message: "Sub-banner not found", success: false },
        { status: 404 }
      );
    }

    // Update the sub-banner
    const updatedSubBanner = await prisma.subBanner.update({
      where: {
        id: params.id,
      },
      data: {
        imageUrl,
        name,
        isActive,
      },
    });

    return NextResponse.json({
      message: "Sub-banner updated successfully",
      success: true,
      data: updatedSubBanner,
    });
  } catch (error) {
    console.error("Error updating sub-banner:", error);
    return NextResponse.json(
      {
        message: "Failed to update sub-banner",
        success: false,
      },
      { status: 500 }
    );
  }
}

// DELETE a sub-banner
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if sub-banner exists
    const existingSubBanner = await prisma.subBanner.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!existingSubBanner) {
      return NextResponse.json(
        { message: "Sub-banner not found", success: false },
        { status: 404 }
      );
    }

    // Delete the sub-banner
    await prisma.subBanner.delete({
      where: {
        id: params.id,
      },
    });

    return NextResponse.json({
      message: "Sub-banner deleted successfully",
      success: true,
    });
  } catch (error) {
    console.error("Error deleting sub-banner:", error);
    return NextResponse.json(
      {
        message: "Failed to delete sub-banner",
        success: false,
      },
      { status: 500 }
    );
  }
}
