import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

export async function GET(request: NextRequest) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
      id: string;
      email: string;
      role: string;
    };

    if (decoded.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Unauthorized - Not an admin", success: false },
        { status: 401 }
      );
    }

    const admin = await prisma.adminUser.findUnique({
      where: { id: decoded.id },
    });

    if (!admin) {
      return NextResponse.json(
        { message: "Admin not found", success: false },
        { status: 404 }
      );
    }

    // Get unread notification count for this admin
    const unreadCount = await prisma.notification.count({
      where: {
        OR: [
          { recipientId: admin.id, recipientType: "admin" },
          { recipientId: null, recipientType: "admin" }, // General notifications for all admins
        ],
        isRead: false,
      },
    });

    return NextResponse.json({
      message: "Unread notification count fetched successfully",
      success: true,
      data: {
        unreadCount,
      },
    });
  } catch (error) {
    console.error("Error fetching unread notification count:", error);
    return NextResponse.json(
      { message: "Failed to fetch unread notification count", success: false },
      { status: 500 }
    );
  }
}
