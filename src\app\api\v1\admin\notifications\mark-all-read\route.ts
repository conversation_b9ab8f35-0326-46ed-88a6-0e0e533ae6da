import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

export async function PATCH(request: NextRequest) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
      id: string;
      email: string;
      role: string;
    };

    if (decoded.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Unauthorized - Not an admin", success: false },
        { status: 401 }
      );
    }

    const admin = await prisma.adminUser.findUnique({
      where: { id: decoded.id },
    });

    if (!admin) {
      return NextResponse.json(
        { message: "Admin not found", success: false },
        { status: 404 }
      );
    }

    // Mark all notifications as read for this admin
    const result = await prisma.notification.updateMany({
      where: {
        OR: [
          { recipientId: admin.id, recipientType: "admin" },
          { recipientId: null, recipientType: "admin" },
        ],
        isRead: false,
      },
      data: {
        isRead: true,
      },
    });

    return NextResponse.json({
      message: "All notifications marked as read",
      success: true,
      data: {
        updatedCount: result.count,
      },
    });
  } catch (error) {
    console.error("Error marking all notifications as read:", error);
    return NextResponse.json(
      { message: "Failed to mark all notifications as read", success: false },
      { status: 500 }
    );
  }
}
