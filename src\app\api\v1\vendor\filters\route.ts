import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    console.log("Fetching all filter options");
    
    // Fetch materials
    const materials = await prisma.material.findMany({
      where: {
        isActive: true,
      },
      orderBy: {
        name: "asc",
      },
    });
    
    console.log(`Found ${materials.length} materials`);
    
    // Fetch sizes
    const sizes = await prisma.size.findMany({
      where: {
        isActive: true,
      },
      orderBy: {
        name: "asc",
      },
    });
    
    console.log(`Found ${sizes.length} sizes`);
    
   
    

    
    return NextResponse.json({
      message: "Filter options fetched successfully",
      success: true,
      data: {
        materials,
        sizes
      },
    });
  } catch (error) {
    console.error("Error fetching filter options:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch filter options",
        success: false,
      },
      { status: 500 }
    );
  }
}
