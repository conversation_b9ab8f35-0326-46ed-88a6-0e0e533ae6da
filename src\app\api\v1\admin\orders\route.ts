import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

export async function GET(request: NextRequest) {
  try {
    // Get the auth token from cookies
    const authToken = request.cookies.get("authToken");

    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized - No token", success: false },
        { status: 401 }
      );
    }

    try {
      // Verify the token
      const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
        id: string;
        email: string;
        role: string;
      };

      // Check if the user is an admin
      if (decoded.role !== "ADMIN") {
        return NextResponse.json(
          { message: "Unauthorized - Not an admin", success: false },
          { status: 401 }
        );
      }
    } catch (jwtError) {
      console.error("JWT verification error:", jwtError);
      return NextResponse.json(
        { message: "Unauthorized - Invalid token", success: false },
        { status: 401 }
      );
    }

    console.log("Fetching orders from database...");

    try {
      // Get query parameters
      const searchParams = request.nextUrl.searchParams;
      const status = searchParams.get("status");
      const vendorId = searchParams.get("vendorId");
      const startDateParam = searchParams.get("startDate");
      const endDateParam = searchParams.get("endDate");
      const searchQuery = searchParams.get("search");
      const page = parseInt(searchParams.get("page") || "1");
      const limit = parseInt(searchParams.get("limit") || "10");
      const skip = (page - 1) * limit;

      // Log the raw date parameters
      console.log("Raw date parameters:", { startDateParam, endDateParam });

      // Parse dates safely
      let startDate = null;
      let endDate = null;

      if (startDateParam) {
        try {
          startDate = startDateParam;
        } catch (e) {
          console.error("Invalid start date format:", startDateParam);
        }
      }

      if (endDateParam) {
        try {
          endDate = endDateParam;
        } catch (e) {
          console.error("Invalid end date format:", endDateParam);
        }
      }

      // Build where clause for filtering
      let where: any = {};

      // Filter by status
      if (status && status !== "all") {
        where.status = status;
      }

      // Filter by vendor
      if (vendorId && vendorId !== "all") {
        where.vendorId = vendorId;
      }

      // Filter by date range
      if (startDate || endDate) {
        where.createdAt = {};

        if (startDate) {
          // Set time to start of day (00:00:00)
          const startDateTime = new Date(startDate);
          startDateTime.setHours(0, 0, 0, 0);
          where.createdAt.gte = startDateTime;
          console.log("Start date filter:", startDateTime.toISOString());
        }

        if (endDate) {
          // Set time to end of day (23:59:59.999)
          const endDateTime = new Date(endDate);
          endDateTime.setHours(23, 59, 59, 999);
          where.createdAt.lte = endDateTime;
          console.log("End date filter:", endDateTime.toISOString());
        }
      }

      // Search by order ID
      if (searchQuery) {
        where.id = {
          contains: searchQuery,
          mode: 'insensitive',
        };
      }

      // Count total orders matching the filter
      const totalOrders = await prisma.order.count({ where });

      // Fetch filtered and paginated orders
      const orders = await prisma.order.findMany({
        where,
        include: {
          items: {
            include: {
              product: true,
            },
          },
          vendor: {
            select: {
              id: true,
              businessName: true,
              email: true,
              contactPerson: true,
              maxCredit: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      });

      console.log(`Found ${orders.length} orders out of ${totalOrders} total`);

      return NextResponse.json({
        message: "Orders fetched successfully",
        success: true,
        data: orders,
        pagination: {
          total: totalOrders,
          page,
          limit,
          totalPages: Math.ceil(totalOrders / limit)
        }
      });
    } catch (dbError) {
      console.error("Database error:", dbError);
      return NextResponse.json(
        {
          message: "Database error: " + (dbError instanceof Error ? dbError.message : "Unknown error"),
          success: false,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error in GET orders:", error);
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "An unknown error occurred",
        success: false,
      },
      { status: 500 }
    );
  }
}
