// src/middleware.ts
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import * as jose from "jose";

interface DecodedToken {
  id: string;
  email: string;
  role?: string;
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const authToken = request.cookies.get("authToken");

  // Public routes that don't need authentication
  const publicPaths = [
    "/api/v1/auth/vendor/login",
    "/api/v1/auth/vendor/logout",
    "/api/v1/auth/vendor/register",
    "/api/v1/auth/admin/login",
    "/auth/vendor/login",
    "/auth/vendor/register",
    "/admin/login",
  ];

  const isPublicPath = publicPaths.some((path) => pathname === path);
  if (isPublicPath) {
    return NextResponse.next();
  }

  if (!authToken?.value) {
    if (pathname.startsWith("/api/v1/")) {
      return new NextResponse(
        JSON.stringify({
          success: false,
          message: "Authentication required",
        }),
        {
          status: 401,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
    }

    if (pathname.startsWith("/admin/")) {
      return NextResponse.redirect(new URL("/admin/login", request.url));
    }
    if (pathname.startsWith("/vendor/")) {
      return NextResponse.redirect(new URL("/auth/vendor/login", request.url));
    }
  }

  try {
    if (!authToken?.value) {
      throw new Error("No auth token");
    }

    const secret = new TextEncoder().encode(process.env.JWT_KEY!);

    try {
      const { payload } = await jose.jwtVerify(authToken.value, secret, {
        algorithms: ["HS256"],
      });
      console.log("Debug - Jose verification succeeded");

      const decoded = payload as unknown as DecodedToken;

      // Protect admin routes
      if (
        pathname.startsWith("/admin/") ||
        pathname.startsWith("/api/v1/admin/")
      ) {
        if (decoded.role !== "ADMIN") {
          return NextResponse.redirect(new URL("/admin/login", request.url));
        }
      }

      // Protect vendor routes
      if (
        pathname.startsWith("/vendor/") ||
        pathname.startsWith("/api/v1/vendor/")
      ) {
        if (decoded.role !== "VENDOR") {
          return NextResponse.redirect(
            new URL("/auth/vendor/login", request.url)
          );
        }
      }

      return NextResponse.next();
    } catch (error) {
      console.error("Debug - Jose verification failed:", error);
    }
  } catch (error) {
    console.error("Debug - Middleware error:", error);

    if (pathname.startsWith("/admin/")) {
      return NextResponse.redirect(new URL("/admin/login", request.url));
    }
    if (pathname.startsWith("/vendor/")) {
      return NextResponse.redirect(new URL("/auth/vendor/login", request.url));
    }

    if (pathname.startsWith("/api/v1/")) {
      return new NextResponse(
        JSON.stringify({
          success: false,
          message: "Authentication required",
        }),
        {
          status: 401,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
    }

    return NextResponse.next();
  }
}

export const config = {
  matcher: [
    "/vendor/:path*",
    "/admin/:path*",
    "/api/v1/vendor/:path*",
    "/api/v1/admin/:path*",
  ],
};
