// src/components/admin/ImageUploaderNoCrop.tsx
import React, { useState, useCallback } from "react";
import { useDropzone, FileWithPath } from "react-dropzone";
import axios from "axios";
import { toast } from "react-toastify";

interface ImageUploaderNoCropProps {
  onImagesUploaded: (urls: string[]) => void;
  existingImages?: string[];
  multiple?: boolean;
  maxFiles?: number;
  imageAspectRatio?: string; // "1:1" or "9:16"
}

interface ImageToProcess {
  file: File;
  preview: string;
}

const ImageUploaderNoCrop: React.FC<ImageUploaderNoCropProps> = ({
  onImagesUploaded,
  existingImages = [],
  multiple = true,
  maxFiles = 20,
  imageAspectRatio = "1:1",
}) => {
  const [uploadedImages, setUploadedImages] = useState<string[]>(existingImages);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [pendingImages, setPendingImages] = useState<File[]>([]);
  const [processingCount, setProcessingCount] = useState<number>(0);
  const [totalToProcess, setTotalToProcess] = useState<number>(0);
  const [modalImage, setModalImage] = useState<string | null>(null);

  const onDrop = useCallback(
    async (acceptedFiles: FileWithPath[]) => {
      if (uploadedImages.length + acceptedFiles.length > maxFiles) {
        toast.error(`You can upload a maximum of ${maxFiles} images`);
        return;
      }

      if (acceptedFiles.length === 0) return;

      setIsUploading(true);
      setTotalToProcess(acceptedFiles.length);
      setProcessingCount(0);

      try {
        const uploadPromises = acceptedFiles.map(async (file, index) => {
          const formData = new FormData();
          formData.append("file", file);
          formData.append("resize", "true");
          formData.append("aspectRatio", imageAspectRatio); // Pass the aspect ratio to the server

          const response = await axios.post("/api/v1/admin/upload", formData, {
            headers: {
              "Content-Type": "multipart/form-data",
            },
            onUploadProgress: (progressEvent) => {
              // This will be called for each file individually
              if (progressEvent.total) {
                const percentCompleted = Math.round(
                  (progressEvent.loaded * 100) / progressEvent.total
                );
                console.log(`File ${index + 1} upload progress: ${percentCompleted}%`);
              }
            },
          });

          setProcessingCount(prev => prev + 1);

          if (response.data.success) {
            return response.data.path as string;
          } else {
            throw new Error(`Failed to upload image ${index + 1}`);
          }
        });

        // Process all uploads in parallel
        const newImageUrls = await Promise.all(uploadPromises);

        // Update state with new images
        const allImages = [...uploadedImages, ...newImageUrls];
        setUploadedImages(allImages);
        onImagesUploaded(allImages);

        toast.success(`Successfully uploaded ${newImageUrls.length} images`);
      } catch (error) {
        console.error("Upload error:", error);
        toast.error(
          `Failed to upload images: ${error instanceof Error ? error.message : "Unknown error"}`
        );
      } finally {
        setIsUploading(false);
        setUploadProgress(0);
      }
    },
    [uploadedImages, maxFiles, onImagesUploaded, imageAspectRatio]
  );

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".webp"],
    },
    maxSize: 10485760, // 10MB
    disabled: isUploading,
  });

  // Remove uploaded image
  const removeImage = (index: number): void => {
    const newImages = [...uploadedImages];
    newImages.splice(index, 1);
    setUploadedImages(newImages);
    onImagesUploaded(newImages);
  };

  // Open image in modal
  const openImageModal = (image: string): void => {
    setModalImage(image);
  };

  // Close image modal
  const closeImageModal = (): void => {
    setModalImage(null);
  };

  return (
    <div className="image-uploader">
      {/* Dropzone area */}
      <div
        {...getRootProps()}
        className={`mt-1 flex justify-center px-3 sm:px-6 pt-4 sm:pt-5 pb-4 sm:pb-6 border-2 border-gray-300 border-dashed rounded-md cursor-pointer ${
          isUploading ? "opacity-50 cursor-not-allowed" : ""
        }`}
      >
        <div className="space-y-1 text-center">
          <input {...getInputProps()} />
          <svg
            className="mx-auto h-8 sm:h-12 w-8 sm:w-12 text-gray-400"
            stroke="currentColor"
            fill="none"
            viewBox="0 0 48 48"
          >
            <path
              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <div className="flex flex-col sm:flex-row text-sm text-gray-600 justify-center items-center">
            <label className="relative cursor-pointer rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
              <span>Upload images</span>
            </label>
            <p className="sm:pl-1">or drag and drop</p>
          </div>
          <p className="text-xs text-gray-500">
            PNG, JPG, WEBP up to 10MB
          </p>
          <p className="text-xs text-gray-500">
            {imageAspectRatio === "1:1"
              ? "Images will be automatically resized to 1:1 ratio (1000x1000px) without cropping"
              : "Images will be automatically resized to 9:16 ratio (562x1000px) without cropping"}
          </p>
        </div>
      </div>

      {/* Upload progress */}
      {isUploading && (
        <div className="mt-4">
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-blue-600 h-2.5 rounded-full"
              style={{ width: `${(processingCount / totalToProcess) * 100}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Processing {processingCount} of {totalToProcess} images...
          </p>
        </div>
      )}

      {/* Image previews */}
      {uploadedImages.length > 0 && (
        <div className="mt-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6">
            {uploadedImages.map((image, index) => (
              <div key={index} className="relative">
                <img
                  src={image}
                  alt={`Uploaded ${index + 1}`}
                  className="h-40 sm:h-60 md:h-80 w-full object-contain rounded-md cursor-pointer"
                  onClick={() => openImageModal(image)}
                />
                <button
                  type="button"
                  className="absolute -top-2 -right-2 rounded-full bg-red-500 text-white p-1 w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center text-xs sm:text-base"
                  onClick={() => removeImage(index)}
                >
                  ×
                </button>
              </div>
            ))}
          </div>
          <p className="mt-2 text-xs text-gray-500 text-center">
            Click on any image to view in full size ({imageAspectRatio === "1:1" ? "1000x1000px" : "562x1000px"})
          </p>
        </div>
      )}

      {/* Full-size image modal */}
      {modalImage && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 p-4" onClick={closeImageModal}>
          <div className="relative max-w-4xl w-full max-h-[90vh] flex items-center justify-center">
            <button
              type="button"
              className="absolute top-2 right-2 z-10 rounded-full bg-red-500 text-white p-1 w-8 h-8 flex items-center justify-center"
              onClick={closeImageModal}
            >
              ×
            </button>
            <img
              src={modalImage}
              alt="Full size preview"
              className="max-w-full max-h-[85vh] object-contain"
              onClick={(e) => e.stopPropagation()}
            />
            <div className="absolute bottom-4 left-0 right-0 text-center text-white text-sm bg-black bg-opacity-50 py-2">
              Actual size: {imageAspectRatio === "1:1" ? "1000x1000px (1:1 ratio)" : "562x1000px (9:16 ratio)"} | Click anywhere outside to close
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUploaderNoCrop;
