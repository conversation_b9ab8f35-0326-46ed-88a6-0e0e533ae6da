import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

const JWT_KEY = process.env.JWT_KEY as string;
if (!JWT_KEY) {
  throw new Error("JWT_KEY is not defined");
}

export async function POST(request: NextRequest) {
  try {
    const { identifier, otp } = await request.json();

    // Check if identifier is email or phone number
    const isEmail = identifier.includes('@');

    // Find the vendor first
    const user = await prisma.vendor.findUnique({
      where: isEmail
        ? { email: identifier }
        : { phoneNumber: identifier },
    });

    if (!user) {
      return NextResponse.json(
        { message: "Invalid credentials", success: false },
        { status: 401 }
      );
    }

    // Find the latest unused OTP for this user's email
    const otpRecord = await prisma.otp.findFirst({
      where: {
        email: user.email, // Always use email for OTP verification
        code: otp,
        isUsed: false,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (!otpRecord) {
      return NextResponse.json(
        { message: "Invalid or expired OTP", success: false },
        { status: 401 }
      );
    }

    // Mark OTP as used
    await prisma.otp.update({
      where: { id: otpRecord.id },
      data: { isUsed: true },
    });

    if (user.status !== "ACTIVE") {
      return NextResponse.json(
        {
          message: "Account is not active",
          success: false,
          data: {
            status: user.status,
            email: user.email,
            redirectUrl: "/auth/vendor/pending-approval"
          }
        },
        { status: 403 }
      );
    }

    // Check for existing FCM token for this user
    const existingFcmToken = await prisma.fCMToken.findFirst({
      where: {
        userId: user.id,
        userType: "vendor",
        isActive: true,
      },
    });

    const payload = {
      id: user.id,
      email: user.email,
      role: "VENDOR",
    };

    const token = jwt.sign(payload, JWT_KEY, {
      algorithm: "HS256",
      expiresIn: "30d",
    });

    const response = NextResponse.json({
      message: "Login successful",
      success: true,
      data: {
        id: user.id,
        email: user.email,
        token: token,
        fcmToken: existingFcmToken?.token || null,
        redirectUrl: "/vendor/dashboard",
      },
    });

    response.cookies.set("authToken", token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/",
      maxAge: 24 * 60 * 60*30, // 30 days
    });

    return response;
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      { message: "An error occurred during login", success: false },
      { status: 500 }
    );
  }
}
