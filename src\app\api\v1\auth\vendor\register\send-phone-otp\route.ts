import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { sendOtpSMS } from "@/lib/sms-service";

export async function POST(request: NextRequest) {
  try {
    const { phoneNumber } = await request.json();

    // Validate phone number
    if (!phoneNumber || !/^[0-9]{10}$/.test(phoneNumber)) {
      return NextResponse.json(
        { message: "Invalid phone number", success: false },
        { status: 400 }
      );
    }

    // Check if phone number is already registered and verified
    const existingVendor = await prisma.vendor.findUnique({
      where: { phoneNumber },
    });

    if (existingVendor && existingVendor.isPhoneVerified) {
      return NextResponse.json(
        { message: "Phone number already registered and verified", success: false },
        { status: 409 }
      );
    }

    // Generate OTP
    const otpCode = Math.floor(1000 + Math.random() * 9000).toString();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes expiry

    // Delete any existing unused OTPs for this phone number
    await prisma.otp.deleteMany({
      where: {
        email: phoneNumber, // Using email field to store phone number for OTP
        isUsed: false,
      },
    });

    // Create new OTP record
    await prisma.otp.create({
      data: {
        email: phoneNumber, // Using email field to store phone number for OTP
        code: otpCode,
        expiresAt: expiresAt,
      },
    });

    // Log OTP sending (mock SMS service)
    try {
      await sendOtpSMS(phoneNumber, otpCode);
    } catch (smsError) {
      console.error("Error with SMS service:", smsError);
      // Continue even if SMS service has an error
    }

    // Prepare response - always include OTP in the response for development
    const response: any = {
      message: "OTP sent successfully",
      success: true,
      data: {
        otp: otpCode
      }
    };

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error("Error sending phone OTP:", error);
    return NextResponse.json(
      { message: "An error occurred while sending OTP", success: false },
      { status: 500 }
    );
  }
}
