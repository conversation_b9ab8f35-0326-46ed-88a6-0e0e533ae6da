"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { PencilIcon, TrashIcon, ArrowLeftIcon } from "@heroicons/react/24/outline";
import { adminAPI as api } from "@/lib/axios";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

interface Brand {
  id: string;
  name: string;
  description?: string;
  imageUrl?: string;
  imageAspectRatio?: string;
  price?: number;
  materialId?: string;
  sizeId?: string;
  isActive: boolean;
  material?: {
    id: string;
    name: string;
  };
  size?: {
    id: string;
    name: string;
  };
}

interface Category {
  id: string;
  name: string;
  description?: string;
  imageUrl?: string;
}

const CategoryBrandsPage = ({ params }: { params: { id: string } }) => {
  const router = useRouter();
  const [brands, setBrands] = useState<Brand[]>([]);
  const [category, setCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState(true);

  // Filter states
  const [materials, setMaterials] = useState<{id: string, name: string}[]>([]);
  const [sizes, setSizes] = useState<{id: string, name: string}[]>([]);
  const [materialFilter, setMaterialFilter] = useState<string>("");
  const [sizeFilter, setSizeFilter] = useState<string>("");
  const [minPrice, setMinPrice] = useState<string>("");
  const [maxPrice, setMaxPrice] = useState<string>("");
  const [tempMinPrice, setTempMinPrice] = useState<string>("");
  const [tempMaxPrice, setTempMaxPrice] = useState<string>("");
  const [sort, setSort] = useState<string>("name_asc");
  const [priceFilterOpen, setPriceFilterOpen] = useState<boolean>(false);

  const fetchCategory = async () => {
    try {
      const { data } = await api.get(`/categories/${params.id}`);
      if (data.success) {
        setCategory(data.data);
      } else {
        toast.error("Failed to fetch category details");
      }
    } catch (error) {
      console.error("Error fetching category:", error);
      toast.error("Failed to fetch category details");
    }
  };

  const fetchFilterOptions = async () => {
    try {
      const { data } = await api.get("/filters");
      if (data.success) {
        setMaterials(data.data.materials);
        setSizes(data.data.sizes);
      } else {
        setMaterials([]);
        setSizes([]);
      }
    } catch (error) {
      console.error("Error fetching filter options:", error);
      setMaterials([]);
      setSizes([]);
    }
  };

  const fetchBrands = async () => {
    try {
      setLoading(true);

      // Build query parameters
      const queryParams = new URLSearchParams();
      if (materialFilter) queryParams.append("materialId", materialFilter);
      if (sizeFilter) queryParams.append("sizeId", sizeFilter);
      if (minPrice) queryParams.append("minPrice", minPrice);
      if (maxPrice) queryParams.append("maxPrice", maxPrice);
      if (sort) queryParams.append("sort", sort);

      const url = `/categories/${params.id}/brands${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

      console.log("Fetching brands with URL:", url);
      const { data } = await api.get(url);
      if (data.success) {
        setBrands(data.data);
      } else {
        toast.error("Failed to fetch brands");
        setBrands([]);
      }
    } catch (error) {
      console.error("Error fetching brands:", error);
      toast.error("Failed to fetch brands");
      setBrands([]);
    } finally {
      setLoading(false);
    }
  };

  const resetFilters = () => {
    setMaterialFilter("");
    setSizeFilter("");
    setMinPrice("");
    setMaxPrice("");
    setTempMinPrice("");
    setTempMaxPrice("");
    setSort("name_asc");
  };

  useEffect(() => {
    fetchCategory();
    fetchFilterOptions();
  }, []);

  useEffect(() => {
    fetchBrands();
  }, [materialFilter, sizeFilter, minPrice, maxPrice, sort, params.id]);

  const getImageUrl = (url?: string) => {
    if (!url) return null;
    if (url.startsWith("http://") || url.startsWith("https://")) {
      return url;
    }
    return `${process.env.NEXT_PUBLIC_API_URL || ""}${
      url.startsWith("/") ? "" : "/"
    }${url}`;
  };

  if (loading) {
    return <div className="p-4">Loading...</div>;
  }

  return (
    <div className="p-4">
      <ToastContainer />
      <div className="flex items-center mb-6">
        <button
          onClick={() => router.push("/admin/categories")}
          className="mr-4 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-1" />
          Back to Categories
        </button>
        <h1 className="text-2xl font-bold">
          {category?.name} - Sub Categories
        </h1>
        <div className="ml-auto">
          <Link
            href="/admin/brands"
            className="inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
          >
            Add New Sub Category
          </Link>
        </div>
      </div>

      {/* Filter Section */}
      <div className="mb-6 border-b pb-4">
        <div className="flex flex-wrap items-center gap-3">
          <span className="text-gray-600 font-medium flex items-center">
            Filters:
          </span>

          <button
            onClick={resetFilters}
            className="text-red-600 hover:text-red-800 text-sm"
          >
            Reset Filters
          </button>

          {/* Material Filter */}
          <div className="relative">
            <select
              value={materialFilter}
              onChange={(e) => setMaterialFilter(e.target.value)}
              className="px-3 py-1.5 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm min-w-[140px]"
            >
              <option value="">All Materials</option>
              {materials.map((material) => (
                <option key={material.id} value={material.id}>
                  {material.name}
                </option>
              ))}
            </select>
          </div>

          {/* Size Filter */}
          <div className="relative">
            <select
              value={sizeFilter}
              onChange={(e) => setSizeFilter(e.target.value)}
              className="px-3 py-1.5 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm min-w-[140px]"
            >
              <option value="">All Sizes</option>
              {sizes.map((size) => (
                <option key={size.id} value={size.id}>
                  {size.name}
                </option>
              ))}
            </select>
          </div>

          {/* Price Range Filter */}
          <div className="relative">
            <button
              onClick={() => setPriceFilterOpen(!priceFilterOpen)}
              className="flex items-center gap-1 px-3 py-1.5 border rounded-md text-sm"
            >
              <span className="text-red-600">₹</span> Price Range
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-4 w-4 ml-1 transition-transform ${
                  priceFilterOpen ? "rotate-180" : ""
                }`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            {priceFilterOpen && (
              <div className="absolute z-20 mt-1 w-[300px] bg-white border rounded-md shadow-lg p-4">
                <h3 className="text-red-700 font-medium mb-3">Set Price Range</h3>
                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <label className="block text-sm text-gray-600 mr-2 w-20">
                      Min Price
                    </label>
                    <div className="relative flex-1">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                        ₹
                      </span>
                      <input
                        type="text"
                        value={tempMinPrice}
                        onChange={(e) => {
                          // Only allow numbers
                          const value = e.target.value.replace(/[^0-9]/g, '');
                          setTempMinPrice(value);
                        }}
                        className="pl-7 pr-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm w-full"
                        placeholder="0"
                      />
                    </div>
                  </div>
                  <div className="flex items-center">
                    <label className="block text-sm text-gray-600 mr-2 w-20">
                      Max Price
                    </label>
                    <div className="relative flex-1">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                        ₹
                      </span>
                      <input
                        type="text"
                        value={tempMaxPrice}
                        onChange={(e) => {
                          // Only allow numbers
                          const value = e.target.value.replace(/[^0-9]/g, '');
                          setTempMaxPrice(value);
                        }}
                        className="pl-7 pr-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm w-full"
                        placeholder="50000"
                      />
                    </div>
                  </div>
                </div>
                <div className="flex justify-between">
                  <button
                    onClick={() => {
                      setTempMinPrice("");
                      setTempMaxPrice("");
                      setMinPrice("");
                      setMaxPrice("");
                      setPriceFilterOpen(false);
                    }}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Clear
                  </button>
                  <button
                    onClick={() => {
                      setMinPrice(tempMinPrice);
                      setMaxPrice(tempMaxPrice);
                      setPriceFilterOpen(false);
                    }}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm"
                  >
                    Apply
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Sort Dropdown */}
          <div className="ml-auto">
            <select
              value={sort}
              onChange={(e) => setSort(e.target.value)}
              className="px-3 py-1.5 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
            >
              <option value="name_asc">Name (A-Z)</option>
              <option value="name_desc">Name (Z-A)</option>
              <option value="price-low">Price (Low to High)</option>
              <option value="price-high">Price (High to Low)</option>
            </select>
          </div>
        </div>

        {/* Active Filters Display */}
        <div className="flex flex-wrap gap-2 mt-3">
          {materialFilter && materials.find(m => m.id === materialFilter) && (
            <div className="flex items-center bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm">
              <span>Material: {materials.find(m => m.id === materialFilter)?.name}</span>
              <button
                className="ml-2 text-blue-500 hover:text-blue-700"
                onClick={() => setMaterialFilter("")}
              >
                &times;
              </button>
            </div>
          )}

          {sizeFilter && sizes.find(s => s.id === sizeFilter) && (
            <div className="flex items-center bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm">
              <span>Size: {sizes.find(s => s.id === sizeFilter)?.name}</span>
              <button
                className="ml-2 text-blue-500 hover:text-blue-700"
                onClick={() => setSizeFilter("")}
              >
                &times;
              </button>
            </div>
          )}

          {(minPrice || maxPrice) && (
            <div className="flex items-center bg-red-50 text-red-700 px-3 py-1 rounded-full text-sm">
              <span>
                Price: ₹{minPrice || "0"} - ₹{maxPrice || "50000"}
              </span>
              <button
                className="ml-2 text-red-500 hover:text-red-700"
                onClick={() => {
                  setMinPrice("");
                  setMaxPrice("");
                  setTempMinPrice("");
                  setTempMaxPrice("");
                }}
              >
                &times;
              </button>
            </div>
          )}
        </div>
      </div>

      {brands.length === 0 ? (
        <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
          <p className="text-gray-500 mb-4">No sub categories found for this category.</p>
          <Link
            href="/admin/brands"
            className="inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
          >
            Add New Sub Category
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {brands.map((brand) => (
            <div
              key={brand.id}
              className="bg-white rounded-lg shadow-sm border overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => router.push(`/admin/categories/${params.id}/brands/${brand.id}/products`)}
            >
              <div className="relative h-48 w-full">
                {brand.imageUrl ? (
                  <Image
                    src={getImageUrl(brand.imageUrl) || "/placeholder.png"}
                    alt={brand.name}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="h-full w-full bg-gray-200 flex items-center justify-center">
                    <span className="text-gray-400">No image</span>
                  </div>
                )}
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-lg mb-1">{brand.name}</h3>
                {brand.price && (
                  <p className="text-gray-700 text-sm">
                    Price: ₹{brand.price}
                  </p>
                )}
                {brand.material && (
                  <p className="text-gray-600 text-sm">
                    Material: {brand.material.name}
                  </p>
                )}
                {brand.size && (
                  <p className="text-gray-600 text-sm">
                    Size: {brand.size.name}
                  </p>
                )}
                <div className="flex justify-end mt-2">
                  <Link
                    href={`/admin/brands/${brand.id}/edit`}
                    className="text-blue-600 hover:text-blue-900 mr-3"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <PencilIcon className="h-5 w-5" />
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CategoryBrandsPage;
