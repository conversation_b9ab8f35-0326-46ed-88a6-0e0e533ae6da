import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

// Update cart item quantity
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const { quantity } = await request.json();
    const itemId = params.id;

    await prisma.cartItem.update({
      where: { id: itemId },
      data: { quantity },
    });

    return NextResponse.json({
      message: "Cart item updated successfully",
      success: true,
    });
  } catch (error) {
    console.error("Error updating cart item:", error);
    return NextResponse.json(
      {
        message: "Failed to update cart item",
        success: false,
      },
      { status: 500 }
    );
  }
}

// Remove item from cart
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const itemId = params.id;

    await prisma.cartItem.delete({
      where: { id: itemId },
    });

    return NextResponse.json({
      message: "Cart item removed successfully",
      success: true,
    });
  } catch (error) {
    console.error("Error removing cart item:", error);
    return NextResponse.json(
      {
        message: "Failed to remove cart item",
        success: false,
      },
      { status: 500 }
    );
  }
}
