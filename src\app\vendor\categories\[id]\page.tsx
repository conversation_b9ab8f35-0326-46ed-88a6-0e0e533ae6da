"use client";
import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function CategoryPage({ params }: { params: { id: string } }) {
  const router = useRouter();

  // Redirect to brands page
  useEffect(() => {
    router.push(`/vendor/categories/${params.id}/brands`);
  }, [params.id, router]);

  // Loading state while redirecting
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh]">
      <div className="relative w-20 h-20">
        <div className="absolute top-0 left-0 right-0 bottom-0 animate-spin rounded-full h-20 w-20 border-4 border-t-blue-500 border-b-blue-700 border-l-transparent border-r-transparent"></div>
        <div className="absolute top-2 left-2 right-2 bottom-2 animate-pulse bg-white rounded-full flex items-center justify-center">
          <div className="h-8 w-8 text-blue-600">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
            </svg>
          </div>
        </div>
      </div>
      <p className="text-gray-600 font-medium mt-4">Redirecting to brands...</p>
    </div>
  );
}
