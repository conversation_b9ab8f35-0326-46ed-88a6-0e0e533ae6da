"use client";
import { useEffect, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { vendorAPI as api } from "@/lib/axios";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Slider from "react-slick";

interface Banner {
  id: string;
  imageUrl: string;
  name: string;
  isActive: boolean;
}

const BannerSlider = () => {
  const [banners, setBanners] = useState<Banner[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchBanners = async () => {
      try {
        setLoading(true);
        const response = await api.get("/banners");
        if (response.data.success) {
          console.log("Banners fetched:", response.data.data);
          setBanners(response.data.data);
        } else {
          setError(true);
        }
      } catch (error) {
        console.error("Error fetching banners:", error);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    fetchBanners();
  }, []);

  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    pauseOnHover: true,
    arrows: false, // Hide navigation arrows
    adaptiveHeight: false,
    fade: true,
  };

  if (loading) {
    return (
      <div className="relative h-[250px] sm:h-[300px] md:h-[400px] lg:h-[500px] bg-gray-200 animate-pulse rounded-lg mb-8 sm:mb-12">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-8 h-8 sm:w-10 sm:h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  if (error || banners.length === 0) {
    // Fallback to a default banner if there's an error or no banners
    return (
      <div className="relative mb-8 sm:mb-12 rounded-lg overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-900/70 to-gray-900/30 z-10"></div>
        <div className="relative h-[250px] sm:h-[300px] md:h-[400px] lg:h-[500px]">
          <Image
            src="https://buymetropolis.com/cdn/shop/files/2_c11d58da-63c3-4d88-a75e-75ee0a0cdaac.png?v=1726814175&width=2000"
            alt="Luxury Home Linen"
            fill
            sizes="(max-width: 640px) 100vw, (max-width: 768px) 100vw, (max-width: 1024px) 100vw, 100vw"
            priority
            className="object-cover"
          />
        </div>
        <div className="absolute inset-0 flex flex-col justify-end z-20 p-4 sm:p-6 md:p-8 lg:p-10 text-white">
          <div className="max-w-xl">
            <button
              onClick={() => router.push('/vendor/products')}
              className="bg-white text-gray-900 px-3 sm:px-4 md:px-6 py-1 sm:py-1.5 md:py-2 rounded-sm font-medium hover:bg-gray-100 transition-colors mt-1 sm:mt-2 text-xs sm:text-sm md:text-base"
            >
              EXPLORE
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="banner-slider-wrapper mb-8 sm:mb-12 rounded-lg overflow-hidden">
      <Slider {...settings}>
        {banners.map((banner) => (
          <div key={banner.id} className="banner-slide-item">
            <div className="banner-content">
              {/* Gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-r from-gray-900/70 to-gray-900/30 z-10"></div>

              {/* Banner image */}
              <div className="relative h-[250px] sm:h-[300px] md:h-[400px] lg:h-[500px]">
                <Image
                  src={banner.imageUrl}
                  alt={banner.name || "Banner image"}
                  fill
                  sizes="(max-width: 640px) 100vw, (max-width: 768px) 100vw, (max-width: 1024px) 100vw, 100vw"
                  priority
                  className="object-cover"
                />
              </div>

              {/* Content overlay */}
              <div className="absolute inset-0 flex flex-col justify-end z-20 p-4 sm:p-6 md:p-8 lg:p-10 text-white">
                <div className="max-w-xl">
                  <button
                    onClick={() => router.push('/vendor/products')}
                    className="bg-white text-gray-900 px-3 sm:px-4 md:px-6 py-1 sm:py-1.5 md:py-2 rounded-sm font-medium hover:bg-gray-100 transition-colors mt-1 sm:mt-2 text-xs sm:text-sm md:text-base"
                  >
                    EXPLORE
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </Slider>

      <style jsx global>{`
        .banner-slider-wrapper {
          position: relative;
          overflow: hidden;
        }

        .banner-slide-item {
          position: relative !important;
          outline: none !important;
        }

        .banner-content {
          position: relative;
          height: 100%;
        }

        /* Fix for slick slider */
        .slick-slider,
        .slick-list,
        .slick-track {
          position: relative;
        }

        .slick-slide {
          float: none !important;
          display: inline-block !important;
        }

        .slick-slide > div {
          line-height: 0;
        }

        /* Navigation dots */
        .slick-dots {
          position: absolute;
          bottom: 10px;
          z-index: 30;
          width: 100%;
          padding: 0;
          margin: 0;
          list-style: none;
          text-align: center;
        }

        .slick-dots li {
          position: relative;
          display: inline-block;
          margin: 0 4px;
          padding: 0;
          cursor: pointer;
        }

        .slick-dots li button {
          font-size: 0;
          line-height: 0;
          display: block;
          width: 12px;
          height: 12px;
          padding: 6px;
          cursor: pointer;
          color: transparent;
          border: 0;
          outline: none;
          background: transparent;
          touch-action: manipulation;
        }

        .slick-dots li button:before {
          font-size: 12px;
          line-height: 12px;
          position: absolute;
          top: 0;
          left: 0;
          width: 12px;
          height: 12px;
          content: '•';
          text-align: center;
          opacity: .5;
          color: white;
        }

        .slick-dots li.slick-active button:before {
          opacity: 1;
          color: white;
          font-size: 14px;
        }

        @media (min-width: 768px) {
          .slick-dots {
            bottom: 20px;
          }

          .slick-dots li {
            margin: 0 5px;
          }
        }

        /* Navigation arrows - hidden */
        .slick-prev,
        .slick-next {
          display: none !important;
        }
      `}</style>
    </div>
  );
};

export default BannerSlider;
