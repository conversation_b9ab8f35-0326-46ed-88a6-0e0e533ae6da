import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

// Interface for creating a payment QR code
interface CreatePaymentQRCodeRequest {
  imageUrl: string;
  name: string;
  isActive?: boolean;
}

// GET all payment QR codes
export async function GET() {
  try {
    console.log("Admin: Fetching all payment QR codes");

    const paymentQRCodes = await prisma.paymentQRCode.findMany({
      orderBy: {
        createdAt: "desc",
      },
    });

    console.log("Admin: Fetched QR codes:", paymentQRCodes);

    return NextResponse.json({
      message: "Payment QR codes fetched successfully",
      success: true,
      data: paymentQRCodes,
    });
  } catch (error) {
    console.error("Error fetching payment QR codes:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch payment QR codes",
        success: false,
        error: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// POST a new payment QR code
export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as CreatePaymentQRCodeRequest;
    const { imageUrl, name, isActive = true } = body;

    // Validate required fields
    if (!imageUrl || !name) {
      return NextResponse.json(
        {
          message: "Image URL and name are required",
          success: false,
        },
        { status: 400 }
      );
    }

    console.log("Admin: Creating new payment QR code", { name, imageUrl, isActive });

    // If this is active, deactivate all other QR codes
    if (isActive) {
      console.log("Admin: Deactivating existing active QR codes");

      await prisma.paymentQRCode.updateMany({
        where: { isActive: true },
        data: { isActive: false },
      });
    }

    // Create the new QR code
    console.log("Admin: Creating new QR code with values:", { imageUrl, name, isActive });

    const paymentQRCode = await prisma.paymentQRCode.create({
      data: {
        imageUrl,
        name,
        isActive,
      },
    });

    console.log("Admin: Created QR code:", paymentQRCode);

    return NextResponse.json({
      message: "Payment QR code created successfully",
      success: true,
      data: paymentQRCode,
    });
  } catch (error) {
    console.error("Payment QR code creation error:", error);
    return NextResponse.json(
      {
        message: "Failed to create payment QR code",
        success: false,
        error: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
