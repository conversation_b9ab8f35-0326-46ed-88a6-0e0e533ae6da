import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

// Get all materials
export async function GET() {
  try {
    const materials = await prisma.material.findMany({
      orderBy: {
        name: "asc",
      },
    });

    return NextResponse.json({
      message: "Materials fetched successfully",
      success: true,
      data: materials,
    });
  } catch (error) {
    console.error("Error fetching materials:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch materials",
        success: false,
      },
      { status: 500 }
    );
  }
}

// Create a new material
interface CreateMaterialRequest {
  name: string;
  isActive?: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as CreateMaterialRequest;
    const { name, isActive = true } = body;

    // Check if material with the same name already exists
    const existingMaterial = await prisma.material.findUnique({
      where: { name },
    });

    if (existingMaterial) {
      return NextResponse.json(
        {
          message: "Material with this name already exists",
          success: false,
        },
        { status: 400 }
      );
    }

    const material = await prisma.material.create({
      data: {
        name,
        isActive,
      },
    });

    return NextResponse.json({
      message: "Material created successfully",
      success: true,
      data: material,
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating material:", error);
    return NextResponse.json(
      {
        message: "Failed to create material",
        success: false,
      },
      { status: 500 }
    );
  }
}
