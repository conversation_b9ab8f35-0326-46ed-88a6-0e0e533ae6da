generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum VendorStatus {
  PENDING
  ACTIVE
  SUSPENDED
  INACTIVE
}

model Vendor {
  id           String       @id @default(cuid())
  email        String       @unique
  businessName String
  taxId        String       @unique // GST/VAT number
  status       VendorStatus @default(PENDING)

  // Business Details
  registrationNumber String? // Company registration number
  yearEstablished    Int?
  annualRevenue      Float?

  // Contact Information
  contactPerson  String
  phoneNumber    String    @unique
  alternatePhone String?

  // Address
  address    String
  street     String
  city       String
  state      String
  postalCode String
  country    String @default("India")

  // Business Documents
  taxCertificate  String? // URL to stored document
  businessLicense String? // URL to stored document
  otherDocuments  Json? // Array of additional document URLs

  // Bank Details
  bankName      String?
  bankAccountNo String?
  ifscCode      String?

  // Verification
  isEmailVerified Boolean @default(false)
  isPhoneVerified Boolean @default(false)
  isDocVerified   Boolean @default(false)

  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  lastLoginAt DateTime?

  // Relations
  orders Order[]    @relation("VendorOrders")
  carts  Cart[]      @relation("VendorCart")
  cartItems CartItem[]

  // Credit fields
  maxCredit     Float   @default(0)
  isCreditGiven Boolean @default(false)

  // Current Dealers - Fix the type and make it optional
  currentDealers String?

  @@index([email])
  @@index([phoneNumber])
  @@index([businessName])
  @@index([taxId])
}

model AdminUser {
  id          String    @id @default(cuid())
  email       String    @unique
  password    String
  firstName   String?
  lastName    String?
  role        String    @default("admin")
  isActive    Boolean   @default(true)
  lastLoginAt DateTime?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Category {
  id          String  @id @default(cuid())
  name        String
  slug        String  @unique
  description String?
  imageUrl    String?
  isActive    Boolean @default(true)

  // Hierarchy
  parentId      String?
  parent        Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  subCategories Category[] @relation("CategoryHierarchy")

  // Metadata
  metaTitle String?
  metaDesc  String?

  // Relations
  products     Product[]
  attributes   CategoryAttribute[]
  priceMasters PriceMaster[]

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([slug])
  @@index([parentId])
}

model CategoryAttribute {
  id         String  @id @default(cuid())
  name       String
  type       String // text, number, boolean, select
  options    Json? // For select type attributes
  isRequired Boolean @default(false)

  // Relations
  categoryId String
  category   Category                @relation(fields: [categoryId], references: [id])
  values     ProductAttributeValue[]

  @@unique([categoryId, name])
}

model Brand {
  id              String    @id @default(cuid())
  name            String    @unique
  description     String?
  imageUrl        String?
  imageAspectRatio String    @default("1:1") // Can be "1:1" or "9:16"
  price           Float?
  materialId      String?
  sizeId          String?
  isActive        Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  products        Product[]
  priceMasters    PriceMaster[]

  // Relations

  @@index([materialId])
  @@index([sizeId])
}

model Material {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]
  priceMasters PriceMaster[]
}

model Size {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]
  priceMasters PriceMaster[]
}

model PriceMaster {
  id          String   @id @default(cuid())
  price       Float
  categoryId  String
  brandId     String
  materialId  String
  sizeId      String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  category    Category @relation(fields: [categoryId], references: [id])
  brand       Brand    @relation(fields: [brandId], references: [id])
  material    Material @relation(fields: [materialId], references: [id])
  size        Size     @relation(fields: [sizeId], references: [id])

  @@unique([categoryId, brandId, materialId, sizeId])
  @@index([categoryId])
  @@index([brandId])
  @@index([materialId])
  @@index([sizeId])
}

model Product {
  id               String   @id @default(cuid())
  name             String
  slug             String   @unique
  description      String
  basePrice        Float
  gstPercentage    Float    @default(18) // Default GST rate of 18%
  bulkPricing      Json?
  maxOrderQuantity Int?
  packagingSize    Int      @default(1)
  packagingUnit    String   @default("piece")
  isActive         Boolean  @default(true)
  isPublished      Boolean  @default(false)
  images           String[]
  imageAspectRatio String    @default("9:16") // Can be "1:1" or "9:16"
  categoryId       String
  brandId          String?
  materialId       String?
  sizeId           String?

  // Relations
  category   Category                @relation(fields: [categoryId], references: [id])
  brand      Brand?                  @relation(fields: [brandId], references: [id])
  material   Material?               @relation(fields: [materialId], references: [id])
  size       Size?                   @relation(fields: [sizeId], references: [id])
  cartItems  CartItem[]
  orderItems OrderItem[]
  attributes ProductAttributeValue[]

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([categoryId])
  @@index([brandId])
  @@index([materialId])
  @@index([sizeId])
}

model ProductAttributeValue {
  id    String @id @default(cuid())
  value String

  // Relations
  productId   String
  product     Product           @relation(fields: [productId], references: [id])
  attributeId String
  attribute   CategoryAttribute @relation(fields: [attributeId], references: [id])

  @@unique([productId, attributeId])
}

model Cart {
  id        String     @id @default(cuid())
  vendorId  String
  vendor    Vendor     @relation("VendorCart", fields: [vendorId], references: [id])
  items     CartItem[]
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  @@index([vendorId])
}

model CartItem {
  id        String   @id @default(cuid())
  cartId    String
  cart      Cart     @relation(fields: [cartId], references: [id])
  productId String
  product   Product  @relation(fields: [productId], references: [id])
  vendorId  String
  vendor    Vendor   @relation(fields: [vendorId], references: [id])
  quantity  Int
}

model Order {
  id                String      @id @default(cuid())
  vendorId          String
  totalAmount       Float
  status            OrderStatus @default(PENDING)
  shippingAddress   String
  choiceOfTransport String
  paymentMethod     String
  paymentReferenceId String?    // Added for online payment reference
  items             OrderItem[]
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt
  vendor            Vendor      @relation("VendorOrders", fields: [vendorId], references: [id])

  @@index([vendorId])
}

model OrderItem {
  id        String   @id @default(cuid())
  orderId   String
  productId String
  quantity  Int
  price     Float
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id])
}

enum OrderStatus {
  PENDING
  APPROVED
  REJECTED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}


enum UserRole {
  ADMIN
  VENDOR
  USER
}

model Otp {
  id        String   @id @default(cuid())
  email     String
  code      String
  expiresAt DateTime
  isUsed    Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email])
  @@index([code])
}

model VendorBanner {
  id          String   @id @default(cuid())
  imageUrl    String
  name        String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model SubBanner {
  id          String   @id @default(cuid())
  imageUrl    String
  name        String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model PaymentQRCode {
  id             String   @id @default(cuid())
  imageUrl       String
  name           String
  isActive       Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
}

enum NotificationType {
  VENDOR_REGISTRATION
  ORDER_CREATED
  ORDER_STATUS_CHANGED
  ACCOUNT_STATUS_CHANGED
  PAYMENT_RECEIVED
  GENERAL
}

model Notification {
  id          String           @id @default(cuid())
  title       String
  body        String
  type        NotificationType @default(GENERAL)
  isRead      Boolean          @default(false)
  data        Json?            // Additional data related to the notification
  recipientId String?          // ID of the recipient (vendor or admin)
  recipientType String         // "vendor" or "admin"
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  @@index([recipientId])
  @@index([type])
  @@index([isRead])
}

model FCMToken {
  id          String   @id @default(cuid())
  token       String   @unique
  userId      String   // ID of the user (vendor or admin)
  userType    String   // "vendor" or "admin"
  deviceInfo  String?  // Information about the device
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([userId])
  @@index([userType])
}
