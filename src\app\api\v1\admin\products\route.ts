// get all products

import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const category = searchParams.get("category");
    const brand = searchParams.get("brand");
    const search = searchParams.get("search");
    const sort = searchParams.get("sort") || "newest";
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;

    let where: any = {};

    if (category) {
      where.categoryId = category;
    }

    if (brand) {
      where.brandId = brand;
    }

    // Add search functionality
    if (search && search.trim() !== "") {
      where.OR = [
        {
          name: {
            contains: search,
            mode: "insensitive", // Case-insensitive search
          },
        },
        {
          description: {
            contains: search,
            mode: "insensitive", // Case-insensitive search
          },
        },
      ];
    }

    let orderBy: any = {};
    if (sort === "newest") {
      orderBy.createdAt = "desc";
    } else if (sort === "price-low") {
      orderBy.basePrice = "asc";
    } else if (sort === "price-high") {
      orderBy.basePrice = "desc";
    }

    // Get total count for pagination
    const totalProducts = await prisma.product.count({ where });

    const products = await prisma.product.findMany({
      where,
      orderBy,
      skip,
      take: limit,
      include: {
        category: {
          select: {
            name: true,
          },
        },
        brand: {
          select: {
            name: true,
          },
        },
      },
    });

    const totalPages = Math.ceil(totalProducts / limit);

    return NextResponse.json(
      {
        message: "All Products",
        success: true,
        data: products,
        pagination: {
          total: totalProducts,
          page,
          limit,
          totalPages
        }
      },
      {
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json({
      message: "Error fetching products",
      success: false,
      error: error,
    });
  }
}

// add products
interface CreateProductRequest {
  name: string;
  description: string;
  slug?: string;
  basePrice: number;
  gstPercentage: number;
  maxOrderQuantity?: number;
  images: string[];
  categoryId: string;
  brandId: string;
  materialId: string;
  sizeId: string;
  isActive: boolean;
  isPublished?: boolean;
  packagingSize?: number;
  packagingUnit?: string;
  imageAspectRatio?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body = (await request.json()) as CreateProductRequest;
    const {
      name,
      description,
      slug: providedSlug,
      basePrice,
      gstPercentage,
      packagingSize,
      packagingUnit,
      images,
      categoryId,
      brandId,
      materialId,
      sizeId,
      isActive = true,
      isPublished = false,
    } = body;

    // Validate required fields
    if (!name || !description || !basePrice || !categoryId || !brandId || !materialId || !sizeId) {
      return NextResponse.json(
        {
          message: "Missing required fields",
          success: false,
        },
        { status: 400 }
      );
    }

    // Use provided slug or generate one if not provided
    let slug = providedSlug;

    if (!slug) {
      try {
        // Fetch category, brand, and material names for slug generation
        const category = await prisma.category.findUnique({ where: { id: categoryId } });
        const brand = await prisma.brand.findUnique({ where: { id: brandId } });
        const material = await prisma.material.findUnique({ where: { id: materialId } });

        // Generate slug from category, brand, material, and product name
        slug = `${category?.name || ''}-${brand?.name || ''}-${material?.name || ''}-${name}`
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '');
      } catch (error) {
        console.error("Error generating slug with related entities:", error);
        // Fallback to simple name-based slug
        slug = name
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, "-")
          .replace(/(^-|-$)/g, "");
      }
    }

    // Price Master functionality has been removed

    // IMPORTANT: Log to see if images array is getting corrupted
    console.log("Received images array:", images);

    // Make sure images is always an array, even if it's empty
    const imageArray = Array.isArray(images) ? images : [];

    // IMPORTANT: Create clean string arrays
    const cleanImageArray = imageArray.map((img) => {
      // If for some reason the image is already a string with quotes or JSON, clean it
      if (typeof img === "string") {
        // Remove any quotes that might be in the string
        return img.replace(/^"|"$/g, "").replace(/\\"/g, '"');
      }
      return img;
    });

    console.log("Cleaned images array:", cleanImageArray);

    const product = await prisma.product.create({
      data: {
        name,
        slug,
        description,
        basePrice,
        gstPercentage: gstPercentage || 18, // Default to 18% if not provided
        packagingSize: packagingSize || 10, // Default to 10 if not provided
        packagingUnit: packagingUnit || "piece", // Default to "piece" if not provided
        images: cleanImageArray || [],
        imageAspectRatio: body.imageAspectRatio || "9:16", // Use provided aspect ratio or default to 9:16
        isActive: isActive,
        isPublished: isPublished,
        categoryId,
        brandId,
        materialId,
        sizeId,
      },
    });

    return NextResponse.json({
      message: "Product created successfully",
      success: true,
      data: product,
    });
  } catch (error) {
    console.error("Product creation error:", error);
    return NextResponse.json(
      {
        message: "Failed to create product",
        success: false,
      },
      { status: 500 }
    );
  }
}
