import { prisma } from './prisma';
import { Prisma } from '@prisma/client';
import {
  sendNotificationToDevice,
  sendNotificationToDevices
} from './firebase-admin';
import { Notification, NotificationType } from '@prisma/client';

/**
 * Create a notification in the database
 * @param title Notification title
 * @param body Notification body
 * @param type Notification type
 * @param recipientId ID of the recipient (vendor or admin)
 * @param recipientType Type of recipient ('vendor' or 'admin')
 * @param data Additional data to store with the notification
 * @returns Created notification
 */
export async function createNotification(
  title: string,
  body: string,
  type: NotificationType,
  recipientId: string | null,
  recipientType: 'vendor' | 'admin',
  data?: Prisma.InputJsonValue
): Promise<Notification> {
  try {
    const notification = await prisma.notification.create({
      data: {
        title,
        body,
        type,
        recipientId,
        recipientType,
        data: data || {},
        isRead: false,
      },
    });

    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
}

/**
 * Send a push notification to a specific user
 * @param userId ID of the user (vendor or admin)
 * @param userType Type of user ('vendor' or 'admin')
 * @param title Notification title
 * @param body Notification body
 * @param data Additional data to send with the notification
 * @returns Result of the notification sending
 */
export async function sendPushNotification(
  userId: string,
  userType: 'vendor' | 'admin',
  title: string,
  body: string,
  data?: Record<string, any>
): Promise<{ success: boolean; message?: string; messageId?: string; error?: any; successCount?: number; failureCount?: number }> {
  try {
    // Find all active FCM tokens for this user
    const fcmTokens = await prisma.fCMToken.findMany({
      where: {
        userId,
        userType,
        isActive: true,
      },
    });

    if (!fcmTokens || fcmTokens.length === 0) {
      console.log(`No active FCM tokens found for ${userType} with ID ${userId}`);
      return { success: false, message: 'No active FCM tokens found' };
    }

    // Extract tokens
    const tokens = fcmTokens.map((token: { token: string }) => token.token);

    // Convert all data values to strings as required by Firebase
    const stringifiedData: Record<string, string> = {};
    if (data) {
      Object.keys(data).forEach(key => {
        // Convert any non-string values to strings
        stringifiedData[key] = typeof data[key] === 'string'
          ? data[key]
          : JSON.stringify(data[key]);
      });
    }

    // Send notification to all devices
    if (tokens.length === 1) {
      const result = await sendNotificationToDevice(tokens[0], title, body, stringifiedData);
      // Ensure we return a consistent response format
      return {
        success: result.success,
        message: result.success ? 'Notification sent successfully' : 'Failed to send notification',
        messageId: result.messageId,
        error: result.error
      };
    } else {
      const result = await sendNotificationToDevices(tokens, title, body, stringifiedData);
      // Ensure we return a consistent response format
      return {
        success: result.success,
        message: result.success ? `Notification sent to ${result.successCount} devices` : 'Failed to send notifications',
        successCount: result.successCount,
        failureCount: result.failureCount,
        error: result.error
      };
    }
  } catch (error) {
    console.error('Error sending push notification:', error);
    return { success: false, message: 'Error sending push notification', error };
  }
}

/**
 * Create and send a notification to a user
 * @param title Notification title
 * @param body Notification body
 * @param type Notification type
 * @param recipientId ID of the recipient (vendor or admin)
 * @param recipientType Type of recipient ('vendor' or 'admin')
 * @param data Additional data for the notification
 * @returns Created notification and push result
 */
export async function createAndSendNotification(
  title: string,
  body: string,
  type: NotificationType,
  recipientId: string | null,
  recipientType: 'vendor' | 'admin',
  data?: Prisma.InputJsonValue
) {
  try {
    // Create notification in database
    const notification = await createNotification(
      title,
      body,
      type,
      recipientId,
      recipientType,
      data
    );

    // Only send push notification if we have a recipient ID
    let pushResult: { success: boolean; message: string; [key: string]: any } = {
      success: false,
      message: 'No recipient ID provided'
    };

    if (recipientId) {
      const result = await sendPushNotification(
        recipientId,
        recipientType,
        title,
        body,
        {
          notificationId: notification.id,
          type: type as unknown as string,
          ...(typeof data === 'object' && data !== null ? data : {}),
        }
      );

      // Ensure pushResult always has a message property
      pushResult = {
        ...result,
        message: result.message || (result.success ? 'Notification sent successfully' : 'Failed to send notification')
      };
    }

    return {
      notification,
      pushResult,
    };
  } catch (error) {
    console.error('Error creating and sending notification:', error);
    throw error;
  }
}

/**
 * Send notification to all admins
 * @param title Notification title
 * @param body Notification body
 * @param type Notification type
 * @param data Additional data for the notification
 * @returns Array of notification results
 */
export async function notifyAllAdmins(
  title: string,
  body: string,
  type: NotificationType,
  data?: Prisma.InputJsonValue
) {
  try {
    // Get all admin users
    const admins = await prisma.adminUser.findMany({
      where: {
        isActive: true,
      },
    });

    const results = [];

    // Create notifications for each admin
    for (const admin of admins) {
      const result = await createAndSendNotification(
        title,
        body,
        type,
        admin.id,
        'admin',
        data
      );
      results.push(result);
    }

    return results;
  } catch (error) {
    console.error('Error notifying all admins:', error);
    throw error;
  }
}

/**
 * Mark a notification as read
 * @param notificationId ID of the notification to mark as read
 * @returns Updated notification
 */
export async function markNotificationAsRead(notificationId: string) {
  try {
    const notification = await prisma.notification.update({
      where: { id: notificationId },
      data: { isRead: true },
    });
    return notification;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
}
