"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { toast } from "react-toastify";
import { adminAPI as api } from "@/lib/axios";
import Modal from "./Modal";
import slugify from "slugify";
import ImageUploaderNoCrop from "./ImageUploaderNoCrop";

// Define the schema for category creation
const categorySchema = yup.object({
  name: yup.string().required("Category name is required"),
  isActive: yup.boolean(),
});

type CategoryFormData = yup.InferType<typeof categorySchema>;

interface AddCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (category: any) => void;
}

const AddCategoryModal: React.FC<AddCategoryModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CategoryFormData>({
    resolver: yupResolver(categorySchema),
    defaultValues: {
      isActive: true,
    },
  });

  const handleImagesUploaded = (imageUrls: string[]) => {
    setUploadedImages(imageUrls);
  };

  const onSubmit = async (data: CategoryFormData) => {
    try {
      if (uploadedImages.length === 0) {
        toast.error("Please upload an image for the category");
        return;
      }

      setIsSubmitting(true);

      // Generate slug from name
      const slug = slugify(data.name, { lower: true, strict: true });

      const response = await api.post("/categories", {
        ...data,
        slug,
        imageUrl: uploadedImages[0], // Use the first uploaded image
      });

      if (response.data.success) {
        toast.success("Category created successfully");
        onSuccess(response.data.data);
        reset();
        setUploadedImages([]);
        onClose();
      } else {
        toast.error(response.data.message || "Failed to create category");
      }
    } catch (error: any) {
      console.error("Error creating category:", error);
      toast.error(error.response?.data?.message || "Failed to create category");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Add New Category" size="md">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Category Name <span className="text-red-500">*</span>
          </label>
          <input
            {...register("name")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="Enter category name"
          />
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Category Image <span className="text-red-500">*</span>
          </label>
          <ImageUploaderNoCrop
            onImagesUploaded={handleImagesUploaded}
            multiple={false}
            maxFiles={1}
            existingImages={uploadedImages}
          />
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="isActive"
            {...register("isActive")}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
            Active
          </label>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={() => {
              reset();
              setUploadedImages([]);
              onClose();
            }}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Creating..." : "Create Category"}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default AddCategoryModal;
