// Install first:
// npm install react-easy-crop

import React, { useState, useCallback } from "react";
import { useDropzone, FileWithPath } from "react-dropzone";
import <PERSON><PERSON><PERSON> from "react-easy-crop";
import { Point, Area } from "react-easy-crop";
import axios from "axios";
import { toast } from "react-toastify";

interface ImageUploaderProps {
  // eslint-disable-next-line no-unused-vars
  onImagesUploaded: (urls: string[]) => void;
  existingImages?: string[];
  multiple?: boolean;
  maxFiles?: number;
}

interface ImageToProcess {
  file: File;
  preview: string;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  onImagesUploaded,
  existingImages = [],
  multiple = true,
  maxFiles = 5,
}) => {
  const [uploadedImages, setUploadedImages] =
    useState<string[]>(existingImages);
  const [currentImage, setCurrentImage] = useState<ImageToProcess | null>(null);
  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 });
  const [zoom, setZoom] = useState<number>(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);
  const [showCropper, setShowCropper] = useState<boolean>(false);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [pendingImages, setPendingImages] = useState<File[]>([]);

  const onCropComplete = useCallback(
    (croppedArea: Area, croppedAreaPixels: Area) => {
      setCroppedAreaPixels(croppedAreaPixels);
    },
    []
  );

  const onDrop = useCallback(
    (acceptedFiles: FileWithPath[]) => {
      if (!multiple && acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        setCurrentImage({
          file,
          preview: URL.createObjectURL(file),
        });
        setShowCropper(true);
      } else if (multiple) {
        if (uploadedImages.length + acceptedFiles.length > maxFiles) {
          toast.error(`You can upload a maximum of ${maxFiles} images`);
          return;
        }

        if (acceptedFiles.length > 0) {
          const firstFile = acceptedFiles[0];
          const restFiles = acceptedFiles.slice(1);

          setPendingImages([...pendingImages, ...restFiles]);
          setCurrentImage({
            file: firstFile,
            preview: URL.createObjectURL(firstFile),
          });
          setShowCropper(true);
        }
      }
    },
    [multiple, maxFiles, uploadedImages.length, pendingImages]
  );

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".webp"],
    },
    maxSize: 10485760,
    disabled: isUploading,
  });

  // Helper function to create an image
  const createImage = (url: string): Promise<HTMLImageElement> =>
    new Promise((resolve, reject) => {
      const image = new Image();
      image.addEventListener("load", () => resolve(image));
      image.addEventListener("error", (error) => reject(error));
      image.src = url;
    });

  // This function creates a cropped image from the selected area
  const createCroppedImage = async (
    imageSrc: string,
    pixelCrop: Area
  ): Promise<File> => {
    const image = await createImage(imageSrc);
    const canvas = document.createElement("canvas");
    canvas.width = pixelCrop.width;
    canvas.height = pixelCrop.height;
    const ctx = canvas.getContext("2d");

    if (!ctx) {
      throw new Error("No 2d context");
    }

    ctx.fillStyle = "white";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    ctx.drawImage(
      image,
      pixelCrop.x,
      pixelCrop.y,
      pixelCrop.width,
      pixelCrop.height,
      0,
      0,
      pixelCrop.width,
      pixelCrop.height
    );

    return new Promise((resolve, reject) => {
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error("Canvas is empty"));
            return;
          }

          const file = new File([blob], "cropped-image.jpg", {
            type: "image/jpeg",
            lastModified: Date.now(),
          });
          resolve(file);
        },
        "image/jpeg",
        0.95
      );
    });
  };

  const processCrop = async (): Promise<void> => {
    if (!croppedAreaPixels || !currentImage) {
      toast.error("Please select a crop area first");
      return;
    }

    try {
      setIsUploading(true);

      const croppedFile = await createCroppedImage(
        currentImage.preview,
        croppedAreaPixels
      );

      const formData = new FormData();
      formData.append("file", croppedFile);

      const response = await axios.post("/api/v1/admin/upload", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (response.data.success) {
        const imagePath = response.data.path as string;

        const newImages = [...uploadedImages, imagePath];
        setUploadedImages(newImages);
        onImagesUploaded(newImages);

        if (pendingImages.length > 0) {
          const nextFile = pendingImages[0];
          setPendingImages(pendingImages.slice(1));
          setCurrentImage({
            file: nextFile,
            preview: URL.createObjectURL(nextFile),
          });
          setCrop({ x: 0, y: 0 });
          setZoom(1);
        } else {
          setShowCropper(false);
          setCurrentImage(null);
        }
      } else {
        toast.error("Failed to upload image");
      }
    } catch (error) {
      console.error("Error:", error);
      toast.error(
        `Failed to process image: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    } finally {
      setIsUploading(false);
    }
  };

  // Cancel cropping
  const cancelCrop = (): void => {
    if (currentImage) {
      URL.revokeObjectURL(currentImage.preview);
    }
    setShowCropper(false);
    setCurrentImage(null);

    if (pendingImages.length > 0) {
      const nextFile = pendingImages[0];
      setPendingImages(pendingImages.slice(1));
      setCurrentImage({
        file: nextFile,
        preview: URL.createObjectURL(nextFile),
      });
      setShowCropper(true);
      setCrop({ x: 0, y: 0 });
      setZoom(1);
    }
  };

  // Remove uploaded image
  const removeImage = (index: number): void => {
    const newImages = [...uploadedImages];
    newImages.splice(index, 1);
    setUploadedImages(newImages);
    onImagesUploaded(newImages);
  };

  return (
    <div className="image-uploader">
      {/* Dropzone area */}
      <div
        {...getRootProps()}
        className={`mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md cursor-pointer ${isUploading ? "opacity-50 cursor-not-allowed" : ""}`}
      >
        <div className="space-y-1 text-center">
          <input {...getInputProps()} />
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            stroke="currentColor"
            fill="none"
            viewBox="0 0 48 48"
          >
            <path
              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <div className="flex text-sm text-gray-600 justify-center">
            <label className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
              <span>Upload images</span>
            </label>
            <p className="pl-1">or drag and drop</p>
          </div>
          <p className="text-xs text-gray-500">
            PNG, JPG, JPEG up to 10MB {isUploading && "(Uploading...)"}
          </p>
          {multiple && (
            <p className="text-xs text-gray-500">
              {uploadedImages.length} of {maxFiles} images uploaded
            </p>
          )}
        </div>
      </div>

      {/* Cropper modal */}
      {showCropper && currentImage && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-3xl w-full max-h-[90vh] overflow-auto">
            <h3 className="text-lg font-medium mb-4">Crop Image</h3>
            <div
              style={{ position: "relative", height: "400px", width: "100%" }}
            >
              <Cropper
                image={currentImage.preview}
                crop={crop}
                zoom={zoom}
                aspect={1}
                onCropChange={setCrop}
                onCropComplete={onCropComplete}
                onZoomChange={setZoom}
              />
            </div>
            <div className="flex justify-end space-x-3 mt-4">
              <button
                type="button"
                onClick={cancelCrop}
                className="px-4 py-2 bg-gray-300 rounded-md hover:bg-gray-400"
                disabled={isUploading}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={processCrop}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300"
                disabled={isUploading}
              >
                {isUploading ? "Processing..." : "Apply & Upload"}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Image previews */}
      {uploadedImages.length > 0 && (
        <div className="mt-4 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
          {uploadedImages.map((image, index) => (
            <div key={index} className="relative">
              <img
                src={image}
                alt={`Uploaded ${index + 1}`}
                className="h-24 w-full object-cover rounded-md"
              />
              <button
                type="button"
                className="absolute -top-2 -right-2 rounded-full bg-red-500 text-white p-1 w-6 h-6 flex items-center justify-center"
                onClick={() => removeImage(index)}
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
