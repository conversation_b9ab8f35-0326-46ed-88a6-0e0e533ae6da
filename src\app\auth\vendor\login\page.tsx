
"use client";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import api from "@/lib/axios";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";

const emailSchema = yup.object({
  identifier: yup
    .string()
    .required("Email or Phone number is required")
    .test("identifier", "Enter valid email or 10-digit phone number", (value) => {
      if (!value) return false;
      // Check if it's a valid email
      const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
      // Check if it's a valid 10-digit phone number
      const phoneRegex = /^[0-9]{10}$/;
      return emailRegex.test(value) || phoneRegex.test(value);
    }),
});

const otpSchema = yup.object({
  otp: yup
    .string()
    .required("OTP is required")
    .length(4, "OTP must be 4 digits"),
});

type EmailFormData = yup.InferType<typeof emailSchema>;
type OtpFormData = yup.InferType<typeof otpSchema>;

export default function VendorLoginPage() {
  const router = useRouter();
  const [error, setError] = useState("");
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [identifier, setIdentifier] = useState("");
  const [identifierType, setIdentifierType] = useState<"email" | "mobile">("mobile");
  const [resendTimer, setResendTimer] = useState(0);

  // Helper function to determine if an identifier is an email
  const isEmailIdentifier = (value: string): boolean => {
    return value.includes('@');
  };

  // Add useEffect for countdown timer
  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setInterval(() => {
        setResendTimer((prev) => prev - 1);
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [resendTimer]);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<EmailFormData>({
    resolver: yupResolver(emailSchema),
  });



  const {
    register: otpRegister,
    handleSubmit: handleOtpSubmit,
    formState: { errors: otpErrors, isSubmitting: otpIsSubmitting },
  } = useForm<OtpFormData>({
    resolver: yupResolver(otpSchema),
  });

  // Handle identifier submission
  const handleEmailSubmit = async (data: EmailFormData) => {
    try {
      const { identifier } = data;
      setIdentifier(identifier);

      // Determine if the identifier is an email or phone number
      setIdentifierType(isEmailIdentifier(identifier) ? "email" : "mobile");

      const response = await api.post("/auth/vendor/login/otpsend", { identifier });
      if (response.data.success) {
        setIsOtpSent(true);
        setResendTimer(30);

        // Show OTP in toast if available in response
        if (response.data.data?.otp) {
          toast.success(`OTP: ${response.data.data.otp}`);
        } else {
          toast.success("OTP sent successfully");
        }
      } else {
        // Check if account is pending approval
        if (response.data.data?.status === "PENDING" && response.data.data?.redirectUrl) {
          // Redirect to pending approval page with email using router
          const redirectUrl = `${response.data.data.redirectUrl}?email=${encodeURIComponent(response.data.data.email)}`;
          router.push(redirectUrl);
        } else {
          setError(response.data.message);
          toast.error(response.data.message || "Error sending OTP");
        }
      }
    } catch (err: any) {
      // Check if the error response contains pending approval data
      if (err.response?.status === 403 && err.response?.data?.data?.status === "PENDING") {
        const redirectUrl = `${err.response.data.data.redirectUrl}?email=${encodeURIComponent(err.response.data.data.email)}`;
        router.push(redirectUrl);
      } else {
        setError(err.response?.data?.message || "Something went wrong");
        toast.error(err.response?.data?.message || "Something went wrong");
      }
    }
  };

  // Add resend OTP handler
  const handleResendOtp = async () => {
    if (resendTimer > 0) return;

    // Ensure the identifier type is still set correctly
    setIdentifierType(isEmailIdentifier(identifier) ? "email" : "mobile");

    try {
      const response = await api.post("/auth/vendor/login/otpsend", { identifier });
      if (response.data.success) {
        setResendTimer(30);

        // Show OTP in toast if available in response
        if (response.data.data?.otp) {
          toast.success(`OTP: ${response.data.data.otp}`);
        } else {
          toast.success("New OTP sent to your email");
        }
      } else {
        // Check if account is pending approval
        if (response.data.data?.status === "PENDING" && response.data.data?.redirectUrl) {
          // Redirect to pending approval page with email using router
          const redirectUrl = `${response.data.data.redirectUrl}?email=${encodeURIComponent(response.data.data.email)}`;
          router.push(redirectUrl);
        } else {
          toast.error(response.data.message || "Error sending OTP");
        }
      }
    } catch (err: any) {
      // Check if the error response contains pending approval data
      if (err.response?.status === 403 && err.response?.data?.data?.status === "PENDING") {
        const redirectUrl = `${err.response.data.data.redirectUrl}?email=${encodeURIComponent(err.response.data.data.email)}`;
        router.push(redirectUrl);
      } else {
        toast.error(err.response?.data?.message || "Failed to resend OTP");
      }
    }
  };

  // Handle OTP submission
  const handleOtpSubmitAction = async (data: OtpFormData) => {
    try {
      const response = await api.post("/auth/vendor/login/verify-otp", {
        identifier,
        otp: data.otp,
      });

      if (response.data.success) {
        toast.success("Login successful", {
          onClose: () => {
            // Use Next.js router for navigation instead of window.location
            router.push(response.data.data.redirectUrl || "/vendor/dashboard");
          },
        });
      } else {
        // Check if account is pending approval
        if (response.data.data?.status === "PENDING" && response.data.data?.redirectUrl) {
          // Redirect to pending approval page with email using router
          const redirectUrl = `${response.data.data.redirectUrl}?email=${encodeURIComponent(response.data.data.email)}`;
          router.push(redirectUrl);
        } else {
          setError(response.data.message);
          toast.error(response.data.message || "OTP verification failed");
        }
      }
    } catch (err: any) {
      // Check if the error response contains pending approval data
      if (err.response?.status === 403 && err.response?.data?.data?.status === "PENDING") {
        const redirectUrl = `${err.response.data.data.redirectUrl}?email=${encodeURIComponent(err.response.data.data.email)}`;
        router.push(redirectUrl);
      } else {
        setError(err.response?.data?.message || "Something went wrong");
        toast.error(err.response?.data?.message || "Something went wrong");
      }
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <ToastContainer />
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="flex justify-center">
            <Image
              src="https://buymetropolis.com/cdn/shop/files/Metropolis_logo_-_transparent.png?v=1726657630&width=280"
              alt="Metropolis"
              width={200}
              height={60}
              priority
            />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Dealer Login
          </h2>
        </div>

        {!isOtpSent ? (
          // Email form
          <form
            className="mt-8 space-y-6"
            onSubmit={handleSubmit(handleEmailSubmit)}
          >
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
                {error}
              </div>
            )}
            <div className="rounded-md shadow-sm space-y-4">
              <div>
                <label
                  htmlFor="identifier"
                  className="block text-sm font-medium mb-1"
                >
                  Email or Phone Number
                </label>
                <input
                  {...register("identifier")}
                  type="text"
                  className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                  placeholder="Enter email or phone number"
                />
                {errors.identifier && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors.identifier.message}
                  </p>
                )}
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isSubmitting}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {isSubmitting ? "Sending OTP..." : "Send OTP"}
              </button>
            </div>
          </form>
        ) : (
          // OTP form
          <form
            className="mt-8 space-y-6"
            onSubmit={handleOtpSubmit(handleOtpSubmitAction)}
          >
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
                {error}
              </div>
            )}
            <div className="rounded-md shadow-sm space-y-4">
              <div>
                <label htmlFor="identifier" className="block text-sm font-medium mb-1">
                  {identifierType === "email" ? "Email" : "Mobile Number"}
                </label>
                <input
                  type="text"
                  value={identifier}
                  disabled
                  className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                />
              </div>
              <div>
                <label htmlFor="otp" className="block text-sm font-medium mb-1">
                  OTP
                </label>
                <input
                  {...otpRegister("otp")}
                  type="text"
                  className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                  placeholder="Enter OTP"
                />
                {otpErrors.otp && (
                  <p className="text-red-500 text-sm mt-1">
                    {otpErrors.otp.message}
                  </p>
                )}
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={otpIsSubmitting}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {otpIsSubmitting ? "Verifying..." : "Verify OTP"}
              </button>
            </div>
            <div className="text-sm text-center">
              {resendTimer > 0 ? (
                <span className="text-gray-500">
                  Resend OTP in {resendTimer} seconds
                </span>
              ) : (
                <button
                  onClick={handleResendOtp}
                  type="button"
                  className="font-medium text-blue-600 hover:text-blue-500"
                >
                  Resend OTP
                </button>
              )}
            </div>
          </form>
        )}

        <div className="text-center mt-4">
          <Link
            href="/auth/vendor/register"
            className="text-blue-600 hover:text-blue-500"
          >
            Don't have an account? Sign Up
          </Link>
        </div>
      </div>
    </div>
  );
}

