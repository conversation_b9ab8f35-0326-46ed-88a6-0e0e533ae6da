"use client";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { adminAPI as api } from "@/lib/axios";
import { toast } from "react-toastify";
import BannerImageUploader from "./BannerImageUploader";
import { X } from "lucide-react";

interface AddBannerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface FormData {
  name: string;
  isActive: boolean;
}

export default function AddBannerModal({
  isOpen,
  onClose,
  onSuccess,
}: AddBannerModalProps) {
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      name: "",
      isActive: true,
    },
  });

  const handleImagesUploaded = (urls: string[]) => {
    setUploadedImages(urls);
  };

  const onSubmit = async (data: FormData) => {
    if (uploadedImages.length === 0) {
      toast.error("Please upload an image for the banner");
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await api.post("/banners", {
        name: data.name,
        imageUrl: uploadedImages[0],
        isActive: data.isActive,
      });

      if (response.data.success) {
        reset();
        setUploadedImages([]);
        onSuccess();
      } else {
        toast.error("Failed to create banner");
      }
    } catch (error) {
      console.error("Error creating banner:", error);
      toast.error("Failed to create banner");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    setUploadedImages([]);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">Add New Banner</h2>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="p-4 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Banner Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              {...register("name", { required: "Banner name is required" })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isSubmitting}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Banner Image <span className="text-red-500">*</span>
            </label>
            <BannerImageUploader
              onImagesUploaded={handleImagesUploaded}
              multiple={false}
              maxFiles={1}
              existingImages={uploadedImages}
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="isActive"
              {...register("isActive")}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
              Active
            </label>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save Banner"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
