
"use client";

import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { adminAPI as api } from "@/lib/axios"; // Import the admin API instance

const vendorSchema = yup.object({
  phoneNumber: yup
    .string()
    .matches(/^[0-9]{10}$/, "Invalid phone number")
    .required("Phone number is required"),
  taxId: yup
    .string()
    .min(15, "GST Number must be of 15 digits")
    .required("GST Number is required"),
  businessName: yup.string().required("Business name is required"),
  email: yup.string().email("Invalid email").required("Email is required"),
  contactPerson: yup.string().required("Contact person name is required"),
  address: yup.string().required("Address is required"),
  street: yup.string().required("Street is required"),
  city: yup.string().required("City is required"),
  state: yup.string().required("State is required"),
  postalCode: yup
    .string()
    .matches(/^[0-9]{6}$/, "Invalid postal code")
    .required("Postal code is required"),
  country: yup.string().default("India"),
});

type VendorFormData = {
  phoneNumber: string;
  taxId: string;
  businessName: string;
  email: string;
  contactPerson: string;
  address: string;
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
};

export default function AddVendor() {
  const router = useRouter();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<VendorFormData>({
    resolver: yupResolver(vendorSchema),
    defaultValues: {
      country: "India"
    }
  });
const isValidGST = (gst: string | undefined) => {
    if (!gst) return false;
    const gstPattern =
      /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    return gstPattern.test(gst);
  };
  const [isGstVerifying, setIsGstVerifying] = useState(false);
  // Watch for changes in form fields
  const taxId = watch("taxId");

  const verifyGSTDetails = async (gstNumber: string) => {
   const trimmedGst = gstNumber.trim().toUpperCase();

  if (!isValidGST(trimmedGst)) {
    toast.error("Invalid GST format. Please enter a valid 15-character GST number.");
    return;
  }
    setIsGstVerifying(true);
    try {
      const { data } = await api.get(`/gst-verify?gst=${gstNumber}`);

      if (data.success) {
        // Prefill form with GST data
        setValue("businessName", data.businessName);
        setValue("address", data.address);
        setValue("street", data.street);
        setValue("city", data.city);
        setValue("state", data.state);
        setValue("postalCode", data.pincode);
        toast.success("GST details verified successfully");
      } else {
        toast.error("GST verification failed");
      }
    } catch (error) {
      toast.error("Error verifying GST details");
    } finally {
      setIsGstVerifying(false);
    }
  };

  const onSubmit = async (data: VendorFormData) => {
    try {
      const vendorData = {
        businessName: data.businessName,
        email: data.email,
        phoneNumber: data.phoneNumber,
        contactPerson: data.contactPerson,
        taxId: data.taxId,
        address: data.address,
        street: data.street,
        city: data.city,
        state: data.state,
        postalCode: data.postalCode,
        country: "India",
        status: "PENDING"
      };

      const { data: response } = await api.post("/vendors", vendorData);

      if (response.success) {
        toast.success("Vendor registered successfully!");
        router.push('/admin/vendors');
      }
    } catch (error: any) {
      console.error("Vendor creation error:", error);

      // Handle specific error cases
      if (error.response?.status === 409) {
        toast.error("A vendor with these details already exists");
      } else {
        toast.error(
          error.response?.data?.message ||
          "An unexpected error occurred while creating the vendor"
        );
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <ToastContainer />
      <div className="max-w-2xl mx-auto bg-white p-8 rounded-lg shadow">
        <h1 className="text-2xl font-bold text-gray-800 mb-6">Add New Dealer</h1>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                GST Number <span className="text-red-500">*</span>
              </label>
              <div className="flex gap-2">
                <input
                  {...register("taxId")}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder="Enter GST number"
                />
                <button
                  type="button"
                  onClick={() => taxId && verifyGSTDetails(taxId)}
                  disabled={isGstVerifying || !taxId}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {isGstVerifying ? "Verifying..." : "Verify"}
                </button>
              </div>
              {errors.taxId && (
                <p className="text-red-500 text-sm mt-1">{errors.taxId.message}</p>
              )}
            </div>
            <div className="col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone Number <span className="text-red-500">*</span>
              </label>
              <input
                {...register("phoneNumber")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter 10-digit phone number"
              />
              {errors.phoneNumber && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.phoneNumber.message}
                </p>
              )}
            </div>



            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Business Name <span className="text-red-500">*</span>
              </label>
              <input
                {...register("businessName")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter business name"
              />
              {errors.businessName && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.businessName.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                {...register("email")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter email"
              />
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.email.message}
                </p>
              )}
            </div>



            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Contact Person <span className="text-red-500">*</span>
              </label>
              <input
                {...register("contactPerson")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter contact person name"
              />
              {errors.contactPerson && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.contactPerson.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Address <span className="text-red-500">*</span>
              </label>
              <input
                {...register("address")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter address"
              />
              {errors.address && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.address.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Street <span className="text-red-500">*</span>
              </label>
              <input
                {...register("street")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter street"
              />
              {errors.street && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.street.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                City <span className="text-red-500">*</span>
              </label>
              <input
                {...register("city")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter city"
              />
              {errors.city && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.city.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                State <span className="text-red-500">*</span>
              </label>
              <input
                {...register("state")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter state"
              />
              {errors.state && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.state.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Postal Code <span className="text-red-500">*</span>
              </label>
              <input
                {...register("postalCode")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter postal code"
              />
              {errors.postalCode && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.postalCode.message}
                </p>
              )}
            </div>
          </div>

          <div className="mt-6">
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? "Please wait..." : "Add Vendor"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
