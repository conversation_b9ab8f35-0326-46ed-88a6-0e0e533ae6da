import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

export async function GET(request: NextRequest) {
  try {


    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      console.log("GET /api/v1/vendor/profile - No auth token found");
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    // console.log("GET /api/v1/vendor/profile - Auth token found, verifying...");
    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
      email: string;
      role: string;
    };
    // console.log("GET /api/v1/vendor/profile - Token verified, email:", decoded.email);

    const vendor = await prisma.vendor.findUnique({
      where: { email: decoded.email },
    });

    if (!vendor) {
     // console.log("GET /api/v1/vendor/profile - Vendor not found for email:", decoded.email);
      return NextResponse.json(
        { message: "Vendor not found", success: false },
        { status: 404 }
      );
    }

    // Check for existing FCM token for this vendor
    const existingFcmToken = await prisma.fCMToken.findFirst({
      where: {
        userId: vendor.id,
        userType: "vendor",
        isActive: true,
      },
    });

    console.log("GET /api/v1/vendor/profile - Vendor found:", vendor.id);
    return NextResponse.json({
      message: "Profile fetched successfully",
      success: true,
      data: {
        ...vendor,
        fcmToken: existingFcmToken?.token || null,
      },
    });
  } catch (error) {
    console.error("Error fetching profile:", error);
    return NextResponse.json(
      { message: "Failed to fetch profile", success: false },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
      email: string;
      role: string;
    };

    const vendor = await prisma.vendor.findUnique({
      where: { email: decoded.email },
    });

    if (!vendor) {
      return NextResponse.json(
        { message: "Vendor not found", success: false },
        { status: 404 }
      );
    }

    const body = await request.json();

    const updatedVendor = await prisma.vendor.update({
      where: { id: vendor.id },
      data: {
        businessName: body.businessName,
        phoneNumber: body.phoneNumber,
        address: body.address,
        street: body.street,
        city: body.city,
        state: body.state,
        postalCode: body.postalCode,
        country: body.country,
        contactPerson: body.contactPerson,
        alternatePhone: body.alternatePhone,
        bankName: body.bankName,
        bankAccountNo: body.bankAccountNo,
        ifscCode: body.ifscCode,
      },
    });

    return NextResponse.json({
      message: "Profile updated successfully",
      success: true,
      data: updatedVendor,
    });
  } catch (error) {
    console.error("Error updating profile:", error);
    return NextResponse.json(
      { message: "Failed to update profile", success: false },
      { status: 500 }
    );
  }
}
