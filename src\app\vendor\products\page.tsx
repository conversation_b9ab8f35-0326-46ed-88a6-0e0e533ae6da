"use client";
import { useState, useEffect, useRef } from "react";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";
import { useCallback } from "react";
import { vendorAPI as api } from "@/lib/axios";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { FiFilter, FiChevronDown, FiSearch } from "react-icons/fi";
import LoadingSpinner from "@/components/vendor/LoadingSpinner";
import ProductImage from "@/components/vendor/ProductImage";

interface Product {
  id: string;
  name: string;
  description: string;
  basePrice: number;
  packagingSize: number;
  packagingUnit: string;
  images: string[];
  category: {
    name: string;
  };
  brand?: {
    name: string;
    imageAspectRatio?: string;
  };
  material?: {
    name: string;
  };
  size?: {
    name: string;
  };
}

interface SelectedProduct {
  id: string;
  quantity: number;
}

export default function Products() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState([]);
  const [brands, setBrands] = useState([]);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [showBrandDropdown, setShowBrandDropdown] = useState(false);
  const [showPriceDropdown, setShowPriceDropdown] = useState(false);
  const [tempMinPrice, setTempMinPrice] = useState("");
  const [tempMaxPrice, setTempMaxPrice] = useState("");
  const [categorySearch, setCategorySearch] = useState("");
  const [brandSearch, setBrandSearch] = useState("");
  const [filteredCategories, setFilteredCategories] = useState([]);
  const [filteredBrands, setFilteredBrands] = useState([]);
  const [selectedProducts, setSelectedProducts] = useState<SelectedProduct[]>([]);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 12,
    totalPages: 1
  });
  const categoryDropdownRef = useRef<HTMLDivElement>(null);
  const brandDropdownRef = useRef<HTMLDivElement>(null);
  const priceDropdownRef = useRef<HTMLDivElement>(null);
  const searchParams = useSearchParams();
  const router = useRouter();

  const category = searchParams.get("category");
  const brand = searchParams.get("brand");
  const sort = searchParams.get("sort") || "price_desc";
  const minPrice = searchParams.get("minPrice");
  const maxPrice = searchParams.get("maxPrice");
  const page = parseInt(searchParams.get("page") || "1");

  // Create a function to update URL params without causing a full page refresh
  const updateUrlParams = useCallback((params: Record<string, string | null>) => {
    const newParams = new URLSearchParams(searchParams.toString());

    // Update or remove params based on the provided values
    Object.entries(params).forEach(([key, value]) => {
      if (value === null) {
        newParams.delete(key);
      } else {
        newParams.set(key, value);
      }
    });

    // Use router.replace to update the URL without a full page refresh
    router.replace(`?${newParams.toString()}`, { scroll: false });
  }, [searchParams, router]);

  useEffect(() => {
    fetchProducts();
    fetchCategories();
    fetchBrands();

    // Initialize temp price values from URL params
    setTempMinPrice(minPrice || "");
    setTempMaxPrice(maxPrice || "");
  }, [category, brand, sort, minPrice, maxPrice, page]);

  // Filter categories based on search
  useEffect(() => {
    if (categories.length > 0) {
      if (!categorySearch) {
        setFilteredCategories(categories);
      } else {
        const filtered = categories.filter((cat: any) =>
          cat.name.toLowerCase().includes(categorySearch.toLowerCase())
        );
        setFilteredCategories(filtered);
      }
    }
  }, [categories, categorySearch]);

  // Filter brands based on search
  useEffect(() => {
    if (brands.length > 0) {
      if (!brandSearch) {
        setFilteredBrands(brands);
      } else {
        const filtered = brands.filter((brand: any) =>
          brand.name.toLowerCase().includes(brandSearch.toLowerCase())
        );
        setFilteredBrands(filtered);
      }
    }
  }, [brands, brandSearch]);

  // Handle click outside to close dropdowns
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (categoryDropdownRef.current && !categoryDropdownRef.current.contains(event.target as Node)) {
        setShowCategoryDropdown(false);
      }
      if (brandDropdownRef.current && !brandDropdownRef.current.contains(event.target as Node)) {
        setShowBrandDropdown(false);
      }
      if (priceDropdownRef.current && !priceDropdownRef.current.contains(event.target as Node)) {
        setShowPriceDropdown(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Fetching products...");

      const params = new URLSearchParams();
      if (category) params.append("category", category);
      if (brand) params.append("brand", brand);
      if (sort) params.append("sort", sort);
      if (minPrice) params.append("minPrice", minPrice);
      if (maxPrice) params.append("maxPrice", maxPrice);
      params.append("page", page.toString());
      params.append("limit", "12");

      const url = `/products?${params.toString()}`;
      console.log("Fetching URL:", url);

      const { data } = await api.get(url);
      console.log("Products response:", data);

      if (data.success) {
        setProducts(data.data);
        if (data.pagination) {
          setPagination(data.pagination);
        }
      } else {
        setError(data.message || "Failed to fetch products");
      }
    } catch (error) {
      console.error("Error fetching products:", error);
      setError("Failed to fetch products. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  const fetchBrands = async () => {
    try {
      const { data } = await api.get("/brands");
      if (data.success) {
        setBrands(data.data);
        setFilteredBrands(data.data);
      }
    } catch (error) {
      console.error("Error fetching brands:", error);
    }
  };

  const fetchCategories = async () => {
    try {
      const { data } = await api.get("/categories");
      if (data.success) {
        setCategories(data.data);
        setFilteredCategories(data.data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const addToCart = async (productId: string, quantity: number) => {
    try {
      const { data } = await api.post("/cart", { productId, quantity:quantity });
      if (data.success) {
        toast.success("Product added to cart successfully", {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      }
    } catch (error) {
      console.error("Error adding to cart:", error);
      toast.error("Failed to add product to cart", {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    }
  };

  const toggleProductSelection = (product: Product) => {
    setSelectedProducts(prev => {
      // Check if product is already selected
      const existingIndex = prev.findIndex(item => item.id === product.id);

      if (existingIndex >= 0) {
        // If already selected, remove it
        return prev.filter(item => item.id !== product.id);
      } else {
        // If not selected, add it with default quantity of 1 (not packaging size)
        return [...prev, { id: product.id, quantity: 1 }];
      }
    });
  };

  const isProductSelected = (productId: string) => {
    return selectedProducts.some(item => item.id === productId);
  };

  const addSelectedToCart = async () => {
    if (selectedProducts.length === 0) {
      toast.info("Please select at least one product", {
        position: "top-right",
        autoClose: 3000,
      });
      return;
    }

    try {
      // Add each selected product to cart one by one
      for (const product of selectedProducts) {
        await api.post("/cart", {
          productId: product.id,
          quantity: product.quantity
        });
      }

      toast.success(`${selectedProducts.length} products added to cart successfully`, {
        position: "top-right",
        autoClose: 3000,
      });
      setSelectedProducts([]); // Clear selection after adding to cart
    } catch (error) {
      console.error("Error adding selected products to cart:", error);
      toast.error("Failed to add some products to cart", {
        position: "top-right",
        autoClose: 3000,
      });
    }
  };

  const selectAllProducts = () => {
    if (selectedProducts.length === products.length) {
      // If all products are already selected, deselect all
      setSelectedProducts([]);
    } else {
      // Otherwise, select all products with quantity 1
      const allProducts = products.map(product => ({
        id: product.id,
        quantity: 1
      }));
      setSelectedProducts(allProducts);
    }
  };



  if (loading) {
    return <LoadingSpinner message="Loading products..." />;
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <div className="bg-red-100 rounded-full h-20 w-20 flex items-center justify-center mx-auto mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Products</h3>
        <p className="text-red-500 max-w-md mx-auto text-center">{error}</p>
      </div>
    );
  }

  if (!products.length) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <div className="bg-white rounded-full h-20 w-20 flex items-center justify-center mx-auto mb-6 shadow-sm">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Products Found</h3>
        <p className="text-gray-600 max-w-md mx-auto text-center">
          No products found with selected combination. Try adjusting your filter criteria or browse other categories.
        </p>
      </div>
    );
  }

  return (
    <div>
      <ToastContainer />
      <div className="mb-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Products</h1>

          {selectedProducts.length > 0 && (
            <div className="flex items-center">
              <span className="mr-3 text-gray-600 font-medium">
                {selectedProducts.length} product{selectedProducts.length > 1 ? 's' : ''} selected
              </span>
              <button
                onClick={addSelectedToCart}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Add to Cart
              </button>
            </div>
          )}
        </div>

        {/* Filters at the top */}
        <div className="mb-6 sm:mb-8">
          <div className="flex flex-wrap items-center gap-2 sm:gap-4">
            <div className="flex items-center">
              <FiFilter className="text-blue-600 mr-1 sm:mr-2" size={16} />
              <h2 className="font-medium text-gray-800 text-sm sm:text-base">Filters:</h2>
            </div>

            <button
              onClick={selectAllProducts}
              className="text-blue-600 hover:text-blue-800 text-xs sm:text-sm flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 sm:h-4 sm:w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {selectedProducts.length === products.length ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8h16M4 16h16" />
                )}
              </svg>
              {selectedProducts.length === products.length ? 'Deselect All' : 'Select All'}
            </button>

            {/* Category Filter with Dropdown */}
            <div className="relative" ref={categoryDropdownRef}>
              <button
                className="flex items-center gap-1 sm:gap-2 text-gray-700 font-medium py-1.5 sm:py-2 px-2 sm:px-4 rounded-md hover:bg-gray-50 transition-colors text-xs sm:text-sm"
                onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
              >
                <span>Category</span>
                <FiChevronDown className={`transition-transform ${showCategoryDropdown ? 'rotate-180' : ''}`} size={14} />
              </button>

              {showCategoryDropdown && (
                <div className="absolute z-50 mt-1 w-64 bg-white rounded-md shadow-lg border overflow-hidden">
                  <div className="p-3 border-b">
                    <h3 className="font-medium text-gray-700 mb-2 text-sm">Select Category</h3>
                    <div className="relative">
                      <FiSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={14} />
                      <input
                        type="text"
                        placeholder="Search categories..."
                        value={categorySearch}
                        onChange={(e) => setCategorySearch(e.target.value)}
                        className="w-full pl-9 pr-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                      />
                    </div>
                  </div>
                  <div className="max-h-60 overflow-y-auto p-2">
                    <div
                      className={`px-3 py-2 rounded-md cursor-pointer text-sm ${!category ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'}`}
                      onClick={() => {
                        updateUrlParams({ category: null });
                        setShowCategoryDropdown(false);
                        setCategorySearch("");
                      }}
                    >
                      All Categories
                    </div>
                    {filteredCategories.map((cat: any) => (
                      <div
                        key={cat.id}
                        className={`px-3 py-2 rounded-md cursor-pointer text-sm ${category === cat.id ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'}`}
                        onClick={() => {
                          updateUrlParams({ category: cat.id });
                          setShowCategoryDropdown(false);
                          setCategorySearch("");
                        }}
                      >
                        {cat.name}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Sub Categories Filter with Dropdown */}
            <div className="relative" ref={brandDropdownRef}>
              <button
                className="flex items-center gap-1 sm:gap-2 text-gray-700 font-medium py-1.5 sm:py-2 px-2 sm:px-4 rounded-md hover:bg-gray-50 transition-colors text-xs sm:text-sm"
                onClick={() => setShowBrandDropdown(!showBrandDropdown)}
              >
                <span>Sub Categories</span>
                <FiChevronDown className={`transition-transform ${showBrandDropdown ? 'rotate-180' : ''}`} size={14} />
              </button>

              {showBrandDropdown && (
                <div className="absolute z-50 mt-1 w-64 bg-white rounded-md shadow-lg border overflow-hidden">
                  <div className="p-3 border-b">
                    <h3 className="font-medium text-gray-700 mb-2">Select Sub Category</h3>
                    <div className="relative">
                      <FiSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search sub categories..."
                        value={brandSearch}
                        onChange={(e) => setBrandSearch(e.target.value)}
                        className="w-full pl-9 pr-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                  <div className="max-h-60 overflow-y-auto p-2">
                    <div
                      className={`px-3 py-2 rounded-md cursor-pointer ${!brand ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'}`}
                      onClick={() => {
                        updateUrlParams({ brand: null });
                        setShowBrandDropdown(false);
                        setBrandSearch("");
                      }}
                    >
                      All Sub Categories
                    </div>
                    {filteredBrands.map((b: any) => (
                      <div
                        key={b.id}
                        className={`px-3 py-2 rounded-md cursor-pointer ${brand === b.id ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'}`}
                        onClick={() => {
                          updateUrlParams({ brand: b.id });
                          setShowBrandDropdown(false);
                          setBrandSearch("");
                        }}
                      >
                        {b.name}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Price Range Filter with Dropdown */}
            <div className="relative" ref={priceDropdownRef}>
              <button
                className="flex items-center gap-2 text-gray-700 font-medium py-2 px-4 rounded-md hover:bg-gray-50 transition-colors"
                onClick={() => setShowPriceDropdown(!showPriceDropdown)}
              >
                <span className="text-blue-600 font-medium">₹</span>
                <span>Price Range</span>
                <FiChevronDown className={`transition-transform ${showPriceDropdown ? 'rotate-180' : ''}`} />
              </button>

              {showPriceDropdown && (
                <div className="absolute z-50 mt-1 w-72 bg-white rounded-md shadow-lg border p-4">
                  <h3 className="font-medium text-gray-700 mb-3">Set Price Range</h3>
                  <div className="flex gap-4 mb-4">
                    <div className="flex-1">
                      <label className="block text-sm text-gray-600 mb-1">Min Price</label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">₹</span>
                        <input
                          type="text"
                          value={tempMinPrice}
                          onChange={(e) => setTempMinPrice(e.target.value)}
                          className="pl-7 pr-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm w-full"
                          placeholder="0"
                        />
                      </div>
                    </div>
                    <div className="flex-1">
                      <label className="block text-sm text-gray-600 mb-1">Max Price</label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">₹</span>
                        <input
                          type="text"
                          value={tempMaxPrice}
                          onChange={(e) => setTempMaxPrice(e.target.value)}
                          className="pl-7 pr-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm w-full"
                          placeholder="10000"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <button
                      className="text-gray-600 hover:text-gray-800"
                      onClick={() => {
                        setTempMinPrice("");
                        setTempMaxPrice("");
                        updateUrlParams({ minPrice: null, maxPrice: null });
                        setShowPriceDropdown(false);
                      }}
                    >
                      Clear
                    </button>
                    <button
                      className="bg-blue-600 text-white px-4 py-1 rounded hover:bg-blue-700"
                      onClick={() => {
                        updateUrlParams({
                          minPrice: tempMinPrice || null,
                          maxPrice: tempMaxPrice || null
                        });
                        setShowPriceDropdown(false);
                      }}
                    >
                      Apply
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Sort By */}
            <div className="ml-auto">
              <select
                value={sort}
                onChange={(e) => {
                  updateUrlParams({ sort: e.target.value });
                }}
                className="rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none transition-all bg-white"
              >
                <option value="price_desc">Price: High to Low</option>
                <option value="price_asc">Price: Low to High</option>
                <option value="newest">Newest</option>
              </select>
            </div>
          </div>

          {/* Active Filters */}
          {(category || brand || minPrice || maxPrice) && (
            <div className="mt-4 flex flex-wrap gap-2">
              {category && categories.find((c: any) => c.id === category) && (
                <div className="flex items-center bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm">
                  <span>Category: {(categories.find((c: any) => c.id === category) as any)?.name}</span>
                  <button
                    className="ml-2 text-blue-500 hover:text-blue-700"
                    onClick={() => {
                      updateUrlParams({ category: null });
                    }}
                  >
                    &times;
                  </button>
                </div>
              )}

              {brand && brands.find((b: any) => b.id === brand) && (
                <div className="flex items-center bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm">
                  <span>Sub Category: {(brands.find((b: any) => b.id === brand) as any)?.name}</span>
                  <button
                    className="ml-2 text-blue-500 hover:text-blue-700"
                    onClick={() => {
                      updateUrlParams({ brand: null });
                    }}
                  >
                    &times;
                  </button>
                </div>
              )}

              {(minPrice || maxPrice) && (
                <div className="flex items-center bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm">
                  <span>Price: {minPrice ? `₹${minPrice}` : '₹0'} - {maxPrice ? `₹${maxPrice}` : 'Any'}</span>
                  <button
                    className="ml-2 text-blue-500 hover:text-blue-700"
                    onClick={() => {
                      updateUrlParams({ minPrice: null, maxPrice: null });
                    }}
                  >
                    &times;
                  </button>
                </div>
              )}

              <button
                className="text-blue-600 hover:text-blue-800 text-sm underline"
                onClick={() => {
                  updateUrlParams({
                    category: null,
                    brand: null,
                    minPrice: null,
                    maxPrice: null,
                    sort: "price_desc",
                    page: "1"
                  });
                }}
              >
                Clear All Filters
              </button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 gap-4 sm:gap-6">
          <div className="w-full">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4">
              {products.map((product) => (
              <div
                key={product.id}
                className={`bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden h-auto sm:h-[450px] flex flex-col ${isProductSelected(product.id) ? 'ring-2 ring-blue-600' : ''}`}
              >
                <div className="relative w-full" style={{ height: '240px' }}>
                  {/* Selection checkbox */}
                  <div className="absolute top-2 right-2 z-10">
                    <div
                      className={`h-8 w-8 rounded-full flex items-center justify-center cursor-pointer shadow-md transition-all duration-200 ${
                        isProductSelected(product.id)
                          ? 'bg-blue-600 text-white scale-110 ring-2 ring-blue-300'
                          : 'bg-white text-gray-400 hover:bg-gray-100 hover:scale-105'
                      }`}
                      onClick={() => toggleProductSelection(product)}
                    >
                      {isProductSelected(product.id) ? (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4v16m8-8H4" />
                        </svg>
                      )}
                    </div>
                  </div>

                  {/* Use the ProductImage component with aspect ratio from brand */}
                  <ProductImage
                    src={product.images?.[0] || null}
                    alt={product.name}
                    priority={true}
                    aspectRatio={product.brand?.imageAspectRatio || "9:16"}
                  />
                </div>
                <div
                  className="p-3 flex-grow flex flex-col"
                  onClick={() => toggleProductSelection(product)}
                >
                  <h3 className="font-bold text-gray-800 truncate capitalize">{product.name}</h3>

                  {/* Description - fixed height section */}
                  <div className="min-h-[30px]">
                    {product.description && product.description.trim() !== "" && (
                      <p className="text-sm text-gray-600 mt-1 line-clamp-1 overflow-hidden">
                        {product.description}
                      </p>
                    )}
                  </div>

                  {/* Product specs - fixed height section */}
                  <div className="min-h-[40px] mt-1">
                    {product.size && (
                      <p className="text-sm text-gray-700">
                        <span className="font-medium">Size:</span> {product.size.name}
                      </p>
                    )}
                    {product.material && (
                      <p className="text-sm text-gray-700">
                        <span className="font-medium">Material:</span> {product.material.name}
                      </p>
                    )}
                  </div>

                  {/* Sub-category info - fixed height section */}
                  <div className="min-h-[20px]">
                    {product.brand && (
                      <p className="flex items-center text-sm">
                        <span className="font-medium mr-1">Sub Category:</span>
                        <span className="text-blue-600">{product.brand.name}</span>
                      </p>
                    )}
                  </div>

                  {/* Price and add to cart - fixed at bottom */}
                  <div className="mt-auto pt-1 flex justify-between items-end">
                    <div>
                      <span className="text-xl font-bold text-blue-600">
                        ₹{product.basePrice}
                      </span>
                      <span className="text-sm text-gray-500 ml-1">
                        per {product.packagingUnit || 'piece'}
                      </span>
                      <p className="text-xs text-gray-500 mt-1">
                        Pack: {product.packagingSize} {product.packagingUnit || 'piece'}{product.packagingSize > 1 ? 's' : ''}
                      </p>
                    </div>

                    <button
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent triggering the parent onClick
                        addToCart(product.id, 1); // Add 1 item instead of packaging size
                      }}
                      className="flex items-center justify-center bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                      Add
                    </button>
                  </div>
                </div>
              </div>
            ))}
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="flex justify-center mt-8">
                <nav className="flex items-center space-x-2">
                  <button
                    onClick={() => {
                      if (page > 1) {
                        updateUrlParams({ page: (page - 1).toString() });
                      }
                    }}
                    disabled={page <= 1}
                    className={`px-3 py-1 rounded-md ${
                      page <= 1
                        ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                        : "bg-white text-gray-700 hover:bg-gray-50 border"
                    }`}
                  >
                    Previous
                  </button>

                  {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((pageNum) => (
                    <button
                      key={pageNum}
                      onClick={() => {
                        updateUrlParams({ page: pageNum.toString() });
                      }}
                      className={`px-3 py-1 rounded-md ${
                        pageNum === page
                          ? "bg-blue-600 text-white"
                          : "bg-white text-gray-700 hover:bg-gray-50 border"
                      }`}
                    >
                      {pageNum}
                    </button>
                  ))}

                  <button
                    onClick={() => {
                      if (page < pagination.totalPages) {
                        updateUrlParams({ page: (page + 1).toString() });
                      }
                    }}
                    disabled={page >= pagination.totalPages}
                    className={`px-3 py-1 rounded-md ${
                      page >= pagination.totalPages
                        ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                        : "bg-white text-gray-700 hover:bg-gray-50 border"
                    }`}
                  >
                    Next
                  </button>
                </nav>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
