"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { PencilIcon, TrashIcon, ArrowLeftIcon } from "@heroicons/react/24/outline";
import { adminAPI as api } from "@/lib/axios";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

interface Product {
  id: string;
  name: string;
  description?: string;
  basePrice: number;
  packagingSize: number;
  packagingUnit: string;
  images?: string[];
  isActive: boolean;
  category: {
    name: string;
  };
  brand?: {
    name: string;
    imageAspectRatio?: string;
  };
  material?: {
    name: string;
  };
  size?: {
    name: string;
  };
}

interface Category {
  id: string;
  name: string;
}

interface Brand {
  id: string;
  name: string;
}

const CategoryBrandProductsPage = ({ params }: { params: { id: string; brandId: string } }) => {
  const router = useRouter();
  const { id: categoryId, brandId } = params;
  const [products, setProducts] = useState<Product[]>([]);
  const [category, setCategory] = useState<Category | null>(null);
  const [brand, setBrand] = useState<Brand | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchCategory = async () => {
    try {
      const { data } = await api.get(`/categories/${categoryId}`);
      if (data.success) {
        setCategory(data.data);
      } else {
        toast.error("Failed to fetch category details");
      }
    } catch (error) {
      console.error("Error fetching category:", error);
      toast.error("Failed to fetch category details");
    }
  };

  const fetchBrand = async () => {
    try {
      const { data } = await api.get(`/brands/${brandId}`);
      if (data.success) {
        setBrand(data.data);
      } else {
        toast.error("Failed to fetch sub category details");
      }
    } catch (error) {
      console.error("Error fetching brand:", error);
      toast.error("Failed to fetch sub category details");
    }
  };

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const { data } = await api.get(`/categories/${categoryId}/brands/${brandId}/products`);
      if (data.success) {
        setProducts(data.data);
      } else {
        toast.error("Failed to fetch products");
        setProducts([]);
      }
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error("Failed to fetch products");
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategory();
    fetchBrand();
    fetchProducts();
  }, [categoryId, brandId]);

  const getImageUrl = (url?: string) => {
    if (!url) return null;
    if (url.startsWith("http://") || url.startsWith("https://")) {
      return url;
    }
    return `${process.env.NEXT_PUBLIC_API_URL || ""}${
      url.startsWith("/") ? "" : "/"
    }${url}`;
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this product?")) {
      try {
        const response = await api.delete(`/products/${id}`);
        const result = response.data;

        if (result.success) {
          toast.success("Product deleted successfully");
          fetchProducts(); // Refresh the list
        } else {
          toast.error(result.message || "Failed to delete product");
        }
      } catch (error: any) {
        console.error("Error deleting product:", error);
        toast.error(error.response?.data?.message || "Failed to delete product");
      }
    }
  };

  if (loading) {
    return <div className="p-4">Loading...</div>;
  }

  return (
    <div className="p-4">
      <ToastContainer />
      <div className="flex items-center mb-6">
        <button
          onClick={() => router.push(`/admin/categories/${categoryId}/brands`)}
          className="mr-4 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-1" />
          Back to Sub Categories
        </button>
        <h1 className="text-2xl font-bold">
          {category?.name} - {brand?.name} - Products
        </h1>
        <div className="ml-auto">
          <Link
            href="/admin/products/add"
            className="inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
          >
            Add New Product
          </Link>
        </div>
      </div>

      {products.length === 0 ? (
        <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
          <p className="text-gray-500 mb-4">No products found for this sub category.</p>
          <Link
            href="/admin/products/add"
            className="inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
          >
            Add New Product
          </Link>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Image
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Packaging
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {products.map((product) => (
                <tr key={product.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="relative h-16 w-16">
                      {product.images && product.images.length > 0 ? (
                        <Image
                          src={getImageUrl(product.images[0]) || "/placeholder.png"}
                          alt={product.name}
                          fill
                          className="rounded-md object-cover"
                        />
                      ) : (
                        <div className="h-full w-full bg-gray-200 rounded-md flex items-center justify-center">
                          <span className="text-gray-400 text-xs">No image</span>
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">{product.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap">₹{product.basePrice}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {product.packagingSize} {product.packagingUnit}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 py-1 text-xs rounded-full ${
                        product.isActive
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {product.isActive ? "Active" : "Inactive"}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex gap-2">
                      <Link
                        href={`/admin/products/edit/${product.id}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <PencilIcon className="h-5 w-5" />
                      </Link>
                      <button
                        onClick={() => handleDelete(product.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default CategoryBrandProductsPage;
