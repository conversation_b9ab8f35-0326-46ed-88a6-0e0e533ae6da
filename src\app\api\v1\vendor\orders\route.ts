// src/app/api/v1/vendor/orders/route.ts
import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

interface JWTPayload {
  id: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

export async function GET(request: NextRequest) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(
      authToken.value,
      process.env.JWT_KEY!
    ) as JWTPayload;

    // Find the vendor associated with this user
    const vendor = await prisma.vendor.findFirst({
      where: { email: decoded.email },
    });

    if (!vendor) {
      console.log("Vendor not found for email:", decoded.email);
      return NextResponse.json(
        { message: "Vendor account not found for this user", success: false },
        { status: 404 }
      );
    }

    const orders = await prisma.order.findMany({
      where: {
        vendorId: vendor.id, // Use the vendor's ID, not the user's ID
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({
      message: "Orders fetched successfully",
      success: true,
      data: orders,
    });
  } catch (error) {
    console.error("Error fetching orders:", error);
    return NextResponse.json(
      { message: "Failed to fetch orders", success: false },
      { status: 500 }
    );
  }
}
