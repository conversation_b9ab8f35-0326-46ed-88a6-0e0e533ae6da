"use client";
import { useState, useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Trash2, ShoppingBag } from "lucide-react";
import { vendorAPI as api } from "@/lib/axios";
import "react-toastify/dist/ReactToastify.css";
import PaymentModal from "@/components/vendor/PaymentModal";
import LoadingSpinner from "@/components/vendor/LoadingSpinner";

interface CartItem {
  id: string;
  quantity: number;
  product: {
    id: string;
    name: string;
    basePrice: number;
    gstPercentage: number;
    images: string[];
    packagingSize: number;
    brand?: {
      name: string;
    };
    material?: {
      name: string;
    };
    size?: {
      name: string;
    };
  };
}

interface CartValues {
  id: string;
  items: CartItem[];
}

export default function Cart() {
  const [cart, setCart] = useState<CartValues | null>(null);
  const [loading, setLoading] = useState(true);
  const [shippingAddress, setShippingAddress] = useState("");
  const [transport, setTransport] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("PAY_ONLINE");
  const [referenceId, setReferenceId] = useState("");
  const [hasCredit, setHasCredit] = useState(false); // Whether user has available credit
  const [loadingAddress, setLoadingAddress] = useState(true); // Loading state for address
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const router = useRouter();

  useEffect(() => {
    fetchCart();
    fetchVendorProfileAndSettings();
  }, []);

  const fetchCart = async () => {
    try {
      const { data } = await api.get("/cart");
      if (data.success) {
        setCart(data.data);
      }
    } catch (error) {
      console.error("Error fetching cart:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchVendorProfileAndSettings = async () => {
    setLoadingAddress(true);
    try {
      console.log("Fetching vendor profile...");
      const { data } = await api.get("/profile");
      console.log("Vendor profile response:", data);

      if (data.success) {
        // Check if vendor has available credit
        setHasCredit(data.data.isCreditGiven && data.data.maxCredit > 0);

        // Format and set the shipping address from vendor profile
        const vendor = data.data;
        console.log("Vendor data:", vendor);

        if (vendor) {
          const addressParts = [
            vendor.address,
            vendor.street,
            `${vendor.city}, ${vendor.state} ${vendor.postalCode}`,
            vendor.country,
          ].filter(Boolean);

          console.log("Address parts:", addressParts);

          const formattedAddress = addressParts.join("\n");
          console.log("Formatted address:", formattedAddress);

          setShippingAddress(formattedAddress);
        }
      }
    } catch (error) {
      console.error("Error fetching vendor profile:", error);
      setHasCredit(false);
    } finally {
      setLoadingAddress(false);
    }
  };

  const updateQuantity = async (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return; // Don't allow quantities less than 1

    try {
      // Optimistically update the UI first
      setCart((prevCart) => {
        if (!prevCart) return null;

        const updatedItems = prevCart.items.map((item) => {
          if (item.id === itemId) {
            return { ...item, quantity: newQuantity };
          }
          return item;
        });

        return { ...prevCart, items: updatedItems };
      });

      // Then send the update to the server
      const { data } = await api.patch(`/cart/items/${itemId}`, {
        quantity: newQuantity,
      });

      if (!data.success) {
        // If the update failed, revert to the original cart
        fetchCart();
      }
    } catch (error) {
      console.error("Error updating quantity:", error);
      // If there was an error, revert to the original cart
      fetchCart();
    }
  };

  const removeItem = async (itemId: string) => {
    try {
      // Optimistically update the UI first
      setCart((prevCart) => {
        if (!prevCart) return null;

        const updatedItems = prevCart.items.filter(
          (item) => item.id !== itemId
        );
        return { ...prevCart, items: updatedItems };
      });

      // Then send the delete request to the server
      const { data } = await api.delete(`/cart/items/${itemId}`);

      if (!data.success) {
        // If the delete failed, revert to the original cart
        fetchCart();
      }
    } catch (error) {
      console.error("Error removing item:", error);
      // If there was an error, revert to the original cart
      fetchCart();
    }
  };

  // Initial checkout validation before showing payment modal
  const handleCheckout = () => {
    if (!shippingAddress) {
      alert("Please enter a shipping address");
      return;
    }

    // Transport is now optional, so we don't check for it

    // If payment method is credit, proceed directly to checkout
    if (paymentMethod === "CREDIT") {
      processOrder();
    } else {
      // For online payment, show the payment modal
      setShowPaymentModal(true);
    }
  };

  // Process payment with reference ID (from modal)
  const handlePaymentConfirm = (paymentRefId: string) => {
    setReferenceId(paymentRefId);
    processOrder(paymentRefId);
  };

  // Process the actual order
  const processOrder = async (paymentRefId?: string) => {
    try {
      const { data } = await api.post("/checkout", {
        shippingAddress,
        transport,
        paymentMethod,
        referenceId:
          paymentMethod === "PAY_ONLINE"
            ? paymentRefId || referenceId
            : undefined,
      });

      if (data.success) {
        // Redirect to thank you page with order ID
        router.push(
          `/vendor/orders/thank-you?orderId=${data.data.id || "new"}`
        );
      } else {
        alert(data.message || "Failed to place order");
      }
    } catch (error) {
      console.error("Error during checkout:", error);
      alert("Failed to place order");
    } finally {
      setShowPaymentModal(false);
    }
  };

  if (loading) {
    return <LoadingSpinner message="Loading your cart..." />;
  }

  if (!cart || cart.items.length === 0) {
    return (
      <div className="text-center py-16 max-w-md mx-auto">
        <ShoppingBag className="h-16 w-16 mx-auto text-gray-400 mb-4" />
        <h2 className="text-2xl font-semibold mb-2">Your cart is empty</h2>
        <p className="text-gray-600 mb-6">
          Looks like you haven't added any products to your cart yet.
        </p>
        <button
          onClick={() => router.push("/vendor/categories")}
          className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors inline-flex items-center"
        >
          <ShoppingBag className="h-5 w-5 mr-2" />
          Continue Shopping
        </button>
      </div>
    );
  }

  // Calculate base amount, GST amount, and total amount
  const baseAmount = cart.items.reduce(
    (sum, item) => sum + item.product.basePrice * item.quantity,
    0
  );

  const gstAmount = cart.items.reduce(
    (sum, item) =>
      sum +
      ((item.product.basePrice * item.product.gstPercentage) / 100) *
        item.quantity,
    0
  );

  const totalAmount = baseAmount + gstAmount;

  return (
    <div className="max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold mb-8">Shopping Cart</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-8">
        <div className="md:col-span-2">
          {cart.items.map((item) => (
            <div
              key={item.id}
              className="bg-white rounded-lg shadow-md p-3 sm:p-4 mb-4 flex flex-col sm:flex-row items-start sm:items-center"
            >
              {/* Product image */}
              <div className="relative h-20 w-20 sm:h-24 sm:w-24 mb-3 sm:mb-0 sm:mr-4 overflow-hidden rounded-md border border-gray-200 mx-auto sm:mx-0">
                <Image
                  src={item.product.images[0]}
                  alt={item.product.name}
                  fill
                  className="object-cover"
                />
              </div>

              {/* Product info */}
              <div className="flex-1 w-full">
                <div className="flex flex-col sm:flex-row justify-between items-center sm:items-start">
                  <div>
                    <h3 className="font-bold text-gray-800 uppercase text-center sm:text-left">
                      {item.product.name}
                    </h3>

                    {item.product.size && (
                      <p className="text-sm text-gray-700 text-center sm:text-left">
                        Size: {item.product.size.name}
                      </p>
                    )}
                    {item.product.material && (
                      <p className="text-sm text-gray-700 text-center sm:text-left">
                        Material: {item.product.material.name}
                      </p>
                    )}
                    {item.product.brand && (
                      <p className="text-sm text-gray-700 text-center sm:text-left">
                        Sub Category:{" "}
                        <span className="text-blue-600">
                          {item.product.brand.name}
                        </span>
                      </p>
                    )}
                    <p className="text-xs text-gray-500 mt-1 text-center sm:text-left">
                      Pack: {item.product.packagingSize}{" "}
                      {item.product.packagingSize > 1 ? "pieces" : "piece"}
                    </p>
                  </div>

                  <div className="flex flex-col items-center sm:items-end mt-2 sm:mt-0 sm:ml-4">
                    <div className="text-right">
                      <p className="text-sm text-gray-600">
                        Base Price: ₹{item.product.basePrice * item.quantity}
                      </p>
                      <p className="text-sm text-gray-600">
                        GST ({item.product.gstPercentage}%): ₹
                        {(
                          ((item.product.basePrice *
                            item.product.gstPercentage) /
                            100) *
                          item.quantity
                        ).toFixed(2)}
                      </p>
                      <p className="font-bold text-xl text-blue-600">
                        Total: ₹
                        {(item.product.basePrice * item.quantity +
                          ((item.product.basePrice *
                            item.product.gstPercentage) /
                            100) *
                            item.quantity).toFixed(2)}
                      </p>
                    </div>

                    {/* Quantity controls */}
                    <div className="flex items-center mt-2">
                      <div className="flex items-center border rounded-md shadow-sm">
                        <button
                          type="button"
                          onClick={() =>
                            updateQuantity(
                              item.id,
                              Math.max(1, item.quantity - 1)
                            )
                          }
                          className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-l-md touch-manipulation"
                          aria-label="Decrease quantity"
                        >
                          -
                        </button>
                        <input
                          type="number"
                          min={1}
                          step={1}
                          value={item.quantity}
                          onChange={(e) => {
                            const val = parseInt(e.target.value);
                            if (!isNaN(val) && val >= 1) {
                              updateQuantity(item.id, val);
                            }
                          }}
                          className="w-10 sm:w-12 text-center border-x-0 border-y-0 focus:ring-0 focus:outline-none py-1"
                          aria-label="Quantity"
                        />
                        <button
                          type="button"
                          onClick={() =>
                            updateQuantity(item.id, item.quantity + 1)
                          }
                          className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-r-md touch-manipulation"
                          aria-label="Increase quantity"
                        >
                          +
                        </button>
                      </div>

                      <button
                        onClick={() => removeItem(item.id)}
                        className="ml-2 text-red-500 hover:text-red-600 p-1 rounded-full hover:bg-red-50"
                        aria-label="Remove item"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="md:col-span-1">
          <div className="bg-white rounded-lg shadow p-4">
            <h2 className="text-lg font-semibold mb-4">Order Summary</h2>
            <div className="mb-4">
              <div className="flex justify-between mb-2">
                <span>Base Price</span>
                <span>₹{baseAmount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span>GST Amount</span>
                <span>₹{gstAmount.toFixed(2)}</span>
              </div>
              <div className="border-t pt-2 mt-2"></div>
              <div className="flex justify-between font-semibold text-lg">
                <span>Total</span>
                <span>₹{totalAmount.toFixed(2)}</span>
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">
                Shipping Address
              </label>
              <div className="relative">
                <textarea
                  value={shippingAddress}
                  onChange={(e) => setShippingAddress(e.target.value)}
                  className={`w-full border rounded-md px-3 py-2 ${loadingAddress ? "bg-gray-100" : ""}`}
                  rows={3}
                  placeholder="Enter your shipping address"
                  disabled={loadingAddress}
                />
                {loadingAddress && (
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-50 rounded-md">
                    <div className="animate-spin h-5 w-5 border-4 border-t-blue-500 border-b-blue-700 border-l-transparent border-r-transparent"></div>
                  </div>
                )}
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Your address is pre-filled from your profile but can be edited
                for this order.
              </p>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">
                Transport Details (Optional)
              </label>
              <input
                type="text"
                value={transport}
                onChange={(e) => setTransport(e.target.value)}
                className="w-full border rounded-md px-3 py-2"
                placeholder="Enter transport details (optional)"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">
                Payment Method
              </label>
              <select
                value={paymentMethod}
                onChange={(e) => setPaymentMethod(e.target.value)}
                className="w-full border rounded-md px-3 py-2"
              >
                <option value="PAY_ONLINE">Pay Online</option>
                {hasCredit && <option value="CREDIT">Pay with Credit</option>}
              </select>
            </div>

            {/* Payment method info text */}
            {paymentMethod === "PAY_ONLINE" && (
              <p className="text-sm text-gray-600 mb-4">
                You'll be asked to scan a QR code and provide a payment
                reference ID after clicking "Proceed to Checkout".
              </p>
            )}
            {paymentMethod === "CREDIT" && hasCredit && (
              <p className="text-sm text-gray-600 mb-4">
                Your order will be placed using your available credit.
              </p>
            )}

            <button
              onClick={handleCheckout}
              className="w-full bg-blue-500 text-white py-2 rounded-md hover:bg-blue-600"
            >
              Proceed to Checkout
            </button>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        onSubmit={handlePaymentConfirm}
        totalAmount={parseFloat(totalAmount.toFixed(2))}
      />
    </div>
  );
}
