import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

// Get all sizes
export async function GET() {
  try {
    const sizes = await prisma.size.findMany({
      orderBy: {
        name: "asc",
      },
    });

    return NextResponse.json({
      message: "Sizes fetched successfully",
      success: true,
      data: sizes,
    });
  } catch (error) {
    console.error("Error fetching sizes:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch sizes",
        success: false,
      },
      { status: 500 }
    );
  }
}

// Create a new size
interface CreateSizeRequest {
  name: string;
  isActive?: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as CreateSizeRequest;
    const { name, isActive = true } = body;

    // Check if size with the same name already exists
    const existingSize = await prisma.size.findUnique({
      where: { name },
    });

    if (existingSize) {
      return NextResponse.json(
        {
          message: "Size with this name already exists",
          success: false,
        },
        { status: 400 }
      );
    }

    const size = await prisma.size.create({
      data: {
        name,
        isActive,
      },
    });

    return NextResponse.json({
      message: "Size created successfully",
      success: true,
      data: size,
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating size:", error);
    return NextResponse.json(
      {
        message: "Failed to create size",
        success: false,
      },
      { status: 500 }
    );
  }
}
