/**
 * Zepto Mail Service - Direct API Implementation
 * This utility provides functions to send emails using the Zepto API directly
 */

/**
 * Send an email using Zepto API
 * @param to Recipient email address
 * @param name Recipient name
 * @param subject Email subject
 * @param htmlBody HTML content of the email
 * @returns Promise with the API response
 */
export async function sendEmail(
  to: string,
  name: string,
  subject: string,
  htmlBody: string
) {
  try {
    // Prepare the request payload for Zepto Mail AP
    if(!process.env.NEXT_ZEPTO_API_TOKEN && !process.env.NEXT_ZEPTO_SENDER_EMAIL && !process.env.ZEPTO_SENDER_NAME) {
      console.error("NEXT_ZEPTO_API_TOKEN is not defined in environment variables");
      return { success: false, error: "API configuration error" };
    }
    const payload = {
      from: {
        address: process.env.NEXT_ZEPTO_SENDER_EMAIL || "<EMAIL>",
        name: process.env.ZEPTO_SENDER_NAME || "Metropolis"
      },
      to: [
        {
          email_address: {
            address: to,
            name: name
          }
        }
      ],
      subject: subject,
      htmlbody: htmlBody
    };

    // Send email using Zepto Mail API
    const response = await fetch("https://api.zeptomail.in/v1.1/email", {
      method: "POST",
      headers: {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "Authorization": process.env.NEXT_ZEPTO_API_TOKEN || "Zoho-enczapikey PHtE6r0JR+G+3jUp8kRS5qDpE8ejM4x89OpuJQhF5ohDCvZWG01X/d4jwTSzr0wvU/BFQvLKzYJhuLyZt+vUdj2+NmlIDWqyqK3sx/VYSPOZsbq6x00asV0cdk3cUITvdNNp1CPTvd/TNA=="
      },
      body: JSON.stringify(payload)
    });
console.log("Email response:", response);
    const responseData = await response.json();

    if (!response.ok) {
      throw new Error(responseData.message || "Failed to send email");
    }

    return { success: true, data: responseData };
  } catch (error) {
    console.error("Error sending email via Zepto:", error);
    return { success: false, error };
  }
}

/**
 * Send an OTP email using Zepto API
 * @param to Recipient email address
 * @param otp OTP code
 * @returns Promise with the API response
 */

export async function sendOtpEmail(to: string, otp: string) {
  console.log("Sending OTP email to:", to, "OTP:", otp);  
  const subject = 'Your Metropolis Login OTP';
  const name = to.split('@')[0]; // Use part before @ as name

  // Create HTML content for the email
  const htmlBody = `
    <div style="font-family: Arial, sans-serif; color: #333; padding: 20px; max-width: 600px; border: 1px solid #ddd; border-radius: 8px;">
      <h2 style="color: #2D89EF; text-align: center;">Your Login OTP - Metropolis</h2>
      <p style="text-align: center;">Please use the following OTP to complete your login.</p>

      <div style="text-align: center; margin: 30px 0; padding: 20px; background-color: #f9f9f9; border-radius: 5px;">
        <div style="font-size: 32px; font-weight: bold; letter-spacing: 5px; color: #333; padding: 15px; background-color: #e9f0ff; border-radius: 5px; display: inline-block;">
          ${otp}
        </div>
      </div>

      <p style="font-size: 14px; color: #777; text-align: center;">
        This OTP is valid for 5 minutes. Please do not share this code with anyone.
      </p>

      <p style="margin-top: 20px; text-align: center; font-size: 12px; color: #999;">
        If you didn't request this OTP, please ignore this email.<br>
        This is an automated email. Please do not reply to this message.<br>
        &copy; ${new Date().getFullYear()} Metropolis. All rights reserved.
      </p>
    </div>
  `;

  return sendEmail(to, name, subject, htmlBody);
}
