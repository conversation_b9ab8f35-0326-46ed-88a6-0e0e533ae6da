import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

interface JWTPayload {
  id: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

/**
 * Get notifications for the authenticated user
 * GET /api/v1/notifications
 */
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    // Decode JWT token
    const decoded = jwt.verify(
      authToken.value,
      process.env.JWT_KEY!
    ) as JWTPayload;

    // Determine user type and get user ID
    const userType = decoded.role === "admin" ? "admin" : "vendor";
    let userId: string;

    if (userType === "admin") {
      const admin = await prisma.adminUser.findFirst({
        where: { email: decoded.email },
      });
      if (!admin) {
        return NextResponse.json(
          { message: "Admin user not found", success: false },
          { status: 404 }
        );
      }
      userId = admin.id;
    } else {
      const vendor = await prisma.vendor.findFirst({
        where: { email: decoded.email },
      });
      if (!vendor) {
        return NextResponse.json(
          { message: "Vendor not found", success: false },
          { status: 404 }
        );
      }
      userId = vendor.id;
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "20");
    const page = parseInt(searchParams.get("page") || "1");
    const isRead = searchParams.get("isRead");
    const type = searchParams.get("type");

    // Build where clause
    const where: any = {
      recipientType: userType,
      recipientId: userId,
    };

    // Add optional filters
    if (isRead !== null && isRead !== undefined) {
      where.isRead = isRead === "true";
    }

    if (type) {
      where.type = type;
    }

    // Get total count
    const totalCount = await prisma.notification.count({ where });

    // Get notifications with pagination
    const notifications = await prisma.notification.findMany({
      where,
      orderBy: {
        createdAt: "desc",
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    return NextResponse.json({
      message: "Notifications fetched successfully",
      success: true,
      data: {
        notifications,
        pagination: {
          total: totalCount,
          page,
          limit,
          pages: Math.ceil(totalCount / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching notifications:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch notifications",
        success: false,
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
