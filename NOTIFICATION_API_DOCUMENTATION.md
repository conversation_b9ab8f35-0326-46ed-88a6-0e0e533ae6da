# Notification API Documentation

## Overview
This document describes the notification system APIs for both admin and vendor users, including FCM token handling in login APIs and in-app notification management.

## FCM Token Integration

### 1. Vendor Login API
**Endpoint:** `POST /api/v1/auth/vendor/login/verify-otp`

**Changes Made:**
- Now returns `fcmToken` in the response data
- Returns `null` if no FCM token is stored for the user

**Response Format:**
```json
{
  "message": "Login successful",
  "success": true,
  "data": {
    "id": "user_id",
    "email": "<EMAIL>",
    "token": "jwt_token",
    "fcmToken": "fcm_token_or_null",
    "redirectUrl": "/vendor/dashboard"
  }
}
```

### 2. Admin Login API
**Endpoint:** `POST /api/v1/auth/admin/login`

**Changes Made:**
- Now returns `fcmToken` in the response data
- Returns `null` if no FCM token is stored for the user

**Response Format:**
```json
{
  "message": "Login successful",
  "success": true,
  "data": {
    "id": "admin_id",
    "email": "<EMAIL>",
    "fcmToken": "fcm_token_or_null",
    "redirectUrl": "/admin/dashboard",
    "token": "jwt_token"
  }
}
```

## Profile APIs with FCM Token

### 3. Vendor Profile API
**Endpoint:** `GET /api/v1/vendor/profile`

**Changes Made:**
- Now includes `fcmToken` in the response data

### 4. Admin Profile API (New)
**Endpoint:** `GET /api/v1/admin/profile`

**Response Format:**
```json
{
  "message": "Profile fetched successfully",
  "success": true,
  "data": {
    "id": "admin_id",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "admin",
    "isActive": true,
    "fcmToken": "fcm_token_or_null",
    "lastLoginAt": "2025-01-15T10:30:00Z",
    "createdAt": "2025-01-01T00:00:00Z",
    "updatedAt": "2025-01-15T10:30:00Z"
  }
}
```

## In-App Notification APIs

### 5. Vendor Notifications
**Endpoint:** `GET /api/v1/vendor/notifications`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)

**Response Format:**
```json
{
  "message": "Notifications fetched successfully",
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "notification_id",
        "title": "Order Approved",
        "type": "order_status",
        "desc": "Your order no. 123 has been approved.",
        "createdAt": "12-03-2025",
        "isRead": false,
        "order_id": "912hi9121292912"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalCount": 100,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 6. Admin Notifications
**Endpoint:** `GET /api/v1/admin/notifications`

**Same format as vendor notifications**

### 7. Mark Single Notification as Read
**Endpoint:** `PATCH /api/v1/vendor/notifications` or `PATCH /api/v1/admin/notifications`

**Request Body:**
```json
{
  "notificationId": "notification_id"
}
```

### 8. Get Unread Notification Count
**Vendor:** `GET /api/v1/vendor/notifications/unread-count`
**Admin:** `GET /api/v1/admin/notifications/unread-count`

**Response Format:**
```json
{
  "message": "Unread notification count fetched successfully",
  "success": true,
  "data": {
    "unreadCount": 5
  }
}
```

### 9. Mark All Notifications as Read
**Vendor:** `PATCH /api/v1/vendor/notifications/mark-all-read`
**Admin:** `PATCH /api/v1/admin/notifications/mark-all-read`

**Response Format:**
```json
{
  "message": "All notifications marked as read",
  "success": true,
  "data": {
    "updatedCount": 10
  }
}
```

### 10. Create Notification (Admin Only)
**Endpoint:** `POST /api/v1/admin/notifications`

**Request Body:**
```json
{
  "title": "Order Approved",
  "body": "Your order no. 123 has been approved.",
  "type": "ORDER_STATUS_CHANGED",
  "recipientId": "vendor_id", // Optional: null for all users of that type
  "recipientType": "vendor", // "vendor" or "admin"
  "data": {
    "order_id": "912hi9121292912"
  }
}
```

## Notification Types

The system supports the following notification types:
- `VENDOR_REGISTRATION`
- `ORDER_CREATED`
- `ORDER_STATUS_CHANGED`
- `ACCOUNT_STATUS_CHANGED`
- `PAYMENT_RECEIVED`
- `GENERAL`

## Notification Helper Functions

A utility library (`/lib/notifications.ts`) provides helper functions:

```typescript
// Create order notification
await createOrderNotification({
  orderId: "order_id",
  vendorId: "vendor_id",
  title: "Order Approved",
  body: "Your order no. 123 has been approved.",
  type: "ORDER_STATUS_CHANGED"
});

// Create vendor registration notification
await createVendorRegistrationNotification({
  vendorId: "vendor_id",
  vendorName: "Vendor Name"
});

// Create account status notification
await createAccountStatusNotification({
  vendorId: "vendor_id",
  status: "ACTIVE"
});

// Create payment notification
await createPaymentNotification({
  vendorId: "vendor_id",
  amount: 1000,
  orderId: "order_id"
});

// Create general notification
await createGeneralNotification({
  title: "System Maintenance",
  body: "System will be down for maintenance.",
  recipientType: "vendor",
  recipientId: null // null for all vendors
});
```

## Frontend Integration

A React component (`/components/notifications/NotificationList.tsx`) is provided as an example of how to integrate these APIs in the frontend.

## Example Notification Formats

### Without Navigation:
```json
{
  "title": "Order Approved",
  "type": "order_status",
  "desc": "Your order no. 123 has been approved.",
  "createdAt": "12-03-2025"
}
```

### With Navigation:
```json
{
  "title": "Order Approved",
  "type": "order_status",
  "order_id": "912hi9121292912",
  "desc": "Your order no. 123 has been approved.",
  "createdAt": "12-03-2025"
}
```

## Authentication

All notification APIs require authentication via JWT token in cookies. The APIs automatically determine the user type (admin/vendor) from the token and filter notifications accordingly.
