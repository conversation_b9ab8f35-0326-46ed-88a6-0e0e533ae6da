"use client";
import { useState } from "react";
import { useForm } from "react-hook-form";
import axios from "axios";
import { adminAPI as api } from "@/lib/axios";
import { toast } from "react-toastify";

import { X } from "lucide-react";
import ImageUploaderNoCrop from "./ImageUploaderNoCrop";

interface AddQRCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface FormData {
  name: string;
  isActive: boolean;
}

export default function AddQRCodeModal({
  isOpen,
  onClose,
  onSuccess,
}: AddQRCodeModalProps) {
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      name: "",
      isActive: true,
    },
  });

  const handleImagesUploaded = (urls: string[]) => {
    setUploadedImages(urls);
  };

  const onSubmit = async (data: FormData) => {
    if (uploadedImages.length === 0) {
      toast.error("Please upload a QR code image");
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await api.post("/payment-qrcode", {
        name: data.name,
        imageUrl: uploadedImages[0],
        isActive: data.isActive,
      });

      if (response.data.success) {
        reset();
        setUploadedImages([]);
        onSuccess();
      } else {
        toast.error("Failed to add payment QR code");
      }
    } catch (error) {
      console.error("Error adding payment QR code:", error);

      // Extract error message from the response if available
      let errorMessage = "Failed to add payment QR code";
      if (axios.isAxiosError(error) && error.response?.data) {
        const responseData = error.response.data;
        errorMessage = responseData.error || responseData.message || errorMessage;
        console.error("API error details:", responseData);
      }

      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    setUploadedImages([]);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">Add Payment QR Code</h2>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="p-4 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              QR Code Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              {...register("name", { required: "QR code name is required" })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isSubmitting}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              QR Code Image <span className="text-red-500">*</span>
            </label>
          
             <ImageUploaderNoCrop
              existingImages={uploadedImages}
          onImagesUploaded={handleImagesUploaded}
          multiple={false}
          maxFiles={1}
        />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="isActive"
              {...register("isActive")}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
              Active
            </label>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Add QR Code"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
