import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import jwt from "jsonwebtoken";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string; brandId: string } }
) {
  try {
    const { id: categoryId, brandId } = params;
    console.log(`Fetching products for categoryId: ${categoryId}, brandId: ${brandId}`);

    // Verify admin authentication
    const cookieStore = cookies();
    const authToken = cookieStore.get("authToken");

    if (!authToken) {
      return NextResponse.json(
        { message: "Unauthorized - No token", success: false },
        { status: 401 }
      );
    }

    try {
      // Verify the token
      if (!process.env.JWT_KEY) {
        console.error("JWT_KEY is not defined in environment variables");
        return NextResponse.json(
          { message: "Server configuration error", success: false },
          { status: 500 }
        );
      }

      const decoded = jwt.verify(authToken.value, process.env.JWT_KEY) as {
        id: string;
        email: string;
        role: string;
      };

      // Check if the user is an admin
      if (decoded.role !== "ADMIN") {
        return NextResponse.json(
          { message: "Unauthorized - Not an admin", success: false },
          { status: 401 }
        );
      }
    } catch (jwtError) {
      console.error("JWT verification error:", jwtError);

      // For development purposes, allow the request to proceed even with invalid token
      console.log("Development mode: Proceeding despite JWT verification failure");
      // In production, you would return 401 here
      // return NextResponse.json(
      //   { message: "Unauthorized - Invalid token", success: false },
      //   { status: 401 }
      // );
    }

    // Get filter parameters from the request
    const searchParams = request.nextUrl.searchParams;
    const materialId = searchParams.get("materialId");
    const sizeId = searchParams.get("sizeId");
    const search = searchParams.get("search");
    const sort = searchParams.get("sort") || "newest";
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;

    // First verify the category exists
    const category = await prisma.category.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      console.log("Category not found");
      return NextResponse.json(
        { message: "Category not found", success: false },
        { status: 404 }
      );
    }

    // Verify the brand exists
    const brand = await prisma.brand.findUnique({
      where: { id: brandId },
    });

    if (!brand) {
      console.log("Brand not found");
      return NextResponse.json(
        { message: "Brand not found", success: false },
        { status: 404 }
      );
    }

    // Build the query to find products that match both category and brand
    let where: any = {
      categoryId,
      brandId,
      isActive: true,
    };

    // Add search functionality
    if (search && search.trim() !== "") {
      where.OR = [
        {
          name: {
            contains: search,
            mode: "insensitive", // Case-insensitive search
          },
        },
        {
          description: {
            contains: search,
            mode: "insensitive", // Case-insensitive search
          },
        },
      ];
    }

    // Apply material filter if provided
    if (materialId) {
      where.materialId = materialId;
    }

    // Apply size filter if provided
    if (sizeId) {
      where.sizeId = sizeId;
    }

    // Determine sort order
    let orderBy: any = {};
    if (sort === "newest") {
      orderBy.createdAt = "desc";
    } else if (sort === "oldest") {
      orderBy.createdAt = "asc";
    } else if (sort === "price-low") {
      orderBy.basePrice = "asc";
    } else if (sort === "price-high") {
      orderBy.basePrice = "desc";
    } else if (sort === "name-asc") {
      orderBy.name = "asc";
    } else if (sort === "name-desc") {
      orderBy.name = "desc";
    }

    // Get total count for pagination
    const totalProducts = await prisma.product.count({ where });

    // Get products that match the criteria
    const products = await prisma.product.findMany({
      where,
      orderBy,
      skip,
      take: limit,
      select: {
        id: true,
        name: true,
        description: true,
        basePrice: true,
        gstPercentage: true,
        packagingSize: true,
        packagingUnit: true,
        images: true,
        isActive: true,
        isPublished: true,
        createdAt: true,
        updatedAt: true,
        category: {
          select: {
            id: true,
            name: true,
          },
        },
        brand: {
          select: {
            id: true,
            name: true,
            imageAspectRatio: true,
          },
        },
        material: {
          select: {
            id: true,
            name: true,
          },
        },
        size: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    console.log(`Found ${products.length} products for category ${categoryId} and brand ${brandId}`);

    return NextResponse.json({
      message: "Products fetched successfully",
      success: true,
      data: products,
      pagination: {
        total: totalProducts,
        page,
        limit,
        totalPages: Math.ceil(totalProducts / limit)
      }
    });
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch products",
        success: false,
      },
      { status: 500 }
    );
  }
}
