import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const categoryId = params.id;
    console.log("Fetching brands for categoryId:", categoryId);

    // Get filter parameters from the request
    const searchParams = request.nextUrl.searchParams;
    const minPrice = searchParams.get("minPrice");
    const maxPrice = searchParams.get("maxPrice");
    const materialId = searchParams.get("materialId");
    const sizeId = searchParams.get("sizeId");
    const sort = searchParams.get("sort") || 'asc' // Default to price high to low

    // CRITICAL DEBUG: Log all filter parameters
    console.log("Received filter parameters:", {
      minPrice,
      maxPrice,
      materialId,
      sizeId,
      sort
    });

    // Check if materialId is present and valid
    if (materialId) {
      console.log(`Received materialId: ${materialId}`);

      // Verify the material exists
      const material = await prisma.material.findUnique({
        where: { id: materialId }
      });

      if (material) {
        console.log(`Found material: ${material.name} (ID: ${material.id})`);
      } else {
        console.log(`WARNING: Material with ID ${materialId} not found in database`);
      }
    }

    // Check if sizeId is present and valid
    if (sizeId) {
      console.log(`Received sizeId: ${sizeId}`);

      // Verify the size exists
      const size = await prisma.size.findUnique({
        where: { id: sizeId }
      });

      if (size) {
        console.log(`Found size: ${size.name} (ID: ${size.id})`);
      } else {
        console.log(`WARNING: Size with ID ${sizeId} not found in database`);
      }
    }

    // First verify the category exists
    const category = await prisma.category.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      console.log("Category not found");
      return NextResponse.json(
        { message: "Category not found", success: false },
        { status: 404 }
      );
    }
    console.log("Query conditions:", { categoryId, minPrice, maxPrice, materialId, sizeId, sort });

    // First, get all brands in this category
    const brandsInCategory = await prisma.brand.findMany({
      where: {
        isActive: true,
        products: {
          some: {
            categoryId,
            isActive: true,
            isPublished: true,
          }
        }
      },
      select: {
        id: true,
        name: true,
        materialId: true,
        sizeId: true,
        imageAspectRatio: true,
      }
    });

    console.log("Brands in category with their material/size info:", brandsInCategory.map(b => ({
      id: b.id,
      name: b.name,
      materialId: b.materialId,
      sizeId: b.sizeId
    })));

    console.log(`Found ${brandsInCategory.length} brands in category ${categoryId}`);

    // Apply material and size filters directly to brands
    let filteredBrandIds = brandsInCategory.map(b => b.id);

    // If material filter is provided, filter brands by materialId
    if (materialId) {
      console.log("Applying material filter with ID:", materialId);
      // Get brands that have this material ID
      const brandsWithMaterial = brandsInCategory.filter(b => b.materialId === materialId).map(b => b.id);
      console.log(`Found ${brandsWithMaterial.length} brands with material ID ${materialId}`);

      // If no brands have this material, try to find products with this material
      if (brandsWithMaterial.length === 0) {
        console.log("No brands with this material ID, checking products...");
        const productsWithMaterial = await prisma.product.findMany({
          where: {
            categoryId,
            materialId,
            isActive: true,
            isPublished: true,
          },
          select: {
            brandId: true,
          },
          distinct: ['brandId'],
        });

        const brandIdsFromProducts = productsWithMaterial.map(p => p.brandId).filter(Boolean) as string[];
        console.log(`Found ${brandIdsFromProducts.length} brands from products with material ID ${materialId}`);

        // Use these brand IDs
        filteredBrandIds = brandIdsFromProducts;
      } else {
        filteredBrandIds = brandsWithMaterial;
      }
    }

    // If size filter is provided, further filter the brands
    if (sizeId) {
      console.log("Applying size filter with ID:", sizeId);

      // If we already filtered by material, we need to find the intersection
      if (materialId) {
        // Get brands that have this size ID from the already filtered brands
        const brandsWithSize = brandsInCategory
          .filter(b => filteredBrandIds.includes(b.id) && b.sizeId === sizeId)
          .map(b => b.id);

        console.log(`Found ${brandsWithSize.length} brands with both material ID ${materialId} and size ID ${sizeId}`);

        // Always check products with both material and size
        console.log("Checking products with both material and size...");
        const productsWithBoth = await prisma.product.findMany({
          where: {
            categoryId,
            materialId,
            sizeId,
            isActive: true,
            isPublished: true,
          },
          select: {
            brandId: true,
          },
          distinct: ['brandId'],
        });

        const brandIdsFromProducts = productsWithBoth.map(p => p.brandId).filter(Boolean) as string[];
        console.log(`Found ${brandIdsFromProducts.length} brands from products with both material ID ${materialId} and size ID ${sizeId}`);

        // Combine both sets of brand IDs (from brands and products)
        const combinedBrandIds = Array.from(new Set([...brandsWithSize, ...brandIdsFromProducts]));
        console.log(`Combined total of ${combinedBrandIds.length} unique brands with both material ID ${materialId} and size ID ${sizeId}`);

        // Use these combined brand IDs
        filteredBrandIds = combinedBrandIds;
      } else {
        // If we haven't filtered by material, just filter by size
        // First, check brands that have this size ID directly
        const brandsWithSize = brandsInCategory.filter(b => b.sizeId === sizeId).map(b => b.id);
        console.log(`Found ${brandsWithSize.length} brands with size ID ${sizeId} directly set`);

        // Always check products with this size, regardless of whether brands have the size
        console.log("Checking products with this size ID...");
        const productsWithSize = await prisma.product.findMany({
          where: {
            categoryId,
            sizeId,
            isActive: true,
            isPublished: true,
          },
          select: {
            brandId: true,
          },
          distinct: ['brandId'],
        });

        const brandIdsFromProducts = productsWithSize.map(p => p.brandId).filter(Boolean) as string[];
        console.log(`Found ${brandIdsFromProducts.length} brands from products with size ID ${sizeId}`);

        // Combine both sets of brand IDs (from brands and products)
        const combinedBrandIds = Array.from(new Set([...brandsWithSize, ...brandIdsFromProducts]));
        console.log(`Combined total of ${combinedBrandIds.length} unique brands with size ID ${sizeId}`);

        // Use these combined brand IDs
        filteredBrandIds = combinedBrandIds;
      }
    }

    console.log(`After all filters, found ${filteredBrandIds.length} matching brand IDs`);

    // Build the where clause for brands
    let where: any = {
      id: {
        in: filteredBrandIds,
      },
      isActive: true,
    };

    // Add price filter if provided
    if (minPrice || maxPrice) {
      where.price = {};

      if (minPrice) {
        where.price.gte = parseFloat(minPrice);
      }

      if (maxPrice) {
        where.price.lte = parseFloat(maxPrice);
      }

      // Only exclude null prices when filtering by price
      where.price.not = null;
    }

    // We're now filtering by material/size at the product level
    // So we don't need to filter brands by material/size directly

    // Build the orderBy clause based on sort parameter
    let orderBy: any = {};
    console.log("Building orderBy for sort value:", sort);

    // Make sure we have a valid sort parameter
    const validSort = ["name_asc", "name_desc", "price_asc", "price_desc"].includes(sort)
      ? sort
      : "price_desc";

    console.log("Using sort parameter:", validSort);

    // Apply the appropriate sorting
    switch (validSort) {
      case "name_asc":
        orderBy = { name: "asc" };
        console.log("Sorting by name ascending (A-Z)");
        break;
      case "name_desc":
        orderBy = { name: "desc" };
        console.log("Sorting by name descending (Z-A)");
        break;
      case "price_asc":
        // For price sorting, we'll do manual post-processing
        // Just get the data without sorting in the query
        orderBy = { name: "asc" }; // Default sort by name
        console.log("Will sort by price ascending (low to high) after query");
        break;
      case "price_desc":
      default:
        // For price sorting, we'll do manual post-processing
        // Just get the data without sorting in the query
        orderBy = { name: "desc" }; // Default sort by name
        console.log("Will sort by price descending (high to low) after query");
        break;
    }

    console.log("Query conditions:", { where, orderBy });

    // Get brand details with filters applied
    // Include products to help with client-side filtering
    let brands = await prisma.brand.findMany({
      where,
      orderBy,
      select: {
        id: true,
        name: true,
        description: true,
        imageUrl: true,
        imageAspectRatio: true,
        packagingSizeImage: true,
        price: true,
        materialId: true,
        sizeId: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    });

    // CRITICAL FIX: Perform exact matching for size and material
    if (sizeId || materialId) {
      console.log("Performing exact matching for size and material");

      // Log all brands with their material and size IDs before filtering
      console.log("Brands before filtering:", brands.map(brand => ({
        id: brand.id,
        name: brand.name,
        materialId: brand.materialId,
        sizeId: brand.sizeId
      })));

      brands = brands.filter(brand => {
        let matchesSize = true;
        let matchesMaterial = true;

        if (sizeId) {
          // Exact match for size - must be exactly the same string
          matchesSize = brand.sizeId === sizeId;
          console.log(`Brand ${brand.name} size check: brand.sizeId=${brand.sizeId}, filter sizeId=${sizeId}, matches=${matchesSize}`);
        }

        if (materialId) {
          // Exact match for material - must be exactly the same string
          matchesMaterial = brand.materialId === materialId;
          console.log(`Brand ${brand.name} material check: brand.materialId=${brand.materialId}, filter materialId=${materialId}, matches=${matchesMaterial}`);

          // If it doesn't match, check if the materialId is null or undefined
          if (!matchesMaterial) {
            if (brand.materialId === null || brand.materialId === undefined) {
              console.log(`Brand ${brand.name} has null/undefined materialId`);
            } else {
              console.log(`Brand ${brand.name} materialId type: ${typeof brand.materialId}, filter materialId type: ${typeof materialId}`);

              // Try string comparison as a fallback
              const stringMatch = String(brand.materialId) === String(materialId);
              console.log(`String comparison result: ${stringMatch}`);
            }
          }
        }

        return matchesSize && matchesMaterial;
      });

      // Log all brands that passed the filter
      console.log("Brands after filtering:", brands.map(brand => ({
        id: brand.id,
        name: brand.name,
        materialId: brand.materialId,
        sizeId: brand.sizeId
      })));

      console.log(`After exact matching, ${brands.length} brands remain`);
    }

    // Log the brands with their material and size IDs for debugging
    console.log("Brands with material/size info:", brands.map(b => ({
      id: b.id,
      name: b.name,
      materialId: b.materialId,
      sizeId: b.sizeId
    })));

    // Manual post-processing for sorting if needed
    if (validSort === "price_desc") {
      // Sort brands with non-null prices first (high to low), then brands with null prices
      brands = brands.sort((a, b) => {
        // If both have prices, sort by price (high to low)
        if (a.price !== null && b.price !== null) {
          return b.price - a.price;
        }
        // If only a has price, a comes first
        if (a.price !== null && b.price === null) {
          return -1;
        }
        // If only b has price, b comes first
        if (a.price === null && b.price !== null) {
          return 1;
        }
        // If both have null prices, sort by name
        return a.name.localeCompare(b.name);
      });
    } else if (validSort === "price_asc") {
      // Sort brands with non-null prices first (low to high), then brands with null prices
      brands = brands.sort((a, b) => {
        // If both have prices, sort by price (low to high)
        if (a.price !== null && b.price !== null) {
          return a.price - b.price;
        }
        // If only a has price, a comes first
        if (a.price !== null && b.price === null) {
          return -1;
        }
        // If only b has price, b comes first
        if (a.price === null && b.price !== null) {
          return 1;
        }
        // If both have null prices, sort by name
        return a.name.localeCompare(b.name);
      });
    }

    console.log(`Found ${brands.length} brands for category ${categoryId} with filters applied`);

    return NextResponse.json({
      message: "Brands fetched successfully",
      success: true,
      data: brands,
    });
  } catch (error) {
    console.error("Error fetching brands by category:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch brands",
        success: false,
      },
      { status: 500 }
    );
  }
}
