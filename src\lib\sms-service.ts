import axios from "axios";

/**
 * SMS Service (Mock)
 * This utility provides mock functions for SMS functionality
 * In a production environment, this would be replaced with actual SMS sending logic
 */

/**
 * Mock function for sending OTP via SMS
 * In development, this just logs the OTP to the console
 *
 * @param phone Phone number to send OTP to
 * @param otp OTP code
 * @returns Promise that resolves to a success object
 */
export async function sendOtpSMS(phone: string, otp: string) {
  try {
    const apiKey = process.env.SMS_API_KEY || '6107e534b1415';
    const sender = process.env.SMS_SENDER_ID || 'MANAAS';
    const message = `Your OTP for Metropolis login is ${otp}. Valid for 5 minutes. Do not share this OTP with anyone.`;

    const apiUrl = `https://www.mysmsapp.in/api/push.json?apikey=${apiKey}&sender=${sender}&mobileno=${phone}&text=${encodeURIComponent(message)}`;

    const response = await axios.get(apiUrl);

    if (response.data && response.data.success) {
      console.log(`[SMS] OTP ${otp} sent to ${phone}: ${response.data.message}`);
      return {
        success: true,
        message: 'OTP sent successfully',
      };
    } else {
      throw new Error(response.data.message || 'Failed to send OTP');
    }
  } catch (error) {
    console.error('Error sending OTP:', error);
    return {
      success: false,
      message: 'Failed to send OTP',
      error: error instanceof Error ? error.message : String(error),
    };
  }
}
