import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

// Interface for creating a sub-banner
interface CreateSubBannerRequest {
  imageUrl: string;
  name: string;
  isActive?: boolean;
}

// GET all sub-banners
export async function GET() {
  try {
    const subBanners = await prisma.subBanner.findMany({
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({
      message: "Sub-banners fetched successfully",
      success: true,
      data: subBanners,
    });
  } catch (error) {
    console.error("Error fetching sub-banners:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch sub-banners",
        success: false,
      },
      { status: 500 }
    );
  }
}

// POST a new sub-banner
export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as CreateSubBannerRequest;
    const { imageUrl, name, isActive = true } = body;

    // Validate required fields
    if (!imageUrl || !name) {
      return NextResponse.json(
        {
          message: "Image URL and name are required",
          success: false,
        },
        { status: 400 }
      );
    }

    const subBanner = await prisma.subBanner.create({
      data: {
        imageUrl,
        name,
        isActive,
      },
    });

    return NextResponse.json({
      message: "Sub-banner created successfully",
      success: true,
      data: subBanner,
    });
  } catch (error) {
    console.error("Sub-banner creation error:", error);
    return NextResponse.json(
      {
        message: "Failed to create sub-banner",
        success: false,
      },
      { status: 500 }
    );
  }
}
