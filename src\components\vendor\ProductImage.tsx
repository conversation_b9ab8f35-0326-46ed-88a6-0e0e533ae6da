// src/components/vendor/ProductImage.tsx
import React from 'react';

interface ProductImageProps {
  src: string | null;
  alt: string;
  className?: string;
  priority?: boolean;
  aspectRatio?: string; // "1:1" or "9:16"
}

/**
 * A reusable component for displaying product images with a consistent aspect ratio
 * This component ensures that all product images are displayed in the same way
 * across the application, maintaining the aspect ratio.
 */
const ProductImage: React.FC<ProductImageProps> = ({
  src,
  alt,
  className = '',
  priority = false,
  aspectRatio = '9:16'
}) => {
  // Function to handle image URLs
  const getImageUrl = (url: string | null) => {
    if (!url) return "/placeholder.png";
    if (url.startsWith("http://") || url.startsWith("https://")) {
      return url;
    }
    return `${process.env.NEXT_PUBLIC_API_URL || ""}${
      url.startsWith("/") ? "" : "/"
    }${url}`;
  };

  return (
    <div className={`relative w-full h-full ${className}`}>
      {src ? (
        <div
          className="w-full h-full flex items-center justify-center bg-white"
          style={{
            aspectRatio: aspectRatio === '1:1' ? '1 / 1' : '9 / 16',
          }}
        >
          <img
            src={getImageUrl(src)}
            alt={alt}
            className="max-h-full w-auto h-auto object-contain"
            style={{ maxWidth: '100%' }}
            loading={priority ? "eager" : "lazy"}
          />
        </div>
      ) : (
        <div
          className="w-full h-full bg-gray-200 flex items-center justify-center"
          style={{
            aspectRatio: aspectRatio === '1:1' ? '1 / 1' : '9 / 16',
          }}
        >
          <span className="text-gray-400">No image</span>
        </div>
      )}
    </div>
  );
};

export default ProductImage;
