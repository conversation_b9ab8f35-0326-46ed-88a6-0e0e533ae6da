"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { adminAPI as api } from "@/lib/axios";
import ImageUploaderNoCrop from "@/components/admin/ImageUploaderNoCrop";

interface Material {
  id: string;
  name: string;
  isActive: boolean;
}

interface Size {
  id: string;
  name: string;
  isActive: boolean;
}

const brandSchema = yup.object({
  name: yup.string().required("Sub Category name is required"),
  imageAspectRatio: yup.string().required("Image aspect ratio is required"),
  price: yup.string(),
  materialId: yup.string(),
  sizeId: yup.string(),
  isActive: yup.boolean(),
});

type BrandFormData = yup.InferType<typeof brandSchema>;

export default function EditBrandPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [sizes, setSizes] = useState<Size[]>([]);
  const [loadingMaterials, setLoadingMaterials] = useState(false);
  const [loadingSizes, setLoadingSizes] = useState(false);
  const params = useParams();
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
    watch,
  } = useForm<BrandFormData>({
    resolver: yupResolver(brandSchema),
  });

  // Watch the imageAspectRatio field to update the ImageUploader
  const selectedAspectRatio = watch("imageAspectRatio");

  useEffect(() => {
    const fetchBrand = async () => {
      try {
        setLoading(true);
        const { data } = await api.get(`/brands/${params.id}`);
        if (data.success) {
          reset({
            name: data.data.name,
            imageAspectRatio: data.data.imageAspectRatio || "1:1",
            price: data.data.price ? data.data.price.toString() : "",
            materialId: data.data.materialId || "",
            sizeId: data.data.sizeId || "",
            isActive: data.data.isActive,
          });

          // Set uploaded images if there's an image URL
          if (data.data.imageUrl) {
            setUploadedImages([data.data.imageUrl]);
          }
        } else {
          setError(data.message || "Failed to load sub category data");
          toast.error(data.message || "Failed to load sub category data");
        }
      } catch (err: any) {
        const errorMessage =
          err.response?.data?.message || "Failed to load sub category data";
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    const fetchMaterials = async () => {
      try {
        setLoadingMaterials(true);
        const { data } = await api.get("/materials");
        if (data.success) {
          setMaterials(data.data);
        } else {
          toast.error("Failed to fetch materials");
        }
      } catch (error) {
        console.error("Error fetching materials:", error);
        toast.error("Failed to fetch materials");
      } finally {
        setLoadingMaterials(false);
      }
    };

    const fetchSizes = async () => {
      try {
        setLoadingSizes(true);
        const { data } = await api.get("/sizes");
        if (data.success) {
          setSizes(data.data);
        } else {
          toast.error("Failed to fetch sizes");
        }
      } catch (error) {
        console.error("Error fetching sizes:", error);
        toast.error("Failed to fetch sizes");
      } finally {
        setLoadingSizes(false);
      }
    };

    if (params.id) {
      fetchBrand();
      fetchMaterials();
      fetchSizes();
    }
  }, [params.id, reset]);

  const handleImagesUploaded = (imageUrls: string[]) => {
    setUploadedImages(imageUrls);
  };

  const onSubmit = async (data: BrandFormData) => {
    try {
      const response = await api.put(`/brands/${params.id}`, {
        ...data,
        price: data.price ? parseFloat(data.price) : undefined,
        materialId: data.materialId || undefined,
        sizeId: data.sizeId || undefined,
        imageUrl: uploadedImages.length > 0 ? uploadedImages[0] : undefined,
        imageAspectRatio: data.imageAspectRatio,
      });

      if (response.data.success) {
        toast.success("Sub Category updated successfully");
        router.push("/admin/brands");
      } else {
        throw new Error(response.data.message || "Failed to update sub category");
      }
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.message || "Failed to update sub category";
      setError(errorMessage);
      toast.error(errorMessage);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <ToastContainer />
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-lg">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6">
      <ToastContainer />
      <h1 className="text-2xl font-bold mb-6">Edit Sub Category</h1>
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
          {error}
        </div>
      )}

      <form
        onSubmit={handleSubmit(onSubmit)}
        className="space-y-6 bg-white p-6 rounded-lg shadow"
      >
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Sub Category Name <span className="text-red-500">*</span>
          </label>
          <input
            {...register("name")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="Enter sub category name"
          />
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Price
          </label>
          <input
            {...register("price")}
            type="text"
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="Enter price (optional)"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Material
          </label>
          <select
            {...register("materialId")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="">Select Material (optional)</option>
            {materials.map((material) => (
              <option key={material.id} value={material.id}>
                {material.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Size
          </label>
          <select
            {...register("sizeId")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="">Select Size (optional)</option>
            {sizes.map((size) => (
              <option key={size.id} value={size.id}>
                {size.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Image Aspect Ratio <span className="text-red-500">*</span>
          </label>
          <select
            {...register("imageAspectRatio")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="1:1">Square (1:1)</option>
            <option value="9:16">Portrait (9:16)</option>
          </select>
          <p className="mt-1 text-xs text-gray-500">
            This aspect ratio will be used for products in this sub category
          </p>
          {errors.imageAspectRatio && (
            <p className="text-red-500 text-sm mt-1">{errors.imageAspectRatio.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Sub Category Image <span className="text-red-500">*</span>
          </label>
          <ImageUploaderNoCrop
            onImagesUploaded={handleImagesUploaded}
            multiple={false}
            maxFiles={1}
            existingImages={uploadedImages}
            imageAspectRatio="1:1"
          />
          <p className="mt-1 text-xs text-gray-500">
            Sub category images will always be uploaded in 1:1 ratio (1000x1000px)
          </p>
          <p className="mt-1 text-xs text-gray-500">
            The selected aspect ratio will be used for products in this sub category
          </p>
        </div>

        <div>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              {...register("isActive")}
              className="rounded"
            />
            <span className="text-sm font-medium text-gray-700">Active</span>
          </label>
        </div>

        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-4 py-2 border rounded-md hover:bg-gray-100"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300"
          >
            {isSubmitting ? "Updating..." : "Update Sub Category"}
          </button>
        </div>
      </form>
    </div>
  );
}
