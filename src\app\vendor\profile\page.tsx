"use client";
import { useState, useEffect } from "react";
import api from "@/lib/axios";

interface VendorProfileData {
  id: string;
  email: string;
  businessName: string;
  businessType: string;
  taxId: string;
  status: string;
  registrationNumber?: string;
  yearEstablished?: number;
  annualRevenue?: number;
  contactPerson: string;
  phoneNumber: string;
  alternatePhone?: string;
  address: string;
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  bankName?: string;
  bankAccountNo?: string;
  ifscCode?: string;
}

export default function VendorProfile() {
  const [profile, setProfile] = useState<VendorProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      const { data } = await api.get("/vendor/profile");
      if (data.success) {
        setProfile(data.data);
      }
    } catch (error) {
      console.error("Error fetching profile:", error);
      setError("Failed to load profile");
    } finally {
      setLoading(false);
    }
  };

  async (formData: FormData) => {
    try {
      setLoading(true);
      const { data } = await api.put("/vendor/profile", formData);
      if (data.success) {
        setProfile(data.data);
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      setError("Failed to update profile");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div className="text-red-500">{error}</div>;
  }

  if (!profile) {
    return <div>No profile data found</div>;
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Dealer Profile</h1>

      <div className="bg-white rounded-lg shadow p-6 space-y-6">
        <div className="grid grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Business Name
            </label>
            <p className="mt-1 text-gray-900">{profile.businessName}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Business Type
            </label>
            <p className="mt-1 text-gray-900">{profile.businessType}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Tax ID
            </label>
            <p className="mt-1 text-gray-900">{profile.taxId}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Status
            </label>
            <p className="mt-1 text-gray-900">{profile.status}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Contact Person
            </label>
            <p className="mt-1 text-gray-900">{profile.contactPerson}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Phone Number
            </label>
            <p className="mt-1 text-gray-900">{profile.phoneNumber}</p>
          </div>
        </div>

        <div className="border-t pt-6">
          <h2 className="text-lg font-semibold mb-4">Address Information</h2>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Address
              </label>
              <p className="mt-1 text-gray-900">{profile.address}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Street
              </label>
              <p className="mt-1 text-gray-900">{profile.street}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                City
              </label>
              <p className="mt-1 text-gray-900">{profile.city}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                State
              </label>
              <p className="mt-1 text-gray-900">{profile.state}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Postal Code
              </label>
              <p className="mt-1 text-gray-900">{profile.postalCode}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Country
              </label>
              <p className="mt-1 text-gray-900">{profile.country}</p>
            </div>
          </div>
        </div>

        {profile.bankName && (
          <div className="border-t pt-6">
            <h2 className="text-lg font-semibold mb-4">Bank Information</h2>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Bank Name
                </label>
                <p className="mt-1 text-gray-900">{profile.bankName}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Account Number
                </label>
                <p className="mt-1 text-gray-900">{profile.bankAccountNo}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  IFSC Code
                </label>
                <p className="mt-1 text-gray-900">{profile.ifscCode}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
