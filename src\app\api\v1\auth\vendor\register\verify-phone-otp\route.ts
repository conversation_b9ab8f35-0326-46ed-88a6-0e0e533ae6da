import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const { phoneNumber, otp, vendorId } = await request.json();

    // Validate inputs
    if (!phoneNumber || !otp) {
      return NextResponse.json(
        { message: "Phone number and OTP are required", success: false },
        { status: 400 }
      );
    }

    // Find the latest unused OTP for this phone number
    const otpRecord = await prisma.otp.findFirst({
      where: {
        email: phoneNumber, // Using email field to store phone number for OTP
        code: otp,
        isUsed: false,
        expiresAt: {
          gt: new Date(),
        },
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!otpRecord) {
      return NextResponse.json(
        { message: "Invalid or expired OTP", success: false },
        { status: 401 }
      );
    }

    // Mark <PERSON> as used
    await prisma.otp.update({
      where: { id: otpRecord.id },
      data: { isUsed: true },
    });

    // If vendorId is provided, update the vendor record to mark phone as verified
    if (vendorId) {
      await prisma.vendor.update({
        where: { id: vendorId },
        data: { isPhoneVerified: true },
      });
    }

    return NextResponse.json({
      message: "Phone number verified successfully",
      success: true,
      data: {
        isVerified: true,
      },
    });
  } catch (error) {
    console.error("Phone OTP verification error:", error);
    return NextResponse.json(
      { message: "An error occurred during verification", success: false },
      { status: 500 }
    );
  }
}
