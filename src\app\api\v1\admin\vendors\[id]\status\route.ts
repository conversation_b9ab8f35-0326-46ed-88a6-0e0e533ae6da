import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { NotificationType } from "@prisma/client";
import { createAndSendNotification } from "@/lib/notification-service";

/**
 * Update vendor status
 * PUT /api/v1/admin/vendors/:id/status
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const { status } = await request.json();

    if (!status) {
      return NextResponse.json(
        { message: "Status is required", success: false },
        { status: 400 }
      );
    }

    // Check if vendor exists
    const existingVendor = await prisma.vendor.findUnique({
      where: { id },
    });

    if (!existingVendor) {
      return NextResponse.json(
        { message: "Vendor not found", success: false },
        { status: 404 }
      );
    }

    // Update vendor status
    const vendor = await prisma.vendor.update({
      where: { id },
      data: { status },
    });

    // Send notification to vendor about status change
    try {
      let notificationTitle = "Account Status Updated";
      let notificationBody = "";

      switch (status) {
        case "ACTIVE":
          notificationTitle = "Account Activated";
          notificationBody = "Your vendor account has been approved and activated. You can now log in and start using the platform.";
          break;
        case "SUSPENDED":
          notificationTitle = "Account Suspended";
          notificationBody = "Your vendor account has been suspended. Please contact support for more information.";
          break;
        case "INACTIVE":
          notificationTitle = "Account Deactivated";
          notificationBody = "Your vendor account has been deactivated. Please contact support for more information.";
          break;
        default:
          notificationBody = `Your account status has been updated to ${status}.`;
      }

      await createAndSendNotification(
        notificationTitle,
        notificationBody,
        NotificationType.ACCOUNT_STATUS_CHANGED,
        vendor.id,
        "vendor",
        {
          oldStatus: existingVendor.status,
          newStatus: status,
        }
      );
    } catch (notificationError) {
      console.error("Error sending vendor notification:", notificationError);
      // Continue even if notification fails
    }

    return NextResponse.json({
      message: "Vendor status updated successfully",
      success: true,
      data: vendor,
    });
  } catch (error) {
    console.error("Error updating vendor status:", error);
    return NextResponse.json(
      {
        message: "Failed to update vendor status",
        success: false,
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
