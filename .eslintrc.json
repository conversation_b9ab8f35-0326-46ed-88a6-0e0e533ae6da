{"extends": ["next/core-web-vitals", "eslint:recommended"], "rules": {"react/no-unescaped-entities": "off", "react/display-name": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "off", "no-undef": "off", "no-unused-vars": "off", "@next/next/no-img-element": "off"}, "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "globals": {"React": "readonly", "NodeJS": "readonly"}}