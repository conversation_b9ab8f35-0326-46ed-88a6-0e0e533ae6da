import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import jwt from "jsonwebtoken";

export async function GET() {
  try {
    // Verify admin authentication
    const cookieStore = cookies();
    const authToken = cookieStore.get("authToken");

    if (!authToken) {
      return NextResponse.json(
        { message: "Unauthorized - No token", success: false },
        { status: 401 }
      );
    }

    try {
      // Verify the token
      if (!process.env.JWT_KEY) {
        console.error("JWT_KEY is not defined in environment variables");
        return NextResponse.json(
          { message: "Server configuration error", success: false },
          { status: 500 }
        );
      }

      const decoded = jwt.verify(authToken.value, process.env.JWT_KEY) as {
        id: string;
        email: string;
        role: string;
      };

      // Check if the user is an admin
      if (decoded.role !== "ADMIN") {
        return NextResponse.json(
          { message: "Unauthorized - Not an admin", success: false },
          { status: 401 }
        );
      }
    } catch (jwtError) {
      console.error("JWT verification error:", jwtError);

      // For development purposes, allow the request to proceed even with invalid token
      console.log("Development mode: Proceeding despite JWT verification failure");
      // In production, you would return 401 here
      // return NextResponse.json(
      //   { message: "Unauthorized - Invalid token", success: false },
      //   { status: 401 }
      // );
    }

    // Fetch all materials
    const materials = await prisma.material.findMany({
      where: {
        isActive: true,
      },
      orderBy: {
        name: "asc",
      },
      select: {
        id: true,
        name: true,
      },
    });

    // Fetch all sizes
    const sizes = await prisma.size.findMany({
      where: {
        isActive: true,
      },
      orderBy: {
        name: "asc",
      },
      select: {
        id: true,
        name: true,
      },
    });

    // Fetch all categories
    const categories = await prisma.category.findMany({
      where: {
        isActive: true,
      },
      orderBy: {
        name: "asc",
      },
      select: {
        id: true,
        name: true,
      },
    });

    // Fetch all brands
    const brands = await prisma.brand.findMany({
      where: {
        isActive: true,
      },
      orderBy: {
        name: "asc",
      },
      select: {
        id: true,
        name: true,
      },
    });

    return NextResponse.json({
      message: "Filters fetched successfully",
      success: true,
      data: {
        materials,
        sizes,
        categories,
        brands,
      },
    });
  } catch (error) {
    console.error("Error fetching filters:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch filters",
        success: false,
      },
      { status: 500 }
    );
  }
}
