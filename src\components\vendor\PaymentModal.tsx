import { useState, useEffect } from 'react';
import Image from 'next/image';
import Modal from '@/components/Modal';
import { vendorAPI as api } from "@/lib/axios";

interface PaymentQRCode {
  id: string;
  imageUrl: string;
  name: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (referenceId: string) => void;
  totalAmount: number;
}

export default function PaymentModal({ isOpen, onClose, onSubmit, totalAmount }: PaymentModalProps) {
  const [referenceId, setReferenceId] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [qrCode, setQRCode] = useState<PaymentQRCode | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isOpen) {
      fetchQRCode();
    }
  }, [isOpen]);

  const fetchQRCode = async () => {
    try {
      setLoading(true);
      console.log('Fetching payment QR code...');
      const response = await api.get('/payment-qrcode');
      console.log('Payment QR code response:', response.data);

      if (response.data.success && response.data.data) {
        console.log('Setting QR code:', response.data.data);
        setQRCode(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching payment QR code:', error);
      // Use a fallback QR code on error

    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    if (!referenceId.trim()) {
      alert('Please enter the payment reference ID');
      return;
    }

    setIsSubmitting(true);
    onSubmit(referenceId);
    // Note: We don't reset isSubmitting here because the parent component will close the modal
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Complete Your Payment"
      maxWidth="md"
    >
      <div className="space-y-6">
        <div className="text-center">
          <p className="text-lg font-medium text-gray-900">Total Amount: ₹{totalAmount.toFixed(2)}</p>
          <p className="text-sm text-gray-500 mt-1">Please complete your payment to proceed</p>
        </div>

        <div className="flex flex-col items-center">
          {loading ? (
            <div className="flex justify-center items-center h-52 w-52 mb-4 bg-gray-100 rounded-md">
              <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            </div>
          ) : qrCode ? (
            <div className="mb-4">
              <Image
                src={qrCode.imageUrl}
                alt={qrCode.name}
                width={200}
                height={200}
                className="border rounded-md"
              />
              <p className="mt-2 text-xs text-gray-500 text-center">{qrCode.name}</p>
            </div>
          ) : (
            <div className="flex justify-center items-center h-52 w-52 mb-4 bg-gray-100 rounded-md">
              <p className="text-gray-500 text-center px-4">No payment QR code available. Please contact support.</p>
            </div>
          )}
          <p className="text-sm text-gray-600 mb-4 text-center">
            Scan the QR code above to make your payment
          </p>
        </div>

        <div className="space-y-2">
          <label htmlFor="referenceId" className="block text-sm font-medium text-gray-700">
            Payment Reference ID <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="referenceId"
            value={referenceId}
            onChange={(e) => setReferenceId(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="Enter the reference ID from your payment"
          />
          <p className="text-xs text-gray-500">
            You will find this ID in your payment confirmation
          </p>
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Processing...' : 'Confirm Payment'}
          </button>
        </div>
      </div>
    </Modal>
  );
}
