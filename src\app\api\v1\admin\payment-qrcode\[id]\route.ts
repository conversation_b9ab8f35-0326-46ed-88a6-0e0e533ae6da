import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

// Interface for updating a payment QR code
interface UpdatePaymentQRCodeRequest {
  imageUrl?: string;
  name?: string;
  isActive?: boolean;
}

// GET a specific payment QR code
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Admin: Fetching payment QR code with ID ${params.id}`);

    // Get the QR code from the database
    const paymentQRCode = await prisma.paymentQRCode.findUnique({
      where: { id: params.id },
    });

    console.log("Admin: Fetched QR code:", paymentQRCode);

    if (!paymentQRCode) {
      return NextResponse.json(
        {
          message: "Payment QR code not found",
          success: false,
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: "Payment QR code fetched successfully",
      success: true,
      data: paymentQRCode,
    });
  } catch (error) {
    console.error("Error fetching payment QR code:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch payment QR code",
        success: false,
      },
      { status: 500 }
    );
  }
}

// UPDATE a payment QR code
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json() as UpdatePaymentQRCodeRequest;
    const { imageUrl, name, isActive } = body;

    console.log(`Admin: Updating payment QR code with ID ${params.id}`, { imageUrl, name, isActive });

    // Check if the QR code exists
    const existingQRCode = await prisma.paymentQRCode.findUnique({
      where: { id: params.id },
    });

    console.log("Admin: Existing QR code:", existingQRCode);

    if (!existingQRCode) {
      return NextResponse.json(
        {
          message: "Payment QR code not found",
          success: false,
        },
        { status: 404 }
      );
    }

    // If setting this QR code to active, deactivate all others
    if (isActive && !existingQRCode.isActive) {
      console.log("Admin: Deactivating all other QR codes");

      await prisma.paymentQRCode.updateMany({
        where: { isActive: true },
        data: { isActive: false },
      });
    }

    // Update the QR code
    console.log("Admin: Updating QR code");

    const updatedQRCode = await prisma.paymentQRCode.update({
      where: { id: params.id },
      data: {
        imageUrl,
        name,
        isActive,
      },
    });

    console.log("Admin: Updated QR code:", updatedQRCode);

    return NextResponse.json({
      message: "Payment QR code updated successfully",
      success: true,
      data: updatedQRCode,
    });
  } catch (error) {
    console.error("Error updating payment QR code:", error);
    return NextResponse.json(
      {
        message: "Failed to update payment QR code",
        success: false,
      },
      { status: 500 }
    );
  }
}

// DELETE a payment QR code
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Admin: Deleting payment QR code with ID ${params.id}`);

    // Check if the QR code exists
    const existingQRCode = await prisma.paymentQRCode.findUnique({
      where: { id: params.id },
    });

    console.log("Admin: Existing QR code:", existingQRCode);

    if (!existingQRCode) {
      return NextResponse.json(
        {
          message: "Payment QR code not found",
          success: false,
        },
        { status: 404 }
      );
    }

    // Delete the QR code
    console.log("Admin: Deleting QR code");
    await prisma.paymentQRCode.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      message: "Payment QR code deleted successfully",
      success: true,
    });
  } catch (error) {
    console.error("Error deleting payment QR code:", error);
    return NextResponse.json(
      {
        message: "Failed to delete payment QR code",
        success: false,
      },
      { status: 500 }
    );
  }
}
