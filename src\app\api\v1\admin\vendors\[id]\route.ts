// get vendor by id
// update vendor by id
// delete vendor by id
import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcryptjs";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const vendor = await prisma.vendor.findUnique({
      where: {
        id: params.id,
      },
      select: {
        id: true,
        contactPerson: true,
        businessName: true,
        email: true,
        phoneNumber: true,
        address: true,
        status: true,
        maxCredit: true,
        isCreditGiven: true,
        taxId: true,
        city: true,
        state: true,
        postalCode: true,
        street: true,
      },
    });

    if (!vendor) {
      return NextResponse.json(
        { message: "Vendor not found", success: false },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: "Vendor fetched successfully",
      success: true,
      data: vendor,
    });
  } catch (error) {
    console.error("Error fetching vendor:", error);
    return NextResponse.json(
      { message: "Failed to fetch vendor", success: false },
      { status: 500 }
    );
  }
}

// DELETE a vendor
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if vendor exists
    const vendor = await prisma.vendor.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!vendor) {
      return NextResponse.json(
        { message: "Vendor not found", success: false },
        { status: 404 }
      );
    }

    // Delete the vendor
    await prisma.vendor.delete({
      where: {
        id: params.id,
      },
    });

    return NextResponse.json({
      message: "Vendor deleted successfully",
      success: true,
    });
  } catch (error) {
    console.error("Error deleting vendor:", error);

    // Check if error is related to foreign key constraints
    if (error instanceof Error && error.message.includes("foreign key constraint")) {
      return NextResponse.json(
        {
          message: "Cannot delete vendor because they have associated orders or other data. Please remove those first.",
          success: false
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Failed to delete vendor", success: false },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const {
      businessName,
      email,
      password,
      phoneNumber,
      address,
      status,
      maxCredit,
      isCreditGiven,
      contactPerson,
      street,
      city,
      state,
      postalCode,
      taxId,
    } = await request.json();

    const updateData: any = {
      businessName,
      email,
      phoneNumber,
      address,
      status,
      maxCredit: parseFloat(maxCredit) || 0,
      isCreditGiven: Boolean(isCreditGiven),
      contactPerson,
      street,
      city,
      state,
      postalCode,
      taxId,
    };

    // Only update password if provided
    if (password) {
      updateData.password = await bcrypt.hash(password, 10);
    }

    const vendor = await prisma.vendor.update({
      where: { id: params.id },
      data: updateData,
    });

    return NextResponse.json({
      message: "Vendor updated successfully",
      success: true,
      data: vendor,
    });
  } catch (error) {
    console.error("Error updating vendor:", error);
    return NextResponse.json(
      { message: "Failed to update vendor", success: false },
      { status: 500 }
    );
  }
}
