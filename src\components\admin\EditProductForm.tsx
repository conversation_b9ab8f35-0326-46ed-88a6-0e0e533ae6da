"use client";

import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useState, useEffect } from "react";
import { adminAPI as api } from "@/lib/axios";
import { toast } from "react-toastify";
import ImageUploaderNoCrop from "@/components/admin/ImageUploaderNoCrop";

// Define our own Product interface to match the updated schema
interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  basePrice: number;
  gstPercentage: number;
  bulkPricing?: any;
  maxOrderQuantity?: number | null;
  packagingSize: number;
  packagingUnit: string;
  isActive: boolean;
  isPublished: boolean;
  images: string[];
  categoryId: string;
  brandId?: string | null;
  materialId?: string | null;
  sizeId?: string | null;
  createdAt: Date;
  updatedAt: Date;
  // Include related entities
  category?: {
    id: string;
    name: string;
  };
  brand?: {
    id: string;
    name: string;
  };
  material?: {
    id: string;
    name: string;
  };
  size?: {
    id: string;
    name: string;
  };
}

const productSchema = yup.object({
  name: yup.string().required("Product name is required"),
  description: yup.string().nullable(),
  basePrice: yup
    .number()
    .min(0, "Price must be greater than or equal to 0")
    .required("Base price is required"),
  gstPercentage: yup
    .number()
    .required("GST percentage is required"),
  isActive: yup.boolean(),
  isPublished: yup.boolean(),
  categoryId: yup.string().required("Category is required"),
  brandId: yup.string().required("Brand is required"),
  materialId: yup.string().required("Material is required"),
  sizeId: yup.string().required("Size is required"),
  packagingSize: yup.number().integer().min(1).nullable(),
  packagingUnit: yup.string().nullable(),
});

type ProductFormData = yup.InferType<typeof productSchema>;

interface EditProductFormProps {
  product: Product;
}

interface Category {
  id: string;
  name: string;
}

interface Brand {
  id: string;
  name: string;
}

interface Material {
  id: string;
  name: string;
}

interface Size {
  id: string;
  name: string;
}

export default function EditProductForm({ product }: EditProductFormProps) {
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [sizes, setSizes] = useState<Size[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [loadingBrands, setLoadingBrands] = useState(true);
  const [loadingMaterials, setLoadingMaterials] = useState(true);
  const [loadingSizes, setLoadingSizes] = useState(true);
  const [productImages, setProductImages] = useState<string[]>(product.images || []);

  useEffect(() => {
    console.log("Product data received:", product);
    console.log("Category:", product.category);
    console.log("Brand:", product.brand);
    console.log("Material:", product.material);
    console.log("Size:", product.size);

    // Debug log for IDs
    console.log("Category ID:", product.categoryId);
    console.log("Brand ID:", product.brandId);
    console.log("Material ID:", product.materialId);
    console.log("Size ID:", product.sizeId);

    fetchCategories();
    fetchBrands();
    fetchMaterials();
    fetchSizes();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoadingCategories(true);
      const { data } = await api.get("/categories");
      console.log("Categories API response:", data);
      if (data.success) {
        setCategories(data.data);
        console.log("Categories loaded:", data.data);

        // Check if product category is in the list
        if (product.categoryId && product.category) {
          const categoryExists = data.data.some((c: any) => c.id === product.categoryId);
          console.log(`Category ${product.category.name} (${product.categoryId}) exists in list: ${categoryExists}`);
        }
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    } finally {
      setLoadingCategories(false);
    }
  };

  const fetchBrands = async () => {
    try {
      setLoadingBrands(true);
      const { data } = await api.get("/brands");
      console.log("Brands API response:", data);
      if (data.success) {
        setBrands(data.data);
        console.log("Brands loaded:", data.data);

        // Check if product brand is in the list
        if (product.brandId && product.brand) {
          const brandExists = data.data.some((b: any) => b.id === product.brandId);
          console.log(`Brand ${product.brand.name} (${product.brandId}) exists in list: ${brandExists}`);
        }
      }
    } catch (error) {
      console.error("Error fetching brands:", error);
    } finally {
      setLoadingBrands(false);
    }
  };

  const fetchMaterials = async () => {
    try {
      setLoadingMaterials(true);
      const { data } = await api.get("/materials");
      console.log("Materials API response:", data);
      if (data.success) {
        setMaterials(data.data);
        console.log("Materials loaded:", data.data);

        // Check if product material is in the list
        if (product.materialId && product.material) {
          const materialExists = data.data.some((m: any) => m.id === product.materialId);
          console.log(`Material ${product.material.name} (${product.materialId}) exists in list: ${materialExists}`);
        }
      }
    } catch (error) {
      console.error("Error fetching materials:", error);
    } finally {
      setLoadingMaterials(false);
    }
  };

  const fetchSizes = async () => {
    try {
      setLoadingSizes(true);
      const { data } = await api.get("/sizes");
      console.log("Sizes API response:", data);
      if (data.success) {
        setSizes(data.data);
        console.log("Sizes loaded:", data.data);

        // Check if product size is in the list
        if (product.sizeId && product.size) {
          const sizeExists = data.data.some((s: any) => s.id === product.sizeId);
          console.log(`Size ${product.size.name} (${product.sizeId}) exists in list: ${sizeExists}`);
        }
      }
    } catch (error) {
      console.error("Error fetching sizes:", error);
    } finally {
      setLoadingSizes(false);
    }
  };

  // Price Master functionality has been removed

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<ProductFormData>({
    resolver: yupResolver(productSchema),
    defaultValues: {
      name: product.name,
      description: product.description || "",
      basePrice: product.basePrice,
      gstPercentage: product.gstPercentage || 18,
      isActive: product.isActive,
      isPublished: product.isPublished,
      categoryId: product.categoryId,
      brandId: product.brandId || "",
      materialId: product.materialId || "",
      sizeId: product.sizeId || "",
      packagingSize: product.packagingSize,
      packagingUnit: product.packagingUnit,
    },
  });

  // Log the form's default values
  console.log("Form default values:", {
    name: product.name,
    description: product.description || "",
    basePrice: product.basePrice,
    gstPercentage: product.gstPercentage || 18,
    isActive: product.isActive,
    isPublished: product.isPublished,
    categoryId: product.categoryId,
    brandId: product.brandId || "",
    materialId: product.materialId || "",
    sizeId: product.sizeId || "",
    packagingSize: product.packagingSize,
    packagingUnit: product.packagingUnit,
  });

  // Price Master functionality has been removed

  const handleImagesUploaded = (urls: string[]) => {
    setProductImages(urls);
  };

  const onSubmit = async (data: ProductFormData) => {
    try {
      console.log("Submitting form data:", data);

      // Add images to the data
      const updatedData = {
        ...data,
        images: productImages
      };

      // Use axios for consistency with the rest of the application
      const { data: responseData } = await api.put(`/products/${product.id}`, updatedData);
      console.log("Update response:", responseData);

      if (responseData.success) {
        toast.success("Product updated successfully");

        // Force a hard navigation to refresh the page completely
        window.location.href = "/admin/products";
      } else {
        throw new Error(responseData.message || "Failed to update product");
      }
    } catch (error: any) {
      console.error("Error updating product:", error);
      console.error("Error details:", error.response?.data || error.message);

      // Handle specific error cases
      if (error.response?.status === 401) {
        toast.error("Your session has expired. Please login again.");
        // The axios interceptor will handle the redirect
      } else {
        toast.error(error.response?.data?.message || error.message || "Failed to update product");
      }
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 bg-white p-4 sm:p-6 rounded-lg shadow max-w-full overflow-hidden">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            Product Name <span className="text-red-500">*</span>
          </label>
          <input
            {...register("name")}
            type="text"
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="Enter product name"
          />
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            Description <span className="text-red-500">*</span>
          </label>
          <textarea
            {...register("description")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            rows={4}
            placeholder="Enter product description"
          />
          {errors.description && (
            <p className="text-red-500 text-sm mt-1">
              {errors.description.message}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700 mb-1">
            Category <span className="text-red-500">*</span>
          </label>
          <select
            {...register("categoryId")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            disabled={loadingCategories}
            defaultValue={product.categoryId}
          >
            {loadingCategories ? (
              <option value="">Loading categories...</option>
            ) : (
              <>
                {/* Always add the current category as the first option */}
                {product.category && (
                  <option key={product.categoryId} value={product.categoryId}>
                    {product.category.name} {!categories.some(c => c.id === product.categoryId) ? "(Current)" : ""}
                  </option>
                )}
                {categories
                  .filter(category => category.id !== product.categoryId) // Filter out the current category to avoid duplicates
                  .map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))
                }
              </>
            )}
          </select>
          {errors.categoryId && (
            <p className="text-red-500 text-sm mt-1">{errors.categoryId.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="brandId" className="block text-sm font-medium text-gray-700 mb-1">
            Sub Category <span className="text-red-500">*</span>
          </label>
          <select
            {...register("brandId")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            disabled={loadingBrands}
            defaultValue={product.brandId || ""}
          >
            {loadingBrands ? (
              <option value="">Loading sub categories...</option>
            ) : (
              <>
                {/* Always add the current brand as the first option */}
                {product.brand && product.brandId && (
                  <option key={product.brandId} value={product.brandId}>
                    {product.brand.name} {!brands.some(b => b.id === product.brandId) ? "(Current)" : ""}
                  </option>
                )}
                {brands
                  .filter(brand => brand.id !== product.brandId) // Filter out the current brand to avoid duplicates
                  .map((brand) => (
                    <option key={brand.id} value={brand.id}>
                      {brand.name}
                    </option>
                  ))
                }
              </>
            )}
          </select>
          {errors.brandId && (
            <p className="text-red-500 text-sm mt-1">{errors.brandId.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="materialId" className="block text-sm font-medium text-gray-700 mb-1">
            Material <span className="text-red-500">*</span>
          </label>
          <select
            {...register("materialId")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            disabled={loadingMaterials}
            defaultValue={product.materialId || ""}
          >
            {loadingMaterials ? (
              <option value="">Loading materials...</option>
            ) : (
              <>
                {/* Always add the current material as the first option */}
                {product.material && product.materialId && (
                  <option key={product.materialId} value={product.materialId}>
                    {product.material.name} {!materials.some(m => m.id === product.materialId) ? "(Current)" : ""}
                  </option>
                )}
                {materials
                  .filter(material => material.id !== product.materialId) // Filter out the current material to avoid duplicates
                  .map((material) => (
                    <option key={material.id} value={material.id}>
                      {material.name}
                    </option>
                  ))
                }
              </>
            )}
          </select>
          {errors.materialId && (
            <p className="text-red-500 text-sm mt-1">{errors.materialId.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="sizeId" className="block text-sm font-medium text-gray-700 mb-1">
            Size <span className="text-red-500">*</span>
          </label>
          <select
            {...register("sizeId")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            disabled={loadingSizes}
            defaultValue={product.sizeId || ""}
          >
            {loadingSizes ? (
              <option value="">Loading sizes...</option>
            ) : (
              <>
                {/* Always add the current size as the first option */}
                {product.size && product.sizeId && (
                  <option key={product.sizeId} value={product.sizeId}>
                    {product.size.name} {!sizes.some(s => s.id === product.sizeId) ? "(Current)" : ""}
                  </option>
                )}
                {sizes
                  .filter(size => size.id !== product.sizeId) // Filter out the current size to avoid duplicates
                  .map((size) => (
                    <option key={size.id} value={size.id}>
                      {size.name}
                    </option>
                  ))
                }
              </>
            )}
          </select>
          {errors.sizeId && (
            <p className="text-red-500 text-sm mt-1">{errors.sizeId.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="basePrice" className="block text-sm font-medium text-gray-700 mb-1">
            Base Price <span className="text-red-500">*</span>
          </label>
          <input
            {...register("basePrice", { valueAsNumber: true })}
            type="number"
            step="0.01"
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
          {errors.basePrice && (
            <p className="text-red-500 text-sm mt-1">
              {errors.basePrice.message}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="gstPercentage" className="block text-sm font-medium text-gray-700 mb-1">
            GST Percentage <span className="text-red-500">*</span>
          </label>
          <select
            {...register("gstPercentage", { valueAsNumber: true })}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="5">5%</option>
            <option value="12">12%</option>
            <option value="18">18%</option>
          </select>
          {errors.gstPercentage && (
            <p className="text-red-500 text-sm mt-1">
              {errors.gstPercentage.message}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="packagingSize" className="block text-sm font-medium text-gray-700 mb-1">
            Packaging Size - 1 Bale <span className="text-red-500">*</span>
          </label>
          <input
            {...register("packagingSize", { valueAsNumber: true })}
            type="number"
            min="1"
            step="1"
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
          {errors.packagingSize && (
            <p className="text-red-500 text-sm mt-1">{errors.packagingSize.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="packagingUnit" className="block text-sm font-medium text-gray-700 mb-1">
            Packaging Unit <span className="text-red-500">*</span>
          </label>
          <select
            {...register("packagingUnit")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            defaultValue={product.packagingUnit || "piece"}
          >
            <option value="piece">Piece</option>
            <option value="unit">Unit</option>
          </select>
          {errors.packagingUnit && (
            <p className="text-red-500 text-sm mt-1">
              {errors.packagingUnit.message}
            </p>
          )}
        </div>

        <div>
          <label className="flex items-center space-x-2">
            <input
              {...register("isActive")}
              type="checkbox"
              id="isActive"
              className="rounded"
            />
            <span className="text-sm font-medium text-gray-700">Active</span>
          </label>
        </div>

        <div>
          <label className="flex items-center space-x-2">
            <input
              {...register("isPublished")}
              type="checkbox"
              id="isPublished"
              className="rounded"
            />
            <span className="text-sm font-medium text-gray-700">Published</span>
          </label>
        </div>
      </div>

      <div className="col-span-1 md:col-span-2">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Product Images <span className="text-red-500">*</span>
        </label>

        <ImageUploaderNoCrop
          onImagesUploaded={handleImagesUploaded}
          existingImages={productImages}
          multiple={true}
          maxFiles={5}
        />
        <p className="mt-1 text-xs text-gray-500">
          Images will be automatically resized to 9:16 ratio (562x1000px) without cropping
        </p>
      </div>

      <div className="flex flex-col sm:flex-row justify-end gap-3 sm:space-x-4">
        <button
          type="button"
          onClick={() => router.back()}
          className="w-full sm:w-auto px-4 py-2 border rounded-md hover:bg-gray-100 text-center"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full sm:w-auto px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300 text-center"
        >
          {isSubmitting ? "Updating..." : "Update Product"}
        </button>
      </div>
    </form>
  );
}
