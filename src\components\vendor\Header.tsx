"use client";
import { useRouter } from "next/navigation";
import { default as api } from "@/lib/axios"; // Use base API for auth
import Link from "next/link";
import Image from "next/image";
import { ShoppingCart, User, Package } from "lucide-react";
import { useEffect, useState } from "react";
import jwt from "jsonwebtoken";

interface VendorInfo {
  email: string;
  businessName: string;
}

export default function Header() {
  const router = useRouter();
  const [vendorInfo, setVendorInfo] = useState<VendorInfo | null>(null);

  useEffect(() => {
    // Get vendor info from the JWT token
    const authToken = document.cookie
      .split("; ")
      .find((row) => row.startsWith("authToken="))
      ?.split("=")[1];

    if (authToken) {
      try {
        const decoded = jwt.decode(authToken) as VendorInfo;
        setVendorInfo(decoded);
      } catch (error) {
        console.error("Error decoding token:", error);
      }
    }
  }, []);

  const handleLogout = async () => {
    try {
      const { data } = await api.post("/auth/vendor/logout");
      if (data.success) {
        router.push("/auth/vendor/login");
        router.refresh();
      }
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  return (
    <header className="bg-white shadow-sm sticky top-0 z-20 w-full">
      <div className="max-w-full px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 justify-between items-center">
          {/* Logo - centered on mobile, left-aligned on desktop */}
          <div className="flex items-center justify-center md:justify-start flex-1 md:flex-none">
            <Link href="/vendor/dashboard" className="flex-shrink-0 flex items-center">
              <Image
                src="https://buymetropolis.com/cdn/shop/files/Metropolis_logo_-_transparent.png?v=1726657630&width=280"
                alt="Metropolis"
                width={140}
                height={40}
                priority
              />
            </Link>
          </div>

          {/* Right side navigation */}
          <div className="flex items-center gap-2 sm:gap-4 md:gap-6">
            {/* Cart icon */}
            <Link
              href="/vendor/cart"
              className="relative p-2 hover:bg-gray-100 rounded-full transition-colors"
              aria-label="Cart"
            >
              <ShoppingCart className="h-5 w-5" />
              {/* Add cart count badge here */}
            </Link>

            {/* Orders icon */}
            <Link
              href="/vendor/orders"
              className="relative p-2 hover:bg-gray-100 rounded-full transition-colors"
              aria-label="Orders"
            >
              <Package className="h-5 w-5" />
            </Link>

            {/* User info and logout */}
            <div className="flex items-center gap-2 sm:gap-4">
              {vendorInfo && (
                <div className="flex items-center gap-2">
                  <User className="h-5 w-5 text-gray-600" />
                  <span className="text-sm text-gray-700 hidden md:inline">
                    {vendorInfo.businessName || vendorInfo.email}
                  </span>
                </div>
              )}
              <button
                onClick={handleLogout}
                className="text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors whitespace-nowrap"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
