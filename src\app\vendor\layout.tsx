import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import jwt from "jsonwebtoken";
import Sidebar from "@/components/vendor/Sidebar";
import Header from "@/components/vendor/Header";
import React from "react";

interface JWTPayload {
  id: string;
  email: string;
  role: string;
}

export default function VendorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const cookieStore = cookies();
  const authToken = cookieStore.get("authToken");

  if (!authToken?.value) {
    redirect("/auth/vendor/login");
  }

  try {
    const decoded = jwt.verify(
      authToken.value,
      process.env.JWT_KEY!
    ) as JWTPayload;

    if (decoded.role !== "VENDOR") {
      redirect("/auth/vendor/login");
    }
  } catch (error) {
    console.error("Layout - JWT verification error:", error);
    redirect("/auth/vendor/login");
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Header />
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar - hidden on mobile but still in DOM for the toggle functionality */}
        <div className="md:flex-shrink-0 md:block">
          <Sidebar />
        </div>
        {/* Main content - full width on mobile, adjusted on desktop */}
        <main className="flex-1 p-4 md:p-6 overflow-y-auto w-full">
          <div className="max-w-7xl mx-auto">
            {/* Add top padding on mobile to account for the menu button */}
            <div className="md:pt-0 pt-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
