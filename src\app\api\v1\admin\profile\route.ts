import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

export async function GET(request: NextRequest) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      console.log("GET /api/v1/admin/profile - No auth token found");
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
      id: string;
      email: string;
      role: string;
    };

    if (decoded.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Unauthorized - Not an admin", success: false },
        { status: 401 }
      );
    }

    const admin = await prisma.adminUser.findUnique({
      where: { id: decoded.id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!admin) {
      return NextResponse.json(
        { message: "Admin not found", success: false },
        { status: 404 }
      );
    }

    // Check for existing FCM token for this admin
    const existingFcmToken = await prisma.fCMToken.findFirst({
      where: {
        userId: admin.id,
        userType: "admin",
        isActive: true,
      },
    });

    console.log("GET /api/v1/admin/profile - Admin found:", admin.id);
    return NextResponse.json({
      message: "Profile fetched successfully",
      success: true,
      data: {
        ...admin,
        fcmToken: existingFcmToken?.token || null,
      },
    });
  } catch (error) {
    console.error("Error fetching admin profile:", error);
    return NextResponse.json(
      { message: "Failed to fetch profile", success: false },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
      id: string;
      email: string;
      role: string;
    };

    if (decoded.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Unauthorized - Not an admin", success: false },
        { status: 401 }
      );
    }

    const admin = await prisma.adminUser.findUnique({
      where: { id: decoded.id },
    });

    if (!admin) {
      return NextResponse.json(
        { message: "Admin not found", success: false },
        { status: 404 }
      );
    }

    const body = await request.json();

    const updatedAdmin = await prisma.adminUser.update({
      where: { id: admin.id },
      data: {
        firstName: body.firstName,
        lastName: body.lastName,
        email: body.email,
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Check for existing FCM token for this admin
    const existingFcmToken = await prisma.fCMToken.findFirst({
      where: {
        userId: admin.id,
        userType: "admin",
        isActive: true,
      },
    });

    return NextResponse.json({
      message: "Profile updated successfully",
      success: true,
      data: {
        ...updatedAdmin,
        fcmToken: existingFcmToken?.token || null,
      },
    });
  } catch (error) {
    console.error("Error updating admin profile:", error);
    return NextResponse.json(
      { message: "Failed to update profile", success: false },
      { status: 500 }
    );
  }
}
