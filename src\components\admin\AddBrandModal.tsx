"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { toast } from "react-toastify";
import { adminAPI as api } from "@/lib/axios";
import Modal from "./Modal";
import ImageUploaderNoCrop from "./ImageUploaderNoCrop";

// Define the schema for brand creation
const brandSchema = yup.object({
  name: yup.string().required("Brand name is required"),
  imageAspectRatio: yup.string().required("Image aspect ratio is required"),
  materialId: yup.string(),
  sizeId: yup.string(),
  isActive: yup.boolean(),
});

type BrandFormData = yup.InferType<typeof brandSchema>;

interface Material {
  id: string;
  name: string;
  isActive: boolean;
}

interface Size {
  id: string;
  name: string;
  isActive: boolean;
}

interface AddBrandModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (brand: any) => void;
}

const AddBrandModal: React.FC<AddBrandModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [sizes, setSizes] = useState<Size[]>([]);
  const [loadingMaterials, setLoadingMaterials] = useState(false);
  const [loadingSizes, setLoadingSizes] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
  } = useForm<BrandFormData>({
    resolver: yupResolver(brandSchema),
    defaultValues: {
      imageAspectRatio: "1:1",
      isActive: true,
    },
  });

  // Watch the imageAspectRatio field to update the ImageUploader
  const selectedAspectRatio = watch("imageAspectRatio");

  useEffect(() => {
    if (isOpen) {
      fetchMaterials();
      fetchSizes();
    }
  }, [isOpen]);

  const fetchMaterials = async () => {
    try {
      setLoadingMaterials(true);
      const { data } = await api.get("/materials");
      if (data.success) {
        setMaterials(data.data);
      } else {
        toast.error("Failed to fetch materials");
      }
    } catch (error) {
      console.error("Error fetching materials:", error);
      toast.error("Failed to fetch materials");
    } finally {
      setLoadingMaterials(false);
    }
  };

  const fetchSizes = async () => {
    try {
      setLoadingSizes(true);
      const { data } = await api.get("/sizes");
      if (data.success) {
        setSizes(data.data);
      } else {
        toast.error("Failed to fetch sizes");
      }
    } catch (error) {
      console.error("Error fetching sizes:", error);
      toast.error("Failed to fetch sizes");
    } finally {
      setLoadingSizes(false);
    }
  };

  const handleImagesUploaded = (imageUrls: string[]) => {
    setUploadedImages(imageUrls);
  };

  const onSubmit = async (data: BrandFormData) => {
    try {
      setIsSubmitting(true);

      const response = await api.post("/brands", {
        ...data,
        imageUrl: uploadedImages.length > 0 ? uploadedImages[0] : undefined,
        imageAspectRatio: data.imageAspectRatio,
        materialId: data.materialId || undefined,
        sizeId: data.sizeId || undefined,
      });

      if (response.data.success) {
        toast.success("Sub Category created successfully");
        onSuccess(response.data.data);
        reset();
        setUploadedImages([]);
        onClose();
      } else {
        toast.error(response.data.message || "Failed to create sub category");
      }
    } catch (error: any) {
      console.error("Error creating sub category:", error);
      toast.error(error.response?.data?.message || "Failed to create sub category");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Add New Sub Category" size="md">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Sub Category Name <span className="text-red-500">*</span>
          </label>
          <input
            {...register("name")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="Enter sub category name"
          />
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Image Aspect Ratio <span className="text-red-500">*</span>
          </label>
          <select
            {...register("imageAspectRatio")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="1:1">Square (1:1)</option>
            <option value="9:16">Portrait (9:16)</option>
          </select>
          <p className="mt-1 text-xs text-gray-500">
            This aspect ratio will be used for products in this sub category
          </p>
          {errors.imageAspectRatio && (
            <p className="text-red-500 text-sm mt-1">{errors.imageAspectRatio.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Material
          </label>
          <select
            {...register("materialId")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            disabled={loadingMaterials}
          >
            <option value="">Select Material (optional)</option>
            {materials.map((material) => (
              <option key={material.id} value={material.id}>
                {material.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Size
          </label>
          <select
            {...register("sizeId")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            disabled={loadingSizes}
          >
            <option value="">Select Size (optional)</option>
            {sizes.map((size) => (
              <option key={size.id} value={size.id}>
                {size.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Sub Category Image <span className="text-red-500">*</span>
          </label>
          <ImageUploaderNoCrop
            onImagesUploaded={handleImagesUploaded}
            multiple={false}
            maxFiles={1}
            existingImages={uploadedImages}
            imageAspectRatio="1:1"
          />
          <p className="mt-1 text-xs text-gray-500">
            Sub category images will always be uploaded in 1:1 ratio (1000x1000px)
          </p>
          <p className="mt-1 text-xs text-gray-500">
            The selected aspect ratio will be used for products in this sub category
          </p>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="isActive"
            {...register("isActive")}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
            Active
          </label>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={() => {
              reset();
              setUploadedImages([]);
              onClose();
            }}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Creating..." : "Create Sub Category"}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default AddBrandModal;
