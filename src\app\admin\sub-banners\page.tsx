"use client";
import { useState, useEffect } from "react";
import { adminAPI as api } from "@/lib/axios";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Image from "next/image";
import { Trash2, Plus, Edit } from "lucide-react";
import AddSubBannerModal from "@/components/admin/AddSubBannerModal";
import EditSubBannerModal from "@/components/admin/EditSubBannerModal";

interface SubBanner {
  id: string;
  imageUrl: string;
  name: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function SubBannersPage() {
  const [subBanners, setSubBanners] = useState<SubBanner[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedSubBanner, setSelectedSubBanner] = useState<SubBanner | null>(null);

  useEffect(() => {
    fetchSubBanners();
  }, []);

  const fetchSubBanners = async () => {
    try {
      setLoading(true);
      const response = await api.get("/sub-banners");
      if (response.data.success) {
        setSubBanners(response.data.data);
      } else {
        toast.error("Failed to fetch sub-banners");
      }
    } catch (error) {
      console.error("Error fetching sub-banners:", error);
      toast.error("Failed to fetch sub-banners");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm("Are you sure you want to delete this sub-banner?")) {
      return;
    }

    try {
      const response = await api.delete(`/sub-banners/${id}`);
      if (response.data.success) {
        toast.success("Sub-banner deleted successfully");
        fetchSubBanners();
      } else {
        toast.error("Failed to delete sub-banner");
      }
    } catch (error) {
      console.error("Error deleting sub-banner:", error);
      toast.error("Failed to delete sub-banner");
    }
  };

  const handleAddSuccess = () => {
    setIsAddModalOpen(false);
    fetchSubBanners();
  };

  const handleEditSuccess = () => {
    setIsEditModalOpen(false);
    fetchSubBanners();
  };

  const openEditModal = (subBanner: SubBanner) => {
    setSelectedSubBanner(subBanner);
    setIsEditModalOpen(true);
  };

  return (
    <div className="p-6">
      <ToastContainer position="top-right" autoClose={3000} />
      
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Vendor Sub-Banners</h1>
        <button
          onClick={() => setIsAddModalOpen(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center gap-2 hover:bg-blue-700 transition-colors"
        >
          <Plus size={18} />
          Add Sub-Banner
        </button>
      </div>

      {loading ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      ) : subBanners.length === 0 ? (
        <div className="bg-gray-50 rounded-lg p-12 text-center">
          <p className="text-gray-500 text-lg mb-4">No sub-banners found</p>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center gap-2 mx-auto hover:bg-blue-700 transition-colors"
          >
            <Plus size={18} />
            Add Your First Sub-Banner
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {subBanners.map((subBanner) => (
            <div key={subBanner.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="relative h-48">
                <Image
                  src={subBanner.imageUrl}
                  alt={subBanner.name}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-semibold text-lg">{subBanner.name}</h3>
                  <span className={`px-2 py-1 text-xs rounded-full ${subBanner.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {subBanner.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <div className="flex justify-end gap-2 mt-4">
                  <button
                    onClick={() => openEditModal(subBanner)}
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                  >
                    <Edit size={18} />
                  </button>
                  <button
                    onClick={() => handleDelete(subBanner.id)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-full transition-colors"
                  >
                    <Trash2 size={18} />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add Sub-Banner Modal */}
      <AddSubBannerModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSuccess={handleAddSuccess}
      />

      {/* Edit Sub-Banner Modal */}
      {selectedSubBanner && (
        <EditSubBannerModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSuccess={handleEditSuccess}
          subBanner={selectedSubBanner}
        />
      )}
    </div>
  );
}
