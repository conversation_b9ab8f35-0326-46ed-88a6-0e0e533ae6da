import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

// Get cart items
export async function GET(request: NextRequest) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
      id: string;
    };

    const cart = await prisma.cart.findFirst({
      where: { vendorId: decoded.id },
      include: {
        items: {
          include: {
            product: {
              include: {
                material: true,
                size: true,
                brand: true,
              }
            },
            vendor: true,
          },
        },
      },
    });

    if (!cart) {
      return NextResponse.json({
        message: "Cart is empty",
        success: true,
        data: { items: [] },
      });
    }

    return NextResponse.json({
      message: "Cart fetched successfully",
      success: true,
      data: cart,
    });
  } catch (error) {
    console.error("Error fetching cart:", error);
    return NextResponse.json(
      { message: "Failed to fetch cart", success: false },
      { status: 500 }
    );
  }
}

// Add item to cart
export async function POST(request: NextRequest) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
      id: string; // This is the vendor's ID
    };

    const { productId, quantity } = await request.json();

    // First, get or create the cart for the vendor
    let cart = await prisma.cart.findFirst({
      where: { vendorId: decoded.id },
    });

    if (!cart) {
      cart = await prisma.cart.create({
        data: {
          vendorId: decoded.id,
        },
      });
    }

    // Get the product to check availability
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: {
        id: true
      },
    });

    if (!product) {
      return NextResponse.json(
        { message: "Product not found", success: false },
        { status: 404 }
      );
    }


    // Create cart item with the vendor's ID from the auth token
    const cartItem = await prisma.cartItem.create({
      data: {
        cartId: cart.id,
        productId,
        quantity,
        vendorId: decoded.id, // Use the vendor's ID from the auth token
      },
    });

    return NextResponse.json({
      message: "Item added to cart successfully",
      success: true,
      data: cartItem,
    });
  } catch (error) {
    console.error("Error adding item to cart:", error);
    return NextResponse.json(
      { message: "Failed to add item to cart", success: false },
      { status: 500 }
    );
  }
}
