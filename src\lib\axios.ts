import axios from "axios";

// Base instance with common config
const baseAPI = axios.create({
  baseURL: "/api/v1",
  headers: {
    "Content-Type": "application/json",
  },
});

// Vendor-specific instance
export const vendorAPI = axios.create({
  baseURL: "/api/v1/vendor",
  headers: {
    "Content-Type": "application/json",
  },
});

// Admin-specific instance
export const adminAPI = axios.create({
  baseURL: "/api/v1/admin",
  headers: {
    "Content-Type": "application/json",
  },
});

// Add response interceptor for consistent error handling
baseAPI.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      if (window.location.pathname.startsWith("/admin")) {
        window.location.href = "/admin/login";
      } else if (window.location.pathname.startsWith("/vendor")) {
        window.location.href = "/auth/vendor/login";
      }
    }
    return Promise.reject(error);
  }
);

// Add the same interceptor to vendor and admin APIs
vendorAPI.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      window.location.href = "/auth/vendor/login";
    }
    return Promise.reject(error);
  }
);

adminAPI.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      window.location.href = "/admin/login";
    }
    return Promise.reject(error);
  }
);

// Default export for general API calls
export default baseAPI;
