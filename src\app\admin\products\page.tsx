// src/app/admin/products/page.tsx
"use client";

import { useState, useEffect, useRef, Suspense } from "react";
import Link from "next/link";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { FiPlus, FiUpload, FiFilter, FiChevronDown, FiSearch } from "react-icons/fi";
import { adminAPI as api } from "@/lib/axios";
import { useSearchParams, useRouter } from "next/navigation";
import ConfirmationModal from "@/components/admin/ConfirmationModal";

interface Product {
  id: string;
  name: string;
  basePrice: number;
  categoryId: string;
  isActive: boolean;
  category: {
    name: string;
  };
  brand?: {
    name: string;
  };
}

// Wrap the component with Suspense
export default function Page() {
  return (
    <Suspense fallback={<div className="p-6">Loading products...</div>}>
      <ProductsList />
    </Suspense>
  );
}

function ProductsList() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [categories, setCategories] = useState([]);
  const [brands, setBrands] = useState([]);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [showBrandDropdown, setShowBrandDropdown] = useState(false);
  const [showSortDropdown, setShowSortDropdown] = useState(false);
  const [categorySearch, setCategorySearch] = useState("");
  const [brandSearch, setBrandSearch] = useState("");
  const [productSearch, setProductSearch] = useState("");
  const [filteredCategories, setFilteredCategories] = useState([]);
  const [filteredBrands, setFilteredBrands] = useState([]);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 1
  });
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<string | null>(null);
  const categoryDropdownRef = useRef<HTMLDivElement>(null);
  const brandDropdownRef = useRef<HTMLDivElement>(null);
  const sortDropdownRef = useRef<HTMLDivElement>(null);
  const searchParams = useSearchParams();
  const router = useRouter();

  const category = searchParams.get("category");
  const brand = searchParams.get("brand");
  const search = searchParams.get("search");
  const sort = searchParams.get("sort") || "newest";
  const page = parseInt(searchParams.get("page") || "1");

  useEffect(() => {
    fetchProducts();
    fetchCategories();
    fetchBrands();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [category, brand, sort, page, search]);

  // Initialize productSearch from URL
  useEffect(() => {
    if (search) {
      setProductSearch(search);
    } else {
      setProductSearch("");
    }
  }, [search]);

  // Filter categories based on search
  useEffect(() => {
    if (categories.length > 0) {
      if (!categorySearch) {
        setFilteredCategories(categories);
      } else {
        const filtered = categories.filter((cat: any) =>
          cat.name.toLowerCase().includes(categorySearch.toLowerCase())
        );
        setFilteredCategories(filtered);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categories, categorySearch]);

  // Filter brands based on search
  useEffect(() => {
    if (brands.length > 0) {
      if (!brandSearch) {
        setFilteredBrands(brands);
      } else {
        const filtered = brands.filter((brand: any) =>
          brand.name.toLowerCase().includes(brandSearch.toLowerCase())
        );
        setFilteredBrands(filtered);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [brands, brandSearch]);

  // Handle click outside to close dropdowns
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (categoryDropdownRef.current && !categoryDropdownRef.current.contains(event.target as Node)) {
        setShowCategoryDropdown(false);
      }
      if (brandDropdownRef.current && !brandDropdownRef.current.contains(event.target as Node)) {
        setShowBrandDropdown(false);
      }
      if (sortDropdownRef.current && !sortDropdownRef.current.contains(event.target as Node)) {
        setShowSortDropdown(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);

      const params = new URLSearchParams();
      if (category) params.append("category", category);
      if (brand) params.append("brand", brand);
      if (search) params.append("search", search);
      if (sort) params.append("sort", sort);
      params.append("page", page.toString());
      params.append("limit", "10");

      const url = `/products?${params.toString()}`;

      const { data } = await api.get(url);
      if (data.success) {
        setProducts(data.data);
        if (data.pagination) {
          setPagination(data.pagination);
        }
      } else {
        toast.error(data.message || "Failed to fetch products");
      }
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error("Failed to fetch products");
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const { data } = await api.get("/categories");
      if (data.success) {
        setCategories(data.data);
        setFilteredCategories(data.data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const fetchBrands = async () => {
    try {
      const { data } = await api.get("/brands");
      if (data.success) {
        setBrands(data.data);
        setFilteredBrands(data.data);
      }
    } catch (error) {
      console.error("Error fetching brands:", error);
    }
  };

  const toggleStatus = async (id: string, currentStatus: boolean) => {
    try {
      const { data } = await api.patch(`/products/${id}/status`, {
        isActive: !currentStatus,
      });
      if (data.success) {
        toast.success(
          `Product ${currentStatus ? "deactivated" : "activated"} successfully`
        );
        fetchProducts();
      } else {
        toast.error(data.message || "Failed to update product status");
      }
    } catch (error) {
      console.error("Error updating product status:", error);
      toast.error("Failed to update product status");
    }
  };

  const openDeleteModal = (id: string) => {
    setProductToDelete(id);
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!productToDelete) return;

    try {
      const { data } = await api.delete(`/products/${productToDelete}`);
      if (data.success) {
        toast.success("Product deleted successfully");
        fetchProducts();
      } else {
        toast.error(data.message || "Failed to delete product");
      }
    } catch (error) {
      console.error("Error deleting product:", error);
      toast.error("Failed to delete product");
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <div className="relative w-20 h-20">
          <div className="absolute top-0 left-0 right-0 bottom-0 animate-spin rounded-full h-20 w-20 border-4 border-t-blue-500 border-b-blue-700 border-l-transparent border-r-transparent"></div>
          <div className="absolute top-2 left-2 right-2 bottom-2 animate-pulse bg-white rounded-full flex items-center justify-center">
            <div className="h-8 w-8 text-blue-600">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
            </div>
          </div>
        </div>
        <p className="text-gray-600 font-medium mt-4">Loading products...</p>
      </div>
    );
  }

  return (
    <div className="p-6">
      <ToastContainer />

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Product"
        message="WARNING: This will permanently delete the product from the database. This action CANNOT be undone. Are you sure you want to proceed?"
        confirmText="Delete"
        cancelText="Cancel"
        confirmButtonClass="bg-red-600 hover:bg-red-700"
      />
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Products</h1>
        <div className="flex space-x-3">
          <Link
            href="/admin/products/bulk-upload"
            className="flex items-center bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600"
          >
            <FiUpload className="mr-2" /> Bulk Upload
          </Link>
          <Link
            href="/admin/products/add"
            className="flex items-center bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600"
          >
            <FiPlus className="mr-2" /> Add Product
          </Link>
        </div>
      </div>

      {/* Filters at the top */}
      <div className="mb-8 bg-white p-4 rounded-lg shadow">
        {/* Search input */}
        <div className="mb-6">
          <div className="relative max-w-3xl mx-auto">
            <input
              type="text"
              placeholder="Search products by name or description..."
              value={productSearch}
              onChange={(e) => setProductSearch(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  const params = new URLSearchParams(searchParams.toString());
                  if (productSearch.trim()) {
                    params.set("search", productSearch);
                    params.set("page", "1"); // Reset to first page on new search
                  } else {
                    params.delete("search");
                  }
                  router.push(`?${params.toString()}`);
                }
              }}
              className="w-full pl-10 pr-16 py-2.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none shadow-sm"
            />
            <FiSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={18} />
            <button
              onClick={() => {
                const params = new URLSearchParams(searchParams.toString());
                if (productSearch.trim()) {
                  params.set("search", productSearch);
                  params.set("page", "1"); // Reset to first page on new search
                } else {
                  params.delete("search");
                }
                router.push(`?${params.toString()}`);
              }}
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-blue-500 text-white px-3 py-1.5 rounded-md hover:bg-blue-600 transition-colors shadow-sm text-sm font-medium"
            >
              Search
            </button>
          </div>
        </div>

        <div className="flex flex-wrap items-center gap-4 mb-4">
          <div className="flex items-center">
            <FiFilter className="text-blue-600 mr-2" size={18} />
            <h2 className="font-medium text-gray-800">Filters:</h2>
          </div>

          {/* Category Filter with Dropdown */}
          <div className="relative" ref={categoryDropdownRef}>
            <button
              className="flex items-center gap-2 text-gray-700 font-medium py-2 px-4 rounded-md hover:bg-gray-50 transition-colors"
              onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
            >
              <span>Category</span>
              <FiChevronDown className={`transition-transform ${showCategoryDropdown ? 'rotate-180' : ''}`} />
            </button>

            {showCategoryDropdown && (
              <div className="absolute z-10 mt-1 w-64 bg-white rounded-md shadow-lg border overflow-hidden">
                <div className="p-3 border-b">
                  <h3 className="font-medium text-gray-700 mb-2">Select Category</h3>
                  <div className="relative">
                    <FiSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search categories..."
                      value={categorySearch}
                      onChange={(e) => setCategorySearch(e.target.value)}
                      className="w-full pl-9 pr-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                <div className="max-h-60 overflow-y-auto p-2">
                  <div
                    className={`px-3 py-2 rounded-md cursor-pointer ${!category ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'}`}
                    onClick={() => {
                      const params = new URLSearchParams(searchParams.toString());
                      params.delete("category");
                      router.push(`?${params.toString()}`);
                      setShowCategoryDropdown(false);
                      setCategorySearch("");
                    }}
                  >
                    All Categories
                  </div>
                  {filteredCategories.map((cat: any) => (
                    <div
                      key={cat.id}
                      className={`px-3 py-2 rounded-md cursor-pointer ${category === cat.id ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'}`}
                      onClick={() => {
                        const params = new URLSearchParams(searchParams.toString());
                        params.set("category", cat.id);
                        router.push(`?${params.toString()}`);
                        setShowCategoryDropdown(false);
                        setCategorySearch("");
                      }}
                    >
                      {cat.name}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sub Categories Filter with Dropdown */}
          <div className="relative" ref={brandDropdownRef}>
            <button
              className="flex items-center gap-2 text-gray-700 font-medium py-2 px-4 rounded-md hover:bg-gray-50 transition-colors"
              onClick={() => setShowBrandDropdown(!showBrandDropdown)}
            >
              <span>Sub Categories</span>
              <FiChevronDown className={`transition-transform ${showBrandDropdown ? 'rotate-180' : ''}`} />
            </button>

            {showBrandDropdown && (
              <div className="absolute z-10 mt-1 w-64 bg-white rounded-md shadow-lg border overflow-hidden">
                <div className="p-3 border-b">
                  <h3 className="font-medium text-gray-700 mb-2">Select Sub Category</h3>
                  <div className="relative">
                    <FiSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search sub categories..."
                      value={brandSearch}
                      onChange={(e) => setBrandSearch(e.target.value)}
                      className="w-full pl-9 pr-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                <div className="max-h-60 overflow-y-auto p-2">
                  <div
                    className={`px-3 py-2 rounded-md cursor-pointer ${!brand ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'}`}
                    onClick={() => {
                      const params = new URLSearchParams(searchParams.toString());
                      params.delete("brand");
                      router.push(`?${params.toString()}`);
                      setShowBrandDropdown(false);
                      setBrandSearch("");
                    }}
                  >
                    All
                  </div>
                  {filteredBrands.map((b: any) => (
                    <div
                      key={b.id}
                      className={`px-3 py-2 rounded-md cursor-pointer ${brand === b.id ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'}`}
                      onClick={() => {
                        const params = new URLSearchParams(searchParams.toString());
                        params.set("brand", b.id);
                        router.push(`?${params.toString()}`);
                        setShowBrandDropdown(false);
                        setBrandSearch("");
                      }}
                    >
                      {b.name}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sort Dropdown */}
          <div className="relative" ref={sortDropdownRef}>
            <button
              className="flex items-center gap-2 text-gray-700 font-medium py-2 px-4 rounded-md hover:bg-gray-50 transition-colors"
              onClick={() => setShowSortDropdown(!showSortDropdown)}
            >
              <span>Sort By</span>
              <FiChevronDown className={`transition-transform ${showSortDropdown ? 'rotate-180' : ''}`} />
            </button>

            {showSortDropdown && (
              <div className="absolute z-10 mt-1 w-48 bg-white rounded-md shadow-lg border overflow-hidden">
                <div className="p-2">
                  <div
                    className={`px-3 py-2 rounded-md cursor-pointer ${sort === 'newest' ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'}`}
                    onClick={() => {
                      const params = new URLSearchParams(searchParams.toString());
                      params.set("sort", "newest");
                      router.push(`?${params.toString()}`);
                      setShowSortDropdown(false);
                    }}
                  >
                    Newest First
                  </div>
                  <div
                    className={`px-3 py-2 rounded-md cursor-pointer ${sort === 'price-low' ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'}`}
                    onClick={() => {
                      const params = new URLSearchParams(searchParams.toString());
                      params.set("sort", "price-low");
                      router.push(`?${params.toString()}`);
                      setShowSortDropdown(false);
                    }}
                  >
                    Price: Low to High
                  </div>
                  <div
                    className={`px-3 py-2 rounded-md cursor-pointer ${sort === 'price-high' ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'}`}
                    onClick={() => {
                      const params = new URLSearchParams(searchParams.toString());
                      params.set("sort", "price-high");
                      router.push(`?${params.toString()}`);
                      setShowSortDropdown(false);
                    }}
                  >
                    Price: High to Low
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Active Filters */}
        <div className="flex flex-wrap gap-2">
          {category && categories.find((cat: any) => cat.id === category) && (
            <div className="flex items-center bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm">
              <span>Category: {(categories.find((cat: any) => cat.id === category) as any)?.name}</span>
              <button
                className="ml-2 text-blue-500 hover:text-blue-700"
                onClick={() => {
                  const params = new URLSearchParams(searchParams.toString());
                  params.delete("category");
                  router.push(`?${params.toString()}`);
                }}
              >
                &times;
              </button>
            </div>
          )}

          {brand && brands.find((b: any) => b.id === brand) && (
            <div className="flex items-center bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm">
              <span>Sub Category: {(brands.find((b: any) => b.id === brand) as any)?.name}</span>
              <button
                className="ml-2 text-blue-500 hover:text-blue-700"
                onClick={() => {
                  const params = new URLSearchParams(searchParams.toString());
                  params.delete("brand");
                  router.push(`?${params.toString()}`);
                }}
              >
                &times;
              </button>
            </div>
          )}

          {sort && sort !== 'newest' && (
            <div className="flex items-center bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm">
              <span>Sort: {sort === 'price-low' ? 'Price Low to High' : 'Price High to Low'}</span>
              <button
                className="ml-2 text-blue-500 hover:text-blue-700"
                onClick={() => {
                  const params = new URLSearchParams(searchParams.toString());
                  params.set("sort", "newest");
                  router.push(`?${params.toString()}`);
                }}
              >
                &times;
              </button>
            </div>
          )}

          {search && (
            <div className="flex items-center bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm">
              <span>Search: {search}</span>
              <button
                className="ml-2 text-blue-500 hover:text-blue-700"
                onClick={() => {
                  const params = new URLSearchParams(searchParams.toString());
                  params.delete("search");
                  router.push(`?${params.toString()}`);
                  setProductSearch("");
                }}
              >
                &times;
              </button>
            </div>
          )}

          {(category || brand || (sort && sort !== 'newest') || search) && (
            <button
              className="text-blue-600 hover:text-blue-800 text-sm underline"
              onClick={() => {
                router.push('/admin/products');
              }}
            >
              Clear All Filters
            </button>
          )}
        </div>
      </div>

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        {products.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No products found with selected combination.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Sub Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {products.map((product) => (
                  <tr key={product.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {product.name}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {product.category?.name || "N/A"}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {product.brand?.name || "N/A"}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        ₹{product.basePrice}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          product.isActive
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {product.isActive ? "Active" : "Inactive"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-3">
                      <Link
                        href={`/admin/products/edit/${product.id}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={() => toggleStatus(product.id, product.isActive)}
                        className={`${
                          product.isActive
                            ? "text-red-600 hover:text-red-900"
                            : "text-green-600 hover:text-green-900"
                        }`}
                      >
                        {product.isActive ? "Deactivate" : "Activate"}
                      </button>
                      <button
                        onClick={() => openDeleteModal(product.id)}
                        className="text-white bg-red-600 hover:bg-red-700 px-2 py-1 rounded text-xs font-medium"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex justify-center mt-8 mb-4">
            <nav className="flex items-center space-x-2">
              <button
                onClick={() => {
                  if (page > 1) {
                    const params = new URLSearchParams(searchParams.toString());
                    params.set("page", (page - 1).toString());
                    router.push(`?${params.toString()}`);
                  }
                }}
                disabled={page <= 1}
                className={`px-3 py-1 rounded-md ${
                  page <= 1
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-white text-gray-700 hover:bg-gray-50 border"
                }`}
              >
                Previous
              </button>

              {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((pageNum) => (
                <button
                  key={pageNum}
                  onClick={() => {
                    const params = new URLSearchParams(searchParams.toString());
                    params.set("page", pageNum.toString());
                    router.push(`?${params.toString()}`);
                  }}
                  className={`px-3 py-1 rounded-md ${
                    pageNum === page
                      ? "bg-blue-600 text-white"
                      : "bg-white text-gray-700 hover:bg-gray-50 border"
                  }`}
                >
                  {pageNum}
                </button>
              ))}

              <button
                onClick={() => {
                  if (page < pagination.totalPages) {
                    const params = new URLSearchParams(searchParams.toString());
                    params.set("page", (page + 1).toString());
                    router.push(`?${params.toString()}`);
                  }
                }}
                disabled={page >= pagination.totalPages}
                className={`px-3 py-1 rounded-md ${
                  page >= pagination.totalPages
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-white text-gray-700 hover:bg-gray-50 border"
                }`}
              >
                Next
              </button>
            </nav>
          </div>
        )}
      </div>
    </div>

  );
}
