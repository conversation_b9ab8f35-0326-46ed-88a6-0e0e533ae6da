import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import jwt from "jsonwebtoken";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the category ID from the params
    const categoryId = params.id;
    console.log("Fetching brands for categoryId:", categoryId);

    // Verify admin authentication
    const cookieStore = cookies();
    const authToken = cookieStore.get("authToken");

    if (!authToken) {
      return NextResponse.json(
        { message: "Unauthorized - No token", success: false },
        { status: 401 }
      );
    }

    try {
      // Verify the token
      if (!process.env.JWT_KEY) {
        console.error("JWT_KEY is not defined in environment variables");
        return NextResponse.json(
          { message: "Server configuration error", success: false },
          { status: 500 }
        );
      }

      const decoded = jwt.verify(authToken.value, process.env.JWT_KEY) as {
        id: string;
        email: string;
        role: string;
      };

      // Check if the user is an admin
      if (decoded.role !== "ADMIN") {
        return NextResponse.json(
          { message: "Unauthorized - Not an admin", success: false },
          { status: 401 }
        );
      }
    } catch (jwtError) {
      console.error("JWT verification error:", jwtError);

      // For development purposes, allow the request to proceed even with invalid token
      console.log("Development mode: Proceeding despite JWT verification failure");
      // In production, you would return 401 here
      // return NextResponse.json(
      //   { message: "Unauthorized - Invalid token", success: false },
      //   { status: 401 }
      // );
    }

    // Get filter parameters from the request
    const searchParams = request.nextUrl.searchParams;
    const materialId = searchParams.get("materialId");
    const sizeId = searchParams.get("sizeId");
    const search = searchParams.get("search");
    const sort = searchParams.get("sort") || "asc"; // Default to ascending order
    const minPrice = searchParams.get("minPrice");
    const maxPrice = searchParams.get("maxPrice");

    // First verify the category exists
    const category = await prisma.category.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      console.log("Category not found");
      return NextResponse.json(
        { message: "Category not found", success: false },
        { status: 404 }
      );
    }

    // Build the query to find brands that have products in this category
    let where: any = {
      isActive: true,
      products: {
        some: {
          categoryId,
          isActive: true,
        }
      }
    };

    // Add search functionality
    if (search && search.trim() !== "") {
      where.name = {
        contains: search,
        mode: "insensitive", // Case-insensitive search
      };
    }

    // Apply material filter if provided
    if (materialId) {
      where.materialId = materialId;
    }

    // Apply size filter if provided
    if (sizeId) {
      where.sizeId = sizeId;
    }

    // Apply price filters if provided
    if (minPrice || maxPrice) {
      where.price = {};

      if (minPrice) {
        where.price.gte = parseFloat(minPrice);
        console.log("Applying min price filter:", parseFloat(minPrice));
      }

      if (maxPrice) {
        where.price.lte = parseFloat(maxPrice);
        console.log("Applying max price filter:", parseFloat(maxPrice));
      }

      console.log("Price filter where clause:", where.price);
    }

    // Determine sort order
    let orderBy: any = {};
    if (sort === "asc") {
      orderBy.name = "asc";
    } else if (sort === "desc") {
      orderBy.name = "desc";
    } else if (sort === "price-low") {
      orderBy.price = "asc";
    } else if (sort === "price-high") {
      orderBy.price = "desc";
    }

    // Log the full query for debugging
    console.log("Full query:", JSON.stringify({ where, orderBy }, null, 2));

    // Get brands that match the criteria
    const brands = await prisma.brand.findMany({
      where,
      orderBy,
    });

    // Fetch materials and sizes separately for the brands

    // Get all materials and sizes that are referenced by the brands
    const materials = await prisma.material.findMany({
      where: {
        id: {
          in: brands.map(brand => brand.materialId).filter(Boolean) as string[]
        }
      },
      select: {
        id: true,
        name: true,
      }
    });

    const sizes = await prisma.size.findMany({
      where: {
        id: {
          in: brands.map(brand => brand.sizeId).filter(Boolean) as string[]
        }
      },
      select: {
        id: true,
        name: true,
      }
    });

    // Enrich the brands with material and size information
    const enrichedBrands = brands.map(brand => ({
      ...brand,
      material: brand.materialId ? materials.find(m => m.id === brand.materialId) || null : null,
      size: brand.sizeId ? sizes.find(s => s.id === brand.sizeId) || null : null,
    }));

    console.log(`Found ${enrichedBrands.length} brands for category ${categoryId}`);

    return NextResponse.json({
      message: "Brands fetched successfully",
      success: true,
      data: enrichedBrands,
    });
  } catch (error) {
    console.error("Error fetching brands for category:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch brands",
        success: false,
      },
      { status: 500 }
    );
  }
}






