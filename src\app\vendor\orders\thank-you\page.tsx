"use client";
import { useRouter, useSearchParams } from "next/navigation";
import { CheckCircle } from "lucide-react";

export default function ThankYouPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get("orderId");

  return (
    <div className="max-w-2xl mx-auto mt-10 p-6 bg-white rounded-lg shadow">
      <div className="text-center">
        <CheckCircle className="w-16 h-16 text-green-500 mx-auto" />
        <h1 className="mt-4 text-2xl font-bold text-gray-900">
          Thank You for Your Order!
        </h1>
        <p className="mt-2 text-gray-600">
          Your order #{orderId} has been placed successfully.
        </p>
      </div>

      <div className="mt-8 space-y-4">
        <p className="text-sm text-gray-600">
          • Your order is currently being processed
        </p>
        <p className="text-sm text-gray-600">
          • You will receive an email notification with order details
        </p>
        <p className="text-sm text-gray-600">
          • You can track your order status in the Orders section
        </p>
      </div>

      <div className="mt-8 flex justify-center space-x-4">
        <button
          onClick={() => router.push("/vendor/orders")}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          View Orders
        </button>
        <button
          onClick={() => router.push("/vendor/categories")}
          className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Continue Shopping
        </button>
      </div>
    </div>
  );
}
