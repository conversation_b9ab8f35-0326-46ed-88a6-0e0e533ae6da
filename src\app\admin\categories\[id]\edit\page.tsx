"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { adminAPI as api } from "@/lib/axios";
import ImageUploaderNoCrop from "@/components/admin/ImageUploaderNoCrop";
import slugify from "slugify";

const categorySchema = yup.object({
  name: yup.string().required("Category name is required"),
  isActive: yup.boolean(),
});

type CategoryFormData = yup.InferType<typeof categorySchema>;

export default function EditCategoryPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const params = useParams();
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
  } = useForm<CategoryFormData>({
    resolver: yupResolver(categorySchema),
  });

  useEffect(() => {
    const fetchCategory = async () => {
      try {
        setLoading(true);
        const { data } = await api.get(`/categories/${params.id}`);
        if (data.success) {
          reset({
            name: data.data.name,
            isActive: data.data.isActive,
          });

          // Set uploaded images if there's an image URL
          if (data.data.imageUrl) {
            setUploadedImages([data.data.imageUrl]);
          }
        } else {
          setError(data.message || "Failed to load category data");
          toast.error(data.message || "Failed to load category data");
        }
      } catch (err: any) {
        const errorMessage =
          err.response?.data?.message || "Failed to load category data";
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchCategory();
    }
  }, [params.id, reset]);

  const handleImagesUploaded = (imageUrls: string[]) => {
    setUploadedImages(imageUrls);
  };

  const onSubmit = async (data: CategoryFormData) => {
    try {
      if (uploadedImages.length === 0) {
        toast.error("Please upload an image for the category");
        return;
      }

      // Generate slug from name
      const slug = slugify(data.name, { lower: true, strict: true });

      const response = await api.put(`/categories/${params.id}`, {
        ...data,
        slug,
        imageUrl: uploadedImages[0],
      });

      if (response.data.success) {
        toast.success("Category updated successfully");
        router.push("/admin/categories");
      } else {
        throw new Error(response.data.message || "Failed to update category");
      }
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.message || "Failed to update category";
      setError(errorMessage);
      toast.error(errorMessage);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <ToastContainer />
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-lg">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6">
      <ToastContainer />
      <h1 className="text-2xl font-bold mb-6">Edit Category</h1>
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
          {error}
        </div>
      )}

      <form
        onSubmit={handleSubmit(onSubmit)}
        className="space-y-6 bg-white p-6 rounded-lg shadow"
      >
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Category Name <span className="text-red-500">*</span>
          </label>
          <input
            {...register("name")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="Enter category name"
          />
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Category Image <span className="text-red-500">*</span>
          </label>
          <ImageUploaderNoCrop
            onImagesUploaded={handleImagesUploaded}
            multiple={false}
            maxFiles={1}
            existingImages={uploadedImages}
          />
        </div>

        <div>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              {...register("isActive")}
              className="rounded"
            />
            <span className="text-sm font-medium text-gray-700">Active</span>
          </label>
        </div>

        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-4 py-2 border rounded-md hover:bg-gray-100"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300"
          >
            {isSubmitting ? "Updating..." : "Update Category"}
          </button>
        </div>
      </form>
    </div>
  );
}
