import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

export async function GET(request: NextRequest) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
      id: string;
      email: string;
      role: string;
    };

    if (decoded.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Unauthorized - Not an admin", success: false },
        { status: 401 }
      );
    }

    const admin = await prisma.adminUser.findUnique({
      where: { id: decoded.id },
    });

    if (!admin) {
      return NextResponse.json(
        { message: "Admin not found", success: false },
        { status: 404 }
      );
    }

    // Get query parameters for pagination
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "20");
    const skip = (page - 1) * limit;

    // Fetch notifications for this admin
    const notifications = await prisma.notification.findMany({
      where: {
        OR: [
          { recipientId: admin.id, recipientType: "admin" },
          { recipientId: null, recipientType: "admin" }, // General notifications for all admins
        ],
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await prisma.notification.count({
      where: {
        OR: [
          { recipientId: admin.id, recipientType: "admin" },
          { recipientId: null, recipientType: "admin" },
        ],
      },
    });

    // Transform notifications to match the required format
    const transformedNotifications = notifications.map((notification) => {
      const baseNotification = {
        id: notification.id,
        title: notification.title,
        type: notification.type.toLowerCase(),
        desc: notification.body,
        createdAt: notification.createdAt.toLocaleDateString("en-GB"), // DD-MM-YYYY format
        isRead: notification.isRead,
      };

      // Add navigation data if available
      if (notification.data && typeof notification.data === 'object') {
        const data = notification.data as any;
        if (data.order_id) {
          return {
            ...baseNotification,
            order_id: data.order_id,
          };
        }
        if (data.vendor_id) {
          return {
            ...baseNotification,
            vendor_id: data.vendor_id,
          };
        }
      }

      return baseNotification;
    });

    return NextResponse.json({
      message: "Notifications fetched successfully",
      success: true,
      data: {
        notifications: transformedNotifications,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalCount / limit),
          totalCount,
          hasNext: page * limit < totalCount,
          hasPrev: page > 1,
        },
      },
    });
  } catch (error) {
    console.error("Error fetching admin notifications:", error);
    return NextResponse.json(
      { message: "Failed to fetch notifications", success: false },
      { status: 500 }
    );
  }
}

// Mark notification as read
export async function PATCH(request: NextRequest) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
      id: string;
      email: string;
      role: string;
    };

    if (decoded.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Unauthorized - Not an admin", success: false },
        { status: 401 }
      );
    }

    const admin = await prisma.adminUser.findUnique({
      where: { id: decoded.id },
    });

    if (!admin) {
      return NextResponse.json(
        { message: "Admin not found", success: false },
        { status: 404 }
      );
    }

    const { notificationId } = await request.json();

    if (!notificationId) {
      return NextResponse.json(
        { message: "Notification ID is required", success: false },
        { status: 400 }
      );
    }

    // Update notification as read
    const updatedNotification = await prisma.notification.update({
      where: {
        id: notificationId,
        OR: [
          { recipientId: admin.id, recipientType: "admin" },
          { recipientId: null, recipientType: "admin" },
        ],
      },
      data: {
        isRead: true,
      },
    });

    return NextResponse.json({
      message: "Notification marked as read",
      success: true,
      data: updatedNotification,
    });
  } catch (error) {
    console.error("Error updating notification:", error);
    return NextResponse.json(
      { message: "Failed to update notification", success: false },
      { status: 500 }
    );
  }
}

// Create a new notification (admin only)
export async function POST(request: NextRequest) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
      id: string;
      email: string;
      role: string;
    };

    if (decoded.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Unauthorized - Not an admin", success: false },
        { status: 401 }
      );
    }

    const {
      title,
      body,
      type,
      recipientId,
      recipientType,
      data,
    } = await request.json();

    if (!title || !body || !type || !recipientType) {
      return NextResponse.json(
        { message: "Title, body, type, and recipientType are required", success: false },
        { status: 400 }
      );
    }

    const notification = await prisma.notification.create({
      data: {
        title,
        body,
        type: type.toUpperCase(),
        recipientId,
        recipientType,
        data,
      },
    });

    return NextResponse.json({
      message: "Notification created successfully",
      success: true,
      data: notification,
    });
  } catch (error) {
    console.error("Error creating notification:", error);
    return NextResponse.json(
      { message: "Failed to create notification", success: false },
      { status: 500 }
    );
  }
}
