import * as yup from 'yup';

export const vendorSchema = yup.object().shape({
  email: yup.string().email('Invalid email').required('Email is required'),
  businessName: yup.string().required('Business name is required'),
  taxId: yup.string().required('GST number is required'),
  contactPerson: yup.string().required('Contact person is required'),
  phoneNumber: yup
    .string()
    .matches(/^\d{10}$/, 'Phone number must be 10 digits')
    .required('Phone number is required'),
  address: yup.string().required('Address is required'),
  street: yup.string().required('Street is required'),
  city: yup.string().required('City is required'),
  state: yup.string().required('State is required'),
  postalCode: yup.string().required('Postal code is required'),
  country: yup.string().required('Country is required'),
  currentDealers: yup.string().optional(),
});
