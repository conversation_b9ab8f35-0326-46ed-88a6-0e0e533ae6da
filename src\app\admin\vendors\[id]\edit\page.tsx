"use client";
import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import api from "@/lib/axios";

const vendorSchema = yup.object({
  contactPerson: yup.string().required("Contact person name is required"),
  email: yup
    .string()
    .email("Please enter a valid email")
    .required("Email is required"),
  phoneNumber: yup.string().required("Phone number is required"),
  address: yup.string().required("Address is required"),
  isActive: yup.boolean(),
  businessName: yup.string().required("Business name is required"),
  taxId: yup.string().required("Tax ID is required"),
  status: yup.string().required("Status is required"), // Add status to schema
  maxCredit: yup.number().min(0, "Credit amount cannot be negative"),
  isCreditGiven: yup.boolean(),
});

type VendorFormData = yup.InferType<typeof vendorSchema>;

export default function EditVendorPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [isCreditEnabled, setIsCreditEnabled] = useState(false);
  const params = useParams();
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
  } = useForm<VendorFormData>({
    resolver: yupResolver(vendorSchema),
  });

  useEffect(() => {
    const fetchVendor = async () => {
      try {
        const { data } = await api.get(`/admin/vendors/${params.id}`);
        if (data.success) {
          // Set isActive based on status
          const vendorData = {
            ...data.data,
            isActive: data.data.status === "ACTIVE"
          };
          console.log("Loaded vendor data:", vendorData);
          // Set isCreditEnabled state based on vendor data
          setIsCreditEnabled(Boolean(vendorData.isCreditGiven));
          reset(vendorData);
        } else {
          setError(data.message || "Failed to load vendor data");
          toast.error(data.message || "Failed to load vendor data");
        }
      } catch (error: any) {
        console.error("Error fetching vendor:", error);
        const errorMessage =
          error.response?.data?.message || "Failed to load vendor data";
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchVendor();
    }
  }, [params.id, reset]);

  const onSubmit = async (data: VendorFormData) => {
    try {
      // Ensure status is maintained during update
      const updateData = {
        ...data,
        status: data.isActive ? "ACTIVE" : "INACTIVE", // Map isActive to appropriate status
        maxCredit: data.maxCredit || 0, // Ensure maxCredit is a number
        isCreditGiven: Boolean(data.isCreditGiven) // Ensure isCreditGiven is a boolean
      };

      const response = await api.patch(`/admin/vendors/${params.id}`, updateData);

      if (response.data.success) {
        toast.success("Vendor updated successfully");
        router.push("/admin/vendors");
      } else {
        throw new Error(response.data.message || "Failed to update vendor");
      }
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.message || "Failed to update vendor";
      setError(errorMessage);
      toast.error(errorMessage);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <ToastContainer />
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-lg">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <ToastContainer />
      <h1 className="text-2xl font-semibold mb-6">Edit Vendor</h1>
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
          {error}
        </div>
      )}
      <form onSubmit={handleSubmit(onSubmit)} className="max-w-2xl space-y-4">
        <div>
          <label
            htmlFor="businessName"
            className="block text-sm font-medium mb-1"
          >
            Business Name
          </label>
          <input
            {...register("businessName")}
            type="text"
            className="w-full p-2 border rounded-md"
          />
          {errors.businessName && (
            <p className="text-red-500 text-sm mt-1">
              {errors.businessName.message}
            </p>
          )}
        </div>

        <div>
          <label
            htmlFor="contactPerson"
            className="block text-sm font-medium mb-1"
          >
            Contact Person
          </label>
          <input
            {...register("contactPerson")}
            type="text"
            className="w-full p-2 border rounded-md"
          />
          {errors.contactPerson && (
            <p className="text-red-500 text-sm mt-1">
              {errors.contactPerson.message}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium mb-1">
            Email
          </label>
          <input
            {...register("email")}
            type="email"
            className="w-full p-2 border rounded-md"
          />
          {errors.email && (
            <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="phoneNumber" className="block text-sm font-medium mb-1">
            Phone
          </label>
          <input
            {...register("phoneNumber")}
            type="tel"
            className="w-full p-2 border rounded-md"
          />
          {errors.phoneNumber && (
            <p className="text-red-500 text-sm mt-1">{errors.phoneNumber.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="address" className="block text-sm font-medium mb-1">
            Address
          </label>
          <textarea
            {...register("address")}
            className="w-full p-2 border rounded-md"
            rows={3}
          />
          {errors.address && (
            <p className="text-red-500 text-sm mt-1">
              {errors.address.message}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="taxId" className="block text-sm font-medium mb-1">
            Tax ID
          </label>
          <input
            {...register("taxId")}
            type="text"
            className="w-full p-2 border rounded-md"
          />
          {errors.taxId && (
            <p className="text-red-500 text-sm mt-1">{errors.taxId.message}</p>
          )}
        </div>

        <div className="flex items-center gap-2">
          <input
            {...register("isActive")}
            type="checkbox"
            id="isActive"
            className="h-4 w-4"
            onChange={(e) => {
              // Update status when isActive changes
              const newStatus = e.target.checked ? "ACTIVE" : "INACTIVE";
              setValue("status", newStatus);
            }}
          />
          <label htmlFor="isActive" className="text-sm font-medium">
            Active
          </label>
        </div>

        {/* Add a hidden input for status */}
        <input
          type="hidden"
          {...register("status")}
        />

        <div className="border-t pt-4 mt-4">
          <h3 className="text-lg font-medium mb-4">Credit Settings</h3>

          <div className="flex items-center mb-4">
            <input
              type="checkbox"
              id="isCreditGiven"
              {...register("isCreditGiven")}
              className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
              onChange={(e) => {
                // If credit is deactivated, set maxCredit to 0
                if (!e.target.checked) {
                  setValue("maxCredit", 0);
                }
                setIsCreditEnabled(e.target.checked);
              }}
            />
            <label htmlFor="isCreditGiven" className="ml-2 block text-sm font-medium text-gray-700">
              Activate Credit for this Vendor
            </label>
          </div>

          <div>
            <label htmlFor="maxCredit" className="block text-sm font-medium mb-1">
              Maximum Credit Amount (₹)
            </label>
            <input
              type="number"
              id="maxCredit"
              {...register("maxCredit")}
              className="w-full p-2 border rounded-md"
              min="0"
              step="100"
              disabled={!isCreditEnabled}
            />
            {errors.maxCredit && (
              <p className="text-red-500 text-sm mt-1">
                {errors.maxCredit.message}
              </p>
            )}
            <p className="text-sm text-gray-500 mt-1">
              This is the maximum credit amount the vendor can use for purchases.
            </p>
          </div>
        </div>

        <div className="flex gap-4">
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {isSubmitting ? "Updating..." : "Update Vendor"}
          </button>
          <button
            type="button"
            onClick={() => router.back()}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}
