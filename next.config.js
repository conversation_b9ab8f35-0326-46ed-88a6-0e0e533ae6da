/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      "localhost",
      "buymetropolis.com", // For the logo
      "image-url.com",
      "res.cloudinary.com", // If you're using Cloudinary
      "storage.googleapis.com", // If you're using Google Cloud Storage
      "s3.amazonaws.com", // If you're using AWS S3
      "mpolis.blr1.cdn.digitaloceanspaces.com",
      "mpolis.blr1.digitaloceanspaces.com",
      // Add any other domains you need to load images from
    ],
    remotePatterns: [
      {
        protocol: "http",
        hostname: "localhost",
        port: "3000",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "buymetropolis.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "res.cloudinary.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "storage.googleapis.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "s3.amazonaws.com",
        pathname: "/**",
      }
    ],
  },
  experimental: {
    serverComponentsExternalPackages: ["@aws-sdk", "sharp"],
  },
  api: {
    responseLimit: "8mb",
    bodyParser: {
      sizeLimit: "5mb",
    },
  },
};

module.exports = nextConfig;
