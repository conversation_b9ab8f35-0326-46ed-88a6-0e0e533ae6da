"use client";
import { useState, useEffect } from "react";
import { format } from "date-fns";
import { adminAPI as api } from "@/lib/axios";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  FiFilter,
  FiCalendar,
  FiSearch,
  FiRefreshCw,
  FiChevronDown,
  FiChevronUp,
  FiPackage,
  FiTruck,
  FiCheck,
  FiX,
  FiClock,
  FiAlertCircle
} from "react-icons/fi";
import Link from "next/link";

interface OrderItem {
  id: string;
  quantity: number;
  price: number;
  product: {
    id: string;
    name: string;
    images: string[];
  };
}

interface Order {
  id: string;
  totalAmount: number;
  status:
    | "PENDING"
    | "APPROVED"
    | "REJECTED"
    | "PROCESSING"
    | "SHIPPED"
    | "DELIVERED"
    | "CANCELLED";
  shippingAddress: string;
  paymentMethod: string;
  createdAt: string;
  items: OrderItem[];
  vendor: {
    id: string;
    businessName: string;
    email: string;
    contactPerson: string;
    maxCredit?: number;
  };
}

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-blue-100 text-blue-800",
  REJECTED: "bg-red-100 text-red-800",
  PROCESSING: "bg-purple-100 text-purple-800",
  SHIPPED: "bg-indigo-100 text-indigo-800",
  DELIVERED: "bg-green-100 text-green-800",
  CANCELLED: "bg-gray-100 text-gray-800",
};

export default function AdminOrders() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [vendorFilter, setVendorFilter] = useState<string>("all");
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [vendors, setVendors] = useState<{id: string, businessName: string}[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [ordersPerPage] = useState(10);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [totalOrders, setTotalOrders] = useState(0);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchVendors();
  }, []);

  // Fetch orders whenever filters or pagination changes
  useEffect(() => {
    fetchOrders();
  }, [statusFilter, vendorFilter, startDate, endDate, searchQuery, currentPage, ordersPerPage]);

  const fetchVendors = async () => {
    try {
      const { data } = await api.get("/vendors");
      if (data.success) {
        setVendors(data.data.map((vendor: any) => ({
          id: vendor.id,
          businessName: vendor.businessName
        })));
      }
    } catch (error) {
      console.error("Error fetching vendors:", error);
    }
  };

  const fetchOrders = async () => {
    try {
      setLoading(true);

      // Build query parameters for filtering
      const params = new URLSearchParams();

      if (statusFilter !== "all") {
        params.append("status", statusFilter);
      }

      if (vendorFilter !== "all") {
        params.append("vendorId", vendorFilter);
      }

      if (startDate) {
        params.append("startDate", startDate);
        console.log("Adding start date to params:", startDate);
      }

      if (endDate) {
        params.append("endDate", endDate);
        console.log("Adding end date to params:", endDate);
      }

      if (searchQuery) {
        params.append("search", searchQuery);
      }

      params.append("page", currentPage.toString());
      params.append("limit", ordersPerPage.toString());

      console.log("Fetching orders with params:", params.toString());
      const { data } = await api.get(`/orders?${params.toString()}`);

      console.log("Orders API response:", data);
      if (data.success) {
        setOrders(data.data);
        setTotalOrders(data.pagination.total);
        setTotalPages(data.pagination.totalPages);
      }
    } catch (error) {
      console.error("Error fetching orders:", error);
      toast.error("Failed to fetch orders. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const resetFilters = () => {
    setStatusFilter("all");
    setVendorFilter("all");
    setStartDate("");
    setEndDate("");
    setSearchQuery("");
    setCurrentPage(1);
    // fetchOrders will be called automatically due to the dependency array in useEffect
  };

  const updateOrderStatus = async (orderId: string, status: string) => {
    try {
      const { data } = await api.patch(`/orders/${orderId}/status`, {
        status,
      });
      if (data.success) {
        toast.success(`Order status updated to ${status}`);
        fetchOrders();
      }
    } catch (error) {
      console.error("Error updating order status:", error);
      toast.error("Failed to update order status");
    }
  };

  // Status icon mapping
  const getStatusIcon = (status: string) => {
    switch(status) {
      case 'PENDING': return <FiClock className="mr-2" />;
      case 'APPROVED': return <FiCheck className="mr-2" />;
      case 'REJECTED': return <FiX className="mr-2" />;
      case 'PROCESSING': return <FiPackage className="mr-2" />;
      case 'SHIPPED': return <FiTruck className="mr-2" />;
      case 'DELIVERED': return <FiCheck className="mr-2" />;
      case 'CANCELLED': return <FiAlertCircle className="mr-2" />;
      default: return <FiClock className="mr-2" />;
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="mt-4 text-gray-600">Loading orders...</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen p-6">
      <ToastContainer />

      {/* Header and main filter toggle */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h1 className="text-2xl font-bold text-gray-800">Manage Orders</h1>

        <div className="flex items-center gap-2">
          <button
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            className="flex items-center gap-2 bg-white px-4 py-2 rounded-md shadow-sm border hover:bg-gray-50"
          >
            <FiFilter size={16} />
            <span>Filters</span>
            {isFilterOpen ? <FiChevronUp size={16} /> : <FiChevronDown size={16} />}
          </button>

          <button
            onClick={resetFilters}
            className="flex items-center gap-2 bg-white px-4 py-2 rounded-md shadow-sm border hover:bg-gray-50 text-gray-600"
          >
            <FiRefreshCw size={16} />
            <span>Reset</span>
          </button>
        </div>
      </div>

      {/* Advanced Filter Panel */}
      {isFilterOpen && (
        <div className="bg-white p-4 rounded-lg shadow-md mb-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Status Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Statuses</option>
              <option value="PENDING">Pending</option>
              <option value="APPROVED">Approved</option>
              <option value="PROCESSING">Processing</option>
              <option value="SHIPPED">Shipped</option>
              <option value="DELIVERED">Delivered</option>
              <option value="REJECTED">Rejected</option>
              <option value="CANCELLED">Cancelled</option>
            </select>
          </div>

          {/* Vendor Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Vendor</label>
            <select
              value={vendorFilter}
              onChange={(e) => setVendorFilter(e.target.value)}
              className="w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Vendors</option>
              {vendors.map(vendor => (
                <option key={vendor.id} value={vendor.id}>
                  {vendor.businessName}
                </option>
              ))}
            </select>
          </div>

          {/* Date Range Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">From Date</label>
            <div className="flex items-center">
              <FiCalendar className="text-gray-400 absolute ml-3" />
              <input
                type="date"
                value={startDate}
                onChange={(e) => {
                  setStartDate(e.target.value);
                  console.log("Start date selected:", e.target.value);
                }}
                className="w-full border rounded-md pl-10 pr-3 py-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">To Date</label>
            <div className="flex items-center">
              <FiCalendar className="text-gray-400 absolute ml-3" />
              <input
                type="date"
                value={endDate}
                onChange={(e) => {
                  setEndDate(e.target.value);
                  console.log("End date selected:", e.target.value);
                }}
                className="w-full border rounded-md pl-10 pr-3 py-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Search by Order ID */}
          <div className="md:col-span-2 lg:col-span-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Search by Order ID</label>
            <div className="flex items-center">
              <FiSearch className="text-gray-400 absolute ml-3" />
              <input
                type="text"
                placeholder="Enter order ID..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full border rounded-md pl-10 pr-3 py-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>
      )}

      {/* Order count and results summary */}
      <div className="mb-4 flex justify-between items-center">
        <p className="text-gray-600">
          Showing <span className="font-medium">{orders.length}</span> of{" "}
          <span className="font-medium">{totalOrders}</span> orders
        </p>

        {/* Pagination controls */}
        {totalPages > 1 && (
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded ${
                currentPage === 1
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "bg-white text-gray-700 hover:bg-gray-50 border"
              }`}
            >
              Previous
            </button>

            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`px-3 py-1 rounded ${
                  currentPage === page
                    ? "bg-blue-600 text-white"
                    : "bg-white text-gray-700 hover:bg-gray-50 border"
                }`}
              >
                {page}
              </button>
            ))}

            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 rounded ${
                currentPage === totalPages
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "bg-white text-gray-700 hover:bg-gray-50 border"
              }`}
            >
              Next
            </button>
          </div>
        )}
      </div>

      {/* Orders List */}
      <div className="space-y-6">
        {orders.length === 0 ? (
          <div className="text-center py-12 bg-white rounded-lg shadow">
            <p className="text-gray-500 text-lg">No orders found</p>
            <p className="text-gray-400 mt-2">Try adjusting your filters</p>
          </div>
        ) : (
          orders.map((order: Order) => (
            <div key={order.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              {/* Order Header */}
              <div className="p-6 border-b">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                  <div>
                    <div className="flex items-center">
                      <h3 className="font-semibold text-lg">Order #{order.id.slice(-8)}</h3>
                      <span
                        className={`ml-3 px-3 py-1 rounded-full text-xs font-medium ${
                          statusColors[order.status]
                        }`}
                      >
                        <div className="flex items-center">
                          {getStatusIcon(order.status)}
                          {order.status}
                        </div>
                      </span>
                    </div>
                    <p className="text-sm text-gray-500 mt-1">
                      Placed on {format(new Date(order.createdAt), "PPP")}
                    </p>
                  </div>

                  <div className="flex flex-col md:flex-row items-start md:items-center gap-3">
                    <div className="text-right">
                      <p className="text-sm text-gray-500">Total Amount</p>
                      <p className="text-xl font-bold text-blue-600">₹{order.totalAmount}</p>
                    </div>

                    <div>
                      <select
                        className="border rounded px-3 py-2 bg-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        value={order.status}
                        onChange={(e) => updateOrderStatus(order.id, e.target.value)}
                      >
                        <option value="PENDING">Pending</option>
                        <option value="APPROVED">Approved</option>
                        <option value="PROCESSING">Processing</option>
                        <option value="SHIPPED">Shipped</option>
                        <option value="DELIVERED">Delivered</option>
                        <option value="REJECTED">Rejected</option>
                        <option value="CANCELLED">Cancelled</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              {/* Vendor Information */}
              <div className="px-6 py-4 bg-gray-50 border-b">
                <div className="flex flex-col md:flex-row justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Vendor</h4>
                    <p className="font-medium">{order.vendor.businessName}</p>
                    <p className="text-sm text-gray-600">{order.vendor.email}</p>
                    <p className="text-sm text-gray-600">Contact: {order.vendor.contactPerson}</p>
                  </div>

                  {order.paymentMethod === "CREDIT" && (
                    <div className="mt-3 md:mt-0">
                      <h4 className="text-sm font-medium text-gray-500">Credit Information</h4>
                      <p className="text-sm">
                        <span className="font-medium">Credit Limit:</span> ₹{order.vendor.maxCredit || 0}
                      </p>
                      <p className="text-sm">
                        <span className="font-medium">Order Amount:</span> ₹{order.totalAmount}
                      </p>
                      <p className="text-sm mt-1">
                        <span className="font-medium">Payment Method:</span> {order.paymentMethod}
                      </p>
                    </div>
                  )}

                  {order.paymentMethod !== "CREDIT" && (
                    <div className="mt-3 md:mt-0">
                      <h4 className="text-sm font-medium text-gray-500">Payment Information</h4>
                      <p className="text-sm">
                        <span className="font-medium">Payment Method:</span> {order.paymentMethod}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Order Items */}
              <div className="p-6">
                <h4 className="font-medium mb-3">Order Items</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr>
                        <th className="px-3 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Product
                        </th>
                        <th className="px-3 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Price
                        </th>
                        <th className="px-3 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Quantity
                        </th>
                        <th className="px-3 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {order.items.map((item: OrderItem) => (
                        <tr key={item.id}>
                          <td className="px-3 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              <Link 
                                href={`/admin/products/edit/${item.product.id}`}
                                className="font-medium text-blue-600 hover:text-blue-800"
                              >
                                {item.product.name}
                              </Link>
                            </div>
                          </td>
                          <td className="px-3 py-4 whitespace-nowrap text-right text-sm text-gray-500">
                            ₹{item.price}
                          </td>
                          <td className="px-3 py-4 whitespace-nowrap text-right text-sm text-gray-500">
                            {item.quantity}
                          </td>
                          <td className="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                            ₹{item.price * item.quantity}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr>
                        <th colSpan={3} className="px-3 py-3 text-right text-sm font-medium">
                          Total
                        </th>
                        <th className="px-3 py-3 text-right text-sm font-bold">
                          ₹{order.totalAmount}
                        </th>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>

              {/* Shipping Information */}
              <div className="px-6 py-4 bg-gray-50 border-t">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Shipping Address</h4>
                <p className="text-sm">{order.shippingAddress}</p>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination - Bottom */}
      {totalPages > 1 && (
        <div className="mt-6 flex justify-center">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded ${
                currentPage === 1
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "bg-white text-gray-700 hover:bg-gray-50 border"
              }`}
            >
              Previous
            </button>

            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`px-3 py-1 rounded ${
                  currentPage === page
                    ? "bg-blue-600 text-white"
                    : "bg-white text-gray-700 hover:bg-gray-50 border"
                }`}
              >
                {page}
              </button>
            ))}

            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 rounded ${
                currentPage === totalPages
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "bg-white text-gray-700 hover:bg-gray-50 border"
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
