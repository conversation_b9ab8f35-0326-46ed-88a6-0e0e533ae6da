import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

// Get single category
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const category = await prisma.category.findUnique({
      where: { id },
    });

    if (!category) {
      return NextResponse.json(
        {
          message: "Category not found",
          success: false,
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: "Category fetched successfully",
      success: true,
      data: category,
    });
  } catch (error) {
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "An unknown error occurred",
        success: false,
      },
      { status: 500 }
    );
  }
}

// Update category
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();

    const category = await prisma.category.update({
      where: { id },
      data: body,
    });

    return NextResponse.json({
      message: "Category updated successfully",
      success: true,
      data: category,
    });
  } catch (error) {
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "An unknown error occurred",
        success: false,
      },
      { status: 500 }
    );
  }
}

// Delete category
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    await prisma.category.delete({
      where: { id },
    });

    return NextResponse.json(
      {
        message: "Category deleted successfully",
        success: true,
      },
      { status: 200 }
    );
  } catch (error) {
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "An unknown error occurred",
        success: false,
      },
      { status: 500 }
    );
  }
}
