// admin login api

import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";

const JWT_KEY = process.env.JWT_KEY as string;
if (!JWT_KEY) {
  throw new Error("JWT_KEY is not defined");
}

export async function POST(request: NextRequest) {
  try {
    // 1. get user by checking the email
    const { email, password } = await request.json();

    if (!email || !password) {
      throw new Error("Invalid email or password");
    }

    const userWithPassword = await prisma.adminUser.findUnique({
      where: {
        email: email,
      },
    });

    if (!userWithPassword) {
      throw new Error("User not found");
    }

    if (userWithPassword.isActive !== true) {
      return NextResponse.json(
        {
          message: "Account access denied. Please contact support.",
          success: false,
        },
        {
          status: 403,
        }
      );
    }

    // 2. check password

    const isPasswordValid = await bcrypt.compareSync(
      password,
      userWithPassword.password
    );

    if (!isPasswordValid) {
      throw new Error("Invalid password");
    }

    // 3. Check for existing FCM token for this admin user
    const existingFcmToken = await prisma.fCMToken.findFirst({
      where: {
        userId: userWithPassword.id,
        userType: "admin",
        isActive: true,
      },
    });

    // 4. generate token
    const payload = {
      id: userWithPassword.id,
      email: userWithPassword.email,
      role: "ADMIN",
    };

    const token = jwt.sign(payload, JWT_KEY, { expiresIn: "30d" });

    // 5. create response with cookie
    const response = NextResponse.json({
      message: "Login successful",
      success: true,
      data: {
        id: userWithPassword.id,
        email: userWithPassword.email,
        fcmToken: existingFcmToken?.token || null,
        redirectUrl: "/admin/dashboard",
        token: token
      },
    });

    response.cookies.set("authToken", token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/",
      maxAge: 24 * 60 * 60*30, // 30 days
    });
    return response;
  } catch (error: unknown) {
    // Determine appropriate status code based on error message
    let statusCode = 500;
    let errorMessage = "Internal server error";

    if (error instanceof Error) {
      errorMessage = error.message;

      // Set appropriate status codes based on error message
      if (errorMessage === "Invalid password" || errorMessage === "User not found" || errorMessage === "Invalid email or password") {
        statusCode = 401; // Unauthorized
      }
    }

    return NextResponse.json(
      {
        message: errorMessage,
        success: false,
      },
      {
        status: statusCode,
      }
    );
  }
}
