"use client";
import { useEffect, useState } from "react";
import ProductImage from "@/components/vendor/ProductImage";
import { useRouter, useSearchParams } from "next/navigation";
import { vendorAPI as api } from "@/lib/axios";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

interface Product {
  id: string;
  name: string;
  description: string;
  basePrice: number;
  packagingSize: number;
  packagingUnit: string;
  images: string[];
  category: {
    name: string;
  };
  brand: {
    name: string;
    imageAspectRatio?: string;
  };
  material?: {
    name: string;
  };
  size?: {
    name: string;
  };
}

interface SelectedProduct {
  id: string;
  quantity: number;
}

interface Category {
  id: string;
  name: string;
}

interface SubCategory {
  id: string;
  name: string;
}

export default function BrandProductsPage({
  params
}: {
  params: { id: string; brandId: string }
}) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { id: categoryId, brandId } = params;
  const [category, setCategory] = useState<Category | null>(null);
  const [subCategory, setSubCategory] = useState<SubCategory | null>(null);
  const [subcategoryFromUrl, setSubcategoryFromUrl] = useState<SubCategory | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedProducts, setSelectedProducts] = useState<SelectedProduct[]>([]);



  useEffect(() => {
    fetchCategoryDetails();
    fetchProducts();

    // Get subcategory data from URL parameters instead of API call
    const subcategoryParam = searchParams.get('subcategory');
    if (subcategoryParam) {
      try {
        const subcategoryData = JSON.parse(decodeURIComponent(subcategoryParam));
        console.log("Subcategory data from URL:", subcategoryData);
        setSubcategoryFromUrl(subcategoryData);
        setSubCategory(subcategoryData);
      } catch (error) {
        console.error("Error parsing subcategory data:", error);
        // Fallback: extract from products if URL parameter parsing fails
      }
    }
  }, [categoryId, brandId, searchParams]);

  const fetchCategoryDetails = async () => {
    try {
      const { data } = await api.get(`/categories/${categoryId}`);
      if (data.success) {
        setCategory(data.data);
      }
    } catch (error) {
      console.error("Error fetching category details:", error);
    }
  };



  const fetchProducts = async () => {
    try {
      // Use the new API endpoint to get products by category and brand
      const { data } = await api.get(`/categories/${categoryId}/brands/${brandId}/products`);
      console.log("Products API response:", data);
      if (data.success) {
        setProducts(data.data);

        // Only extract sub-category info from products if not already set from URL parameters
        if (data.data.length > 0 && data.data[0].brand && !subcategoryFromUrl) {
          const subCategoryInfo = {
            id: brandId,
            name: data.data[0].brand.name
          };
          console.log("Setting sub-category from products:", subCategoryInfo);
          setSubCategory(subCategoryInfo);
        }
      } else {
        setProducts([]);
      }
    } catch (error) {
      console.error("Error fetching products:", error);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async (productId: string, quantity: number) => {
    try {
      const { data } = await api.post("/cart", { productId, quantity });
      if (data.success) {
        toast.success("Product added to cart successfully");
      }
    } catch (error) {
      console.error("Error adding to cart:", error);
      toast.error("Failed to add product to cart");
    }
  };

  const toggleProductSelection = (product: Product) => {
    setSelectedProducts(prev => {
      // Check if product is already selected
      const existingIndex = prev.findIndex(item => item.id === product.id);

      if (existingIndex >= 0) {
        // If already selected, remove it
        return prev.filter(item => item.id !== product.id);
      } else {
        // If not selected, add it with default quantity of 1 (not packaging size)
        return [...prev, { id: product.id, quantity: 1 }];
      }
    });
  };

  const isProductSelected = (productId: string) => {
    return selectedProducts.some(item => item.id === productId);
  };

  const addSelectedToCart = async () => {
    if (selectedProducts.length === 0) {
      toast.info("Please select at least one product");
      return;
    }

    try {
      // Add each selected product to cart one by one
      for (const product of selectedProducts) {
        await api.post("/cart", {
          productId: product.id,
          quantity: product.quantity
        });
      }

      toast.success(`${selectedProducts.length} products added to cart successfully`);
      setSelectedProducts([]); // Clear selection after adding to cart
    } catch (error) {
      console.error("Error adding selected products to cart:", error);
      toast.error("Failed to add some products to cart");
    }
  };

  const selectAllProducts = () => {
    if (selectedProducts.length === products.length) {
      // If all products are already selected, deselect all
      setSelectedProducts([]);
    } else {
      // Otherwise, select all products with quantity 1
      const allProducts = products.map(product => ({
        id: product.id,
        quantity: 1
      }));
      setSelectedProducts(allProducts);
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <div className="relative w-20 h-20">
          <div className="absolute top-0 left-0 right-0 bottom-0 animate-spin rounded-full h-20 w-20 border-4 border-t-blue-500 border-b-blue-700 border-l-transparent border-r-transparent"></div>
          <div className="absolute top-2 left-2 right-2 bottom-2 animate-pulse bg-white rounded-full flex items-center justify-center">
            <div className="h-8 w-8 text-blue-600">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
            </div>
          </div>
        </div>
        <p className="text-gray-600 font-medium mt-4">Loading products...</p>
      </div>
    );
  }

  console.log("Rendering with sub-category:", subCategory);
  console.log("Rendering with category:", category);

  return (
    <div>
      <ToastContainer />

      {/* Packaging Size Banner Image - At the very top */}
      {subCategory?.packagingSizeImage && (
        <div className="w-full mb-8">
          <img
            src={subCategory.packagingSizeImage}
            alt={`${subCategory.name} packaging size`}
            className="w-full h-48 sm:h-64 md:h-72 lg:h-80 object-cover rounded-lg shadow-lg"
          />
        </div>
      )}

      <div className="mb-8">
        {/* Back button */}
        <button
          onClick={() => router.push(`/vendor/categories/${categoryId}/brands`)}
          className="flex items-center text-blue-600 hover:text-blue-800 mb-4"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to {category?.name} Sub Categories
        </button>

      </div>

      <div className="flex justify-between items-center mb-8">
        <div>
          <h2 className="text-3xl font-bold uppercase tracking-wider">
            {subCategory?.name.toUpperCase()} PRODUCTS
          </h2>
          <div className="mt-2">
            <button
              onClick={selectAllProducts}
              className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {selectedProducts.length === products.length ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8h16M4 16h16" />
                )}
              </svg>
              {selectedProducts.length === products.length ? 'Deselect All' : 'Select All Products'}
            </button>
          </div>
        </div>

        <div className="flex items-center">
          {selectedProducts.length > 0 && (
            <div className="flex items-center">
              <span className="mr-3 text-gray-600 font-medium">
                {selectedProducts.length} product{selectedProducts.length > 1 ? 's' : ''} selected
              </span>
              <button
                onClick={addSelectedToCart}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Add to Cart
              </button>
            </div>
          )}
        </div>
      </div>

      {products.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <p className="text-gray-500 text-lg">No products found with selected combination.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {products.map((product) => (
            <div
              key={product.id}
              className={`bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden h-[450px] flex flex-col ${isProductSelected(product.id) ? 'ring-2 ring-blue-600' : ''}`}
            >
              <div className="relative w-full" style={{ height: '240px' }}>
                {/* Selection checkbox */}
                <div className="absolute top-2 right-2 z-10">
                  <div
                    className={`h-8 w-8 rounded-full flex items-center justify-center cursor-pointer shadow-md transition-all duration-200 ${
                      isProductSelected(product.id)
                        ? 'bg-blue-600 text-white scale-110 ring-2 ring-blue-300'
                        : 'bg-white text-gray-400 hover:bg-gray-100 hover:scale-105'
                    }`}
                    onClick={() => toggleProductSelection(product)}
                  >
                    {isProductSelected(product.id) ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4v16m8-8H4" />
                      </svg>
                    )}
                  </div>
                </div>

                {/* Use ProductImage component with aspect ratio from brand */}
                <ProductImage
                  src={product.images?.[0] || null}
                  alt={product.name}
                  aspectRatio={product.brand?.imageAspectRatio || "9:16"}
                />
              </div>
              <div
                className="p-4 flex-grow flex flex-col"
                onClick={() => toggleProductSelection(product)}
              >
                <h3 className="font-bold text-gray-800 truncate capitalize">{product.name}</h3>

                {/* Description - fixed height section */}
                <div className="min-h-[36px]">
                  {product.description && (
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2 overflow-hidden">
                      {product.description}
                    </p>
                  )}
                </div>

                {/* Product specs - fixed height section */}
                <div className="min-h-[48px] mt-1">
                  {product.size && (
                    <p className="text-sm text-gray-700">
                      <span className="font-medium">Size:</span> {product.size.name}
                    </p>
                  )}
                  {product.material && (
                    <p className="text-sm text-gray-700">
                      <span className="font-medium">Material:</span> {product.material.name}
                    </p>
                  )}
                </div>

                {/* Sub-category info - fixed height section */}
                <div className="min-h-[20px]">
                  {product.brand && (
                    <p className="flex items-center">
                      <span className="font-medium mr-1">Sub Category:</span>
                      <span className="text-blue-600">{product.brand.name}</span>
                    </p>
                  )}
                </div>

                {/* Price and add to cart - fixed at bottom */}
                <div className="mt-auto pt-1 flex justify-between items-end">
                  <div>
                    <span className="text-xl font-bold text-blue-600">
                      ₹{product.basePrice}
                    </span>
                    <span className="text-sm text-gray-500 ml-1">
                      per {product.packagingUnit || 'piece'}
                    </span>
                    <p className="text-xs text-gray-500 mt-1">
                      Pack: {product.packagingSize} {product.packagingUnit || 'piece'}{product.packagingSize > 1 ? 's' : ''}
                    </p>
                  </div>

                  <button
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent triggering the parent onClick
                      addToCart(product.id, 1);
                    }}
                    className="flex items-center justify-center bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    Add
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
