import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import moment from "moment";

// GET dashboard stats
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const startDateParam = url.searchParams.get("startDate");
    const endDateParam = url.searchParams.get("endDate");

    // No longer need this since we're using Moment.js

    // Parse date parameters or use defaults
    let startDate: Date;
    let endDate: Date;

    if (startDateParam && moment(startDateParam, 'YYYY-MM-DD', true).isValid()) {
      // Parse YYYY-MM-DD format using Moment.js
      startDate = moment(startDateParam, 'YYYY-MM-DD').toDate();
    } else {
      // Default to first day of current month
      startDate = moment().startOf('month').toDate();
    }

    if (endDateParam && moment(endDateParam, 'YYYY-MM-DD', true).isValid()) {
      // Parse YYYY-MM-DD format using Moment.js
      // Set to end of day (23:59:59.999)
      endDate = moment(endDateParam, 'YYYY-MM-DD').endOf('day').toDate();
    } else {
      // Default to current date at end of day
      endDate = moment().endOf('day').toDate();
    }

    console.log("Date range for stats:", {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    });

    // Calculate the previous period of the same length
    const periodLength = endDate.getTime() - startDate.getTime();
    const previousEndDate = new Date(startDate.getTime());
    const previousStartDate = new Date(previousEndDate.getTime() - periodLength);

    // Calculate current period vendors (vendors created in the selected period)
    const currentPeriodVendors = await prisma.vendor.count({
      where: {
        createdAt: {
          gte: startDate,
          lt: endDate,
        },
      },
    });

    // Calculate total vendors
    const totalVendors = await prisma.vendor.count();

    // Calculate previous period vendors
    const previousPeriodVendors = await prisma.vendor.count({
      where: {
        createdAt: {
          gte: previousStartDate,
          lt: previousEndDate,
        },
      },
    });

    // Calculate vendor growth
    let vendorGrowth = 0;
    if (previousPeriodVendors > 0) {
      vendorGrowth = Math.round(((currentPeriodVendors - previousPeriodVendors) / previousPeriodVendors) * 100);
    } else if (currentPeriodVendors > 0) {
      vendorGrowth = 100; // If no previous vendors but we have new ones, 100% growth
    }

    // Calculate current period products
    const currentPeriodProducts = await prisma.product.count({
      where: {
        createdAt: {
          gte: startDate,
          lt: endDate,
        },
      },
    });

    // Calculate total products
    const totalProducts = await prisma.product.count();

    // Calculate previous period products
    const previousPeriodProducts = await prisma.product.count({
      where: {
        createdAt: {
          gte: previousStartDate,
          lt: previousEndDate,
        },
      },
    });

    // Calculate product growth
    let productGrowth = 0;
    if (previousPeriodProducts > 0) {
      productGrowth = Math.round(((currentPeriodProducts - previousPeriodProducts) / previousPeriodProducts) * 100);
    } else if (currentPeriodProducts > 0) {
      productGrowth = 100; // If no previous products but we have new ones, 100% growth
    }

    // Calculate current period orders
    const currentPeriodOrders = await prisma.order.count({
      where: {
        createdAt: {
          gte: startDate,
          lt: endDate,
        },
      },
    });

    // Calculate total orders
    const totalOrders = await prisma.order.count();

    // Calculate previous period orders
    const previousPeriodOrders = await prisma.order.count({
      where: {
        createdAt: {
          gte: previousStartDate,
          lt: previousEndDate,
        },
      },
    });

    // Calculate order growth
    let orderGrowth = 0;
    if (previousPeriodOrders > 0) {
      orderGrowth = Math.round(((currentPeriodOrders - previousPeriodOrders) / previousPeriodOrders) * 100);
    } else if (currentPeriodOrders > 0) {
      orderGrowth = 100; // If no previous orders but we have new ones, 100% growth
    }

    // Calculate current period revenue
    const currentPeriodRevenueResult = await prisma.order.aggregate({
      where: {
        createdAt: {
          gte: startDate,
          lt: endDate,
        },
      },
      _sum: {
        totalAmount: true,
      },
    });
    const currentPeriodRevenue = currentPeriodRevenueResult._sum.totalAmount || 0;

    // Calculate previous period revenue
    const previousPeriodRevenueResult = await prisma.order.aggregate({
      where: {
        createdAt: {
          gte: previousStartDate,
          lt: previousEndDate,
        },
      },
      _sum: {
        totalAmount: true,
      },
    });
    const previousPeriodRevenue = previousPeriodRevenueResult._sum.totalAmount || 0;

    // Calculate total revenue
    const totalRevenueResult = await prisma.order.aggregate({
      _sum: {
        totalAmount: true,
      },
    });
    const totalRevenue = totalRevenueResult._sum.totalAmount || 0;

    // Calculate revenue growth
    let revenueGrowth = 0;
    if (previousPeriodRevenue > 0) {
      revenueGrowth = Math.round(((currentPeriodRevenue - previousPeriodRevenue) / previousPeriodRevenue) * 100);
    } else if (currentPeriodRevenue > 0) {
      revenueGrowth = 100; // If no previous revenue but we have new revenue, 100% growth
    }

    // Format revenue for display (convert to lakhs for Indian format)
    // 1 lakh = 100,000
    const formattedRevenue = (currentPeriodRevenue / 100000).toFixed(1) + 'L';

    // Format date range for display using Moment.js
    const formatDate = (date: Date) => {
      return moment(date).format('DD MMM YYYY');
    };

    // Create period name from date range
    const periodName = `${formatDate(startDate)} - ${formatDate(endDate)}`;

    // Comparison text
    const comparisonText = "vs Previous Period";

    return NextResponse.json({
      message: "Dashboard stats fetched successfully",
      success: true,
      data: {
        period: {
          name: periodName,
          key: "custom",
        },
        vendors: {
          total: totalVendors,
          current: currentPeriodVendors,
          growth: vendorGrowth,
        },
        products: {
          total: totalProducts,
          current: currentPeriodProducts,
          growth: productGrowth,
        },
        orders: {
          total: totalOrders,
          current: currentPeriodOrders,
          growth: orderGrowth,
        },
        revenue: {
          total: totalRevenue,
          period: currentPeriodRevenue,
          formatted: formattedRevenue,
          growth: revenueGrowth,
        },
        comparison: comparisonText,
      },
    });
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch dashboard stats",
        success: false,
      },
      { status: 500 }
    );
  }
}
