import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { shippingAddress, paymentMethod, choiceOfTransport } = body;

    // Get cart with items and product details
    const cart = await prisma.cart.findFirst({
      where: { vendorId: session.user.id },
      include: {
        items: {
          include: {
            product: true,
            vendor: true,
          },
        },
      },
    });

    if (!cart || cart.items.length === 0) {
      return NextResponse.json(
        { message: "Cart is empty", success: false },
        { status: 400 }
      );
    }

    // Group cart items by vendor
    const itemsByVendor = cart.items.reduce((acc, item) => {
      const vendorId = item.vendor.id;
      if (!acc[vendorId]) {
        acc[vendorId] = [];
      }
      acc[vendorId].push(item);
      return acc;
    }, {} as Record<string, typeof cart.items>);

    // Create orders for each vendor
    const orders = await Promise.all(
      Object.entries(itemsByVendor).map(async ([vendorId, items]) => {
        // Calculate total amount for this vendor's items
        const totalAmount = items.reduce(
          (sum, item) => sum + item.product.basePrice * item.quantity,
          0
        );

        // Create order for this vendor
        return prisma.order.create({
          data: {
            vendorId: vendorId,
            totalAmount,
            status: "PENDING",
            shippingAddress,
            choiceOfTransport: choiceOfTransport || "",
            paymentMethod,
            items: {
              create: items.map((item) => ({
                productId: item.product.id,
                quantity: item.quantity,
                price: item.product.basePrice,
              })),
            },
          },
          include: {
            items: true,
          },
        });
      })
    );

    // Clear the cart after successful order creation
    await prisma.cart.delete({
      where: { id: cart.id },
    });

    return NextResponse.json({
      message: "Orders created successfully",
      success: true,
      data: orders,
    });
  } catch (error) {
    console.error("Checkout error:", error);
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "An unknown error occurred",
        success: false,
      },
      { status: 500 }
    );
  }
}
