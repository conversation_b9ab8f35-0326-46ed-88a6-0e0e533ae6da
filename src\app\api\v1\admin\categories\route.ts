import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

interface CreateCategoryRequest {
  name: string;
  slug: string;
  imageUrl: string;
  isActive: boolean;
}

// view categories
export async function GET() {
  try {
    const categories = await prisma.category.findMany({
      where: {
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        description: true,
        imageUrl: true,
        isActive: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    console.log("Fetched categories:", categories);

    return NextResponse.json({
      message: "Categories fetched successfully",
      success: true,
      data: categories,
    });
  } catch (error) {
    console.error("Error fetching categories:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch categories",
        success: false,
      },
      { status: 500 }
    );
  }
}

// add categories
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const {
      name,
      slug,
      imageUrl,
      isActive = true,
    }: CreateCategoryRequest = body;

    console.log("Creating category with data:", {
      name,
      slug,
      imageUrl,
      isActive,
    });

    const category = await prisma.category.create({
      data: {
        name,
        slug,
        imageUrl,
        isActive,
      },
    });

    console.log("Created category:", category);

    return NextResponse.json(
      {
        message: "Category created successfully",
        success: true,
        data: category,
      },
      {
        status: 201,
      }
    );
  } catch (error) {
    console.error("Category creation error:", error);
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "An unknown error occurred",
        success: false,
      },
      { status: 500 }
    );
  }
}
