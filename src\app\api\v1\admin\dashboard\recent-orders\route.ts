import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import moment from "moment";

// GET recent orders
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get("limit") || "5");
    const startDateParam = url.searchParams.get("startDate");
    const endDateParam = url.searchParams.get("endDate");

    console.log("Recent orders API called with date range:", { startDateParam, endDateParam });

    // Parse date parameters or use defaults with Moment.js
    let startDate: Date;
    let endDate: Date;

    if (startDateParam && moment(startDateParam, 'YYYY-MM-DD', true).isValid()) {
      // Parse YYYY-MM-DD format using Moment.js
      startDate = moment(startDateParam, 'YYYY-MM-DD').toDate();
    } else {
      // Default to first day of current month
      startDate = moment().startOf('month').toDate();
    }

    if (endDateParam && moment(endDateParam, 'YYYY-MM-DD', true).isValid()) {
      // Parse YYYY-MM-DD format using Moment.js
      // Set to end of day (23:59:59.999)
      endDate = moment(endDateParam, 'YYYY-MM-DD').endOf('day').toDate();
    } else {
      // Default to current date at end of day
      endDate = moment().endOf('day').toDate();
    }

    console.log("Date range for filtering:", {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    });

    // Fetch recent orders for the selected time period
    const recentOrders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lt: endDate,
        },
      },
      take: limit,
      orderBy: {
        createdAt: "desc",
      },
      include: {
        vendor: {
          select: {
            businessName: true,
          },
        },
      },
    });

    console.log(`Found ${recentOrders.length} orders for the selected period`);

    // Format the orders for the dashboard
    const formattedOrders = recentOrders.map((order) => {
      // Format the amount with commas for thousands
      const formattedAmount = new Intl.NumberFormat('en-IN', {
        maximumFractionDigits: 0
      }).format(order.totalAmount);

      return {
        id: order.id,
        vendor: order.vendor.businessName,
        amount: `₹${formattedAmount}`,
        status: order.status,
        createdAt: order.createdAt,
      };
    });

    return NextResponse.json({
      message: "Recent orders fetched successfully",
      success: true,
      data: formattedOrders,
    });
  } catch (error) {
    console.error("Error fetching recent orders:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch recent orders",
        success: false,
      },
      { status: 500 }
    );
  }
}
