import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

export async function GET(request: NextRequest) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
      id: string;
      email: string;
      role: string;
    };

    if (decoded.role !== "VENDOR") {
      return NextResponse.json(
        { message: "Unauthorized - Not a vendor", success: false },
        { status: 401 }
      );
    }

    const vendor = await prisma.vendor.findUnique({
      where: { id: decoded.id },
    });

    if (!vendor) {
      return NextResponse.json(
        { message: "Vendor not found", success: false },
        { status: 404 }
      );
    }

    // Get unread notification count for this vendor
    const unreadCount = await prisma.notification.count({
      where: {
        OR: [
          { recipientId: vendor.id, recipientType: "vendor" },
          { recipientId: null, recipientType: "vendor" }, // General notifications for all vendors
        ],
        isRead: false,
      },
    });

    return NextResponse.json({
      message: "Unread notification count fetched successfully",
      success: true,
      data: {
        unreadCount,
      },
    });
  } catch (error) {
    console.error("Error fetching unread notification count:", error);
    return NextResponse.json(
      { message: "Failed to fetch unread notification count", success: false },
      { status: 500 }
    );
  }
}
