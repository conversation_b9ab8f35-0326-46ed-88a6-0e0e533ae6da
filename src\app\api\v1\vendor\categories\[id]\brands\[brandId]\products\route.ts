import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string; brandId: string } }
) {
  try {
    const { id: categoryId, brandId } = params;
    console.log(`Fetching products for categoryId: ${categoryId}, brandId: ${brandId}`);

    // First verify the category exists
    const category = await prisma.category.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      console.log("Category not found");
      return NextResponse.json(
        { message: "Category not found", success: false },
        { status: 404 }
      );
    }

    // Verify the brand exists
    const brand = await prisma.brand.findUnique({
      where: { id: brandId },
    });

    if (!brand) {
      console.log("Brand not found");
      return NextResponse.json(
        { message: "Brand not found", success: false },
        { status: 404 }
      );
    }

    // Get products that match both category and brand
    const products = await prisma.product.findMany({
      where: {
        categoryId,
        brandId,
        isActive: true,
        isPublished: true,
      },
      select: {
        id: true,
        name: true,
        description: true,
        basePrice: true,
        packagingSize: true,
        packagingUnit: true,
        images: true,
        category: {
          select: {
            name: true,
          },
        },
        brand: {
          select: {
            name: true,
            imageAspectRatio: true,
          },
        },
        material: {
          select: {
            name: true,
          },
        },
        size: {
          select: {
            name: true,
          },
        },
      },
    });

    console.log(`Found ${products.length} products for category ${categoryId} and brand ${brandId}`);

    return NextResponse.json({
      message: "Products fetched successfully",
      success: true,
      data: products,
    });
  } catch (error) {
    console.error("Error fetching products by category and brand:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch products",
        success: false,
      },
      { status: 500 }
    );
  }
}
