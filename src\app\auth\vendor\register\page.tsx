"use client";

import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import api from "@/lib/axios";
import Link from "next/link";
import OtpInput from "react-otp-input";

const vendorSchema = yup.object({
  phoneNumber: yup
    .string()
    .matches(/^[0-9]{10}$/, "Invalid phone number")
    .required("Phone number is required"),
  taxId: yup
    .string()
    .matches(
      /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
      "Invalid GST Number format"
    )
    .required("GST Number is required"),
  businessName: yup.string().required("Business name is required"),
  email: yup.string().email("Invalid email").required("Email is required"),
  contactPerson: yup.string().required("Contact person name is required"),
  address: yup.string().required("Address is required"),
  street: yup.string().required("Street is required"),
  city: yup.string().required("City is required"),
  state: yup.string().required("State is required"),
  postalCode: yup
    .string()
    .matches(/^[0-9]{6}$/, "Invalid postal code")
    .required("Postal code is required"),
  country: yup.string().default("India"),
  currentDealers: yup.string().optional(), // Make it optional
});

type VendorFormData = {
  phoneNumber: string;
  taxId: string;
  businessName: string;
  email: string;
  contactPerson: string;
  address: string;
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  currentDealers?: string; // Make it optional with ?
};

// No need for OTP schema as we're handling validation manually

export default function AddVendor() {
  const router = useRouter();
  const [showOtpVerification, setShowOtpVerification] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState("");
  const [vendorId, setVendorId] = useState("");
  const [otp, setOtp] = useState("");
  const [isVerifyingOtp, setIsVerifyingOtp] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [isGstVerifying, setIsGstVerifying] = useState(false);

  // Add useEffect for countdown timer
  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setInterval(() => {
        setResendTimer((prev) => prev - 1);
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [resendTimer]);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<VendorFormData>({
    resolver: yupResolver(vendorSchema),
    defaultValues: {
      country: "India",
      // Remove currentDealers from defaultValues since it's optional
    },
    mode: "onBlur",
  });

  const taxId = watch("taxId");

  const verifyGSTDetails = async (gstNumber: string) => {
    setIsGstVerifying(true);
    try {
      const { data } = await api.get(
        `/auth/vendor/gst-verify?gst=${gstNumber}`
      );

      if (data.success) {
        setValue("businessName", data.businessName);
        setValue("address", data.address);
        setValue("street", data.street);
        setValue("city", data.city);
        setValue("state", data.state);
        setValue("postalCode", data.pincode);
        toast.success("GST details verified successfully");
      } else {
        // Clear the form fields on verification failure
        setValue("businessName", "");
        setValue("address", "");
        setValue("street", "");
        setValue("city", "");
        setValue("state", "");
        setValue("postalCode", "");
        toast.error("GST verification failed. Please check the GST number.");
      }
    } catch (error) {
      toast.error("Error verifying GST details. Please try again later.");
    } finally {
      setIsGstVerifying(false);
    }
  };

  // Send OTP to phone number
  const sendOtp = async (phoneNumber: string) => {
    try {
      setResendTimer(30);
      const response = await api.post("/auth/vendor/register/send-phone-otp", {
        phoneNumber,
      });

      if (response.data.success) {
        // Always show OTP in toast for development
        toast.success(`OTP: ${response.data.data.otp}`);
        return true;
      } else {
        toast.error(response.data.message || "Failed to send OTP");
        return false;
      }
    } catch (error: any) {
      console.error("Error sending OTP:", error);
      toast.error(
        error.response?.data?.message || "Failed to send OTP. Please try again."
      );
      return false;
    }
  };

  // Verify OTP
  const verifyOtp = async () => {
    if (!otp || otp.length !== 4) {
      toast.error("Please enter a valid 4-digit OTP");
      return;
    }

    setIsVerifyingOtp(true);
    try {
      const response = await api.post("/auth/vendor/register/verify-phone-otp", {
        phoneNumber,
        otp,
        vendorId,
      });

      if (response.data.success) {
        toast.success("Phone number verified successfully!");
        // Redirect to the Thank You page instead of login
        router.push("/auth/vendor/registration-success");
      } else {
        toast.error(response.data.message || "OTP verification failed");
      }
    } catch (error: any) {
      console.error("OTP verification error:", error);
      toast.error(
        error.response?.data?.message || "Failed to verify OTP. Please try again."
      );
    } finally {
      setIsVerifyingOtp(false);
    }
  };

  // Handle resend OTP
  const handleResendOtp = async () => {
    if (resendTimer > 0) return;
    await sendOtp(phoneNumber);
  };

  const onSubmit = async (data: VendorFormData) => {
    try {
      // Save phone number for OTP verification
      setPhoneNumber(data.phoneNumber);

      const vendorData = {
        ...data,
        country: "India",
        status: "PENDING",
      };

      const { data: response } = await api.post(
        "/auth/vendor/register",
        vendorData
      );

      if (response.success) {
        // Save vendor ID for OTP verification
        setVendorId(response.data.id);

        // Send OTP to the phone number
        const otpSent = await sendOtp(data.phoneNumber);

        if (otpSent) {
          // Show OTP verification screen
          setShowOtpVerification(true);
        }
      }
    } catch (error: any) {
      console.error("Vendor creation error:", error);

      // Handle specific error cases
      if (error.response?.status === 409) {
        toast.error("A vendor with these details already exists");
      } else {
        toast.error(
          error.response?.data?.message ||
            "An unexpected error occurred while creating the vendor"
        );
      }
    }
  };
  // Add this function to check if the GST input is valid
  const isValidGST = (gst: string | undefined) => {
    if (!gst) return false;
    const gstPattern =
      /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    return gstPattern.test(gst);
  };
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <ToastContainer />

      {showOtpVerification ? (
        // OTP Verification Form - Centered and improved UI
        <div className="max-w-md w-full bg-white p-6 sm:p-8 md:p-10 rounded-lg shadow-lg border border-gray-100">
          <div className="text-center mb-6 sm:mb-8">
            <h1 className="text-xl sm:text-2xl font-bold text-gray-800 mb-2">
              Register as Dealer
            </h1>
            <p className="text-gray-600 text-sm">
              <Link
                href="/auth/vendor/login"
                className="text-blue-600 hover:text-blue-800"
              >
                Already registered? Login here
              </Link>
            </p>
          </div>

          <div className="space-y-8">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-3">Verify Your Phone Number</h2>
              <div className="bg-blue-50 py-3 px-5 rounded-md inline-flex items-center justify-center">
                <p className="text-gray-700">
                  We've sent a 4-digit OTP to <span className="font-medium text-blue-700 text-lg">{phoneNumber}</span>
                </p>
              </div>
            </div>

            <div className="flex justify-center my-8">
              <div className="flex items-center justify-center gap-2 sm:gap-3">
                <OtpInput
                  value={otp}
                  onChange={setOtp}
                  numInputs={4}
                  renderSeparator={<span className="mx-1"></span>}
                  renderInput={(props) => (
                    <input
                      {...props}
                      className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 text-center text-xl font-bold border-2 border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
                      style={{
                        aspectRatio: "1/1"
                      }}
                    />
                  )}
                />
              </div>
            </div>

            <div className="space-y-4">
              <button
                type="button"
                onClick={verifyOtp}
                disabled={isVerifyingOtp || otp.length !== 4}
                className="w-full py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 font-medium shadow-sm transition duration-150 ease-in-out"
              >
                {isVerifyingOtp ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Verifying...
                  </span>
                ) : (
                  "Verify OTP"
                )}
              </button>

              <div className="text-center pt-4 border-t border-gray-100 mt-4">
                <p className="text-sm text-gray-600 mb-2">
                  Didn't receive the OTP?
                </p>
                <button
                  type="button"
                  onClick={handleResendOtp}
                  disabled={resendTimer > 0}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  {resendTimer > 0
                    ? `Resend OTP in ${resendTimer}s`
                    : "Resend OTP"}
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        // Registration Form
        <div className="max-w-2xl w-full bg-white p-10 rounded-lg shadow-lg border border-gray-100">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-800 mb-2">
            Register as Dealer
            </h1>
            <p className="text-gray-600 text-sm">
              Already registered?{" "}
              <Link
                href="/auth/vendor/login"
                className="text-blue-600 hover:text-blue-800"
              >
                Login here
              </Link>
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                GST Number <span className="text-red-500">*</span>
              </label>
              <div className="flex gap-2">
                <input
                  {...register("taxId")}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder="Enter GST number"
                />
                <button
                  type="button"
                  onClick={() => taxId && verifyGSTDetails(taxId)}
                  disabled={isGstVerifying || !isValidGST(taxId)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {isGstVerifying ? "Verifying..." : "Verify"}
                </button>
              </div>
              {errors.taxId && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.taxId.message}
                </p>
              )}
            </div>
            <div className="col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone Number <span className="text-red-500">*</span>
              </label>
              <input
                {...register("phoneNumber")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter 10-digit phone number"
              />
              {errors.phoneNumber && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.phoneNumber.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Business Name <span className="text-red-500">*</span>
              </label>
              <input
                {...register("businessName")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter business name"
              />
              {errors.businessName && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.businessName.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                {...register("email")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter email"
              />
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.email.message}
                </p>
              )}
            </div>



            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Contact Person <span className="text-red-500">*</span>
              </label>
              <input
                {...register("contactPerson")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter contact person name"
              />
              {errors.contactPerson && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.contactPerson.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Address <span className="text-red-500">*</span>
              </label>
              <input
                {...register("address")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter address"
              />
              {errors.address && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.address.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Street <span className="text-red-500">*</span>
              </label>
              <input
                {...register("street")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter street"
              />
              {errors.street && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.street.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                City <span className="text-red-500">*</span>
              </label>
              <input
                {...register("city")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter city"
              />
              {errors.city && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.city.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                State <span className="text-red-500">*</span>
              </label>
              <input
                {...register("state")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter state"
              />
              {errors.state && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.state.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Postal Code <span className="text-red-500">*</span>
              </label>
              <input
                {...register("postalCode")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter postal code"
              />
              {errors.postalCode && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.postalCode.message}
                </p>
              )}
            </div>
          </div>

          <div className="col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Which all brands are you currently dealing with?
            </label>
            <input
              {...register("currentDealers")}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder="Enter brands you are currently dealing with"
            />
            {errors.currentDealers && (
              <p className="text-red-500 text-sm mt-1">
                {errors.currentDealers.message}
              </p>
            )}
          </div>

          <div className="col-span-2">
            <button
              type="submit"
              disabled={isSubmitting || isGstVerifying}
              className="w-full py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 font-medium shadow-sm transition duration-150 ease-in-out"
            >
              {isSubmitting ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Registering...
                </span>
              ) : (
                "Register"
              )}
            </button>
          </div>
          </form>
        </div>
      )}
    </div>
  );
}
