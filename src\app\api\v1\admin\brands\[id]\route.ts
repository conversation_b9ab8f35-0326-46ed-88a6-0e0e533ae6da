import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

// Get a specific brand
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const brand = await prisma.brand.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        name: true,
        description: true,
        imageUrl: true,
        imageAspectRatio: true,
        price: true,
        materialId: true,
        sizeId: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!brand) {
      return NextResponse.json(
        {
          message: "Brand not found",
          success: false,
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: "Brand fetched successfully",
      success: true,
      data: brand,
    });
  } catch (error) {
    console.error("Error fetching brand:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch brand",
        success: false,
      },
      { status: 500 }
    );
  }
}

// Update a brand
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { name, description, isActive, imageUrl, imageAspectRatio, price, materialId, sizeId } = await request.json();

    // Check if brand exists
    const existingBrand = await prisma.brand.findUnique({
      where: { id: params.id },
    });

    if (!existingBrand) {
      return NextResponse.json(
        {
          message: "Brand not found",
          success: false,
        },
        { status: 404 }
      );
    }

    // Check if another brand with the same name exists
    if (name && name !== existingBrand.name) {
      const brandWithSameName = await prisma.brand.findUnique({
        where: { name },
      });

      if (brandWithSameName) {
        return NextResponse.json(
          {
            message: "Brand with this name already exists",
            success: false,
          },
          { status: 400 }
        );
      }
    }

    const updatedBrand = await prisma.brand.update({
      where: { id: params.id },
      data: {
        name: name !== undefined ? name : undefined,
        description: description !== undefined ? description : undefined,
        isActive: isActive !== undefined ? isActive : undefined,
        imageUrl: imageUrl !== undefined ? imageUrl : undefined,
        imageAspectRatio: imageAspectRatio !== undefined ? imageAspectRatio : undefined,
        price: price !== undefined ? price : undefined,
        materialId: materialId !== undefined ? materialId : undefined,
        sizeId: sizeId !== undefined ? sizeId : undefined,
      },
    });

    return NextResponse.json({
      message: "Brand updated successfully",
      success: true,
      data: updatedBrand,
    });
  } catch (error) {
    console.error("Error updating brand:", error);
    return NextResponse.json(
      {
        message: "Failed to update brand",
        success: false,
      },
      { status: 500 }
    );
  }
}

// Delete a brand
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if brand exists
    const existingBrand = await prisma.brand.findUnique({
      where: { id: params.id },
      include: {
        products: {
          select: { id: true },
          take: 1,
        },
        priceMasters: {
          select: { id: true },
          take: 1,
        },
      },
    });

    if (!existingBrand) {
      return NextResponse.json(
        {
          message: "Brand not found",
          success: false,
        },
        { status: 404 }
      );
    }

    // Check if brand is being used by products or price masters
    if (existingBrand.products.length > 0 || existingBrand.priceMasters.length > 0) {
      return NextResponse.json(
        {
          message: "Cannot delete brand as it is being used by products or price masters",
          success: false,
        },
        { status: 400 }
      );
    }

    await prisma.brand.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      message: "Brand deleted successfully",
      success: true,
    });
  } catch (error) {
    console.error("Error deleting brand:", error);
    return NextResponse.json(
      {
        message: "Failed to delete brand",
        success: false,
      },
      { status: 500 }
    );
  }
}
