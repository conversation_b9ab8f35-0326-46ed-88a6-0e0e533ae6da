"use client";

import { useState, useEffect } from "react";
import { toast, ToastContainer } from "react-toastify";
import { adminAPI as api } from "@/lib/axios";
import "react-toastify/dist/ReactToastify.css";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { PencilIcon, TrashIcon } from "@heroicons/react/24/outline";
import { FiTag, FiLayers, FiGrid } from "react-icons/fi";
import ImageUploaderNoCrop from "@/components/admin/ImageUploaderNoCrop";

interface Material {
  id: string;
  name: string;
  isActive: boolean;
}

interface Size {
  id: string;
  name: string;
  isActive: boolean;
}

interface Brand {
  id: string;
  name: string;
  imageUrl: string | null;
  price: number | null;
  materialId: string | null;
  sizeId: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function BrandsPage() {
  const router = useRouter();
  const [brands, setBrands] = useState<Brand[]>([]);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: "",
    price: "",
    materialId: "",
    sizeId: "",
    imageAspectRatio: "1:1",
  });
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [sizes, setSizes] = useState<Size[]>([]);
  const [loadingMaterials, setLoadingMaterials] = useState(false);
  const [loadingSizes, setLoadingSizes] = useState(false);

  useEffect(() => {
    fetchBrands();
    fetchMaterials();
    fetchSizes();
  }, []);

  const fetchBrands = async () => {
    try {
      setLoading(true);
      const { data } = await api.get("/brands");
      if (data.success) {
        setBrands(data.data);
      }
    } catch (error) {
      console.error("Error fetching brands:", error);
      toast.error("Failed to fetch sub categories");
    } finally {
      setLoading(false);
    }
  };

  const fetchMaterials = async () => {
    try {
      setLoadingMaterials(true);
      const { data } = await api.get("/materials");
      if (data.success) {
        setMaterials(data.data);
      } else {
        toast.error("Failed to fetch materials");
      }
    } catch (error) {
      console.error("Error fetching materials:", error);
      toast.error("Failed to fetch materials");
    } finally {
      setLoadingMaterials(false);
    }
  };

  const fetchSizes = async () => {
    try {
      setLoadingSizes(true);
      const { data } = await api.get("/sizes");
      if (data.success) {
        setSizes(data.data);
      } else {
        toast.error("Failed to fetch sizes");
      }
    } catch (error) {
      console.error("Error fetching sizes:", error);
      toast.error("Failed to fetch sizes");
    } finally {
      setLoadingSizes(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleImagesUploaded = (imageUrls: string[]) => {
    setUploadedImages(imageUrls);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name) {
      toast.error("Sub Category name is required");
      return;
    }

    try {
      setIsSubmitting(true);
      const { data } = await api.post("/brands", {
        name: formData.name,
        price: formData.price ? parseFloat(formData.price) : undefined,
        materialId: formData.materialId || undefined,
        sizeId: formData.sizeId || undefined,
        imageUrl: uploadedImages.length > 0 ? uploadedImages[0] : undefined,
        imageAspectRatio: formData.imageAspectRatio,
      });

      if (data.success) {
        toast.success("Sub Category created successfully");
        setFormData({
          name: "",
          price: "",
          materialId: "",
          sizeId: "",
          imageAspectRatio: "1:1",
        });
        setUploadedImages([]);
        fetchBrands();
      }
    } catch (error) {
      console.error("Error creating sub category:", error);
      toast.error("Failed to create sub category");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this sub category?")) {
      return;
    }

    try {
      const { data } = await api.delete(`/brands/${id}`);
      if (data.success) {
        toast.success("Sub Category deleted successfully");
        fetchBrands();
      }
    } catch (error: any) {
      console.error("Error deleting sub category:", error);
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Failed to delete sub category");
      }
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <ToastContainer />
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold flex items-center"><FiTag className="mr-2" /> Sub Categories</h1>
        <div className="flex space-x-4">
          <Link href="/admin/materials" className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
            <FiLayers className="mr-2" /> Materials
          </Link>
          <Link href="/admin/sizes" className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
            <FiGrid className="mr-2" /> Sizes
          </Link>

        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow mb-8">
        <h2 className="text-xl font-semibold mb-4">Add New Sub Category</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Sub Category Name</label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Price</label>
            <input
              type="text"
              name="price"
              value={formData.price}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder="Enter price (optional)"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Material</label>
            <select
              name="materialId"
              value={formData.materialId}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="">Select Material (optional)</option>
              {materials.map((material) => (
                <option key={material.id} value={material.id}>
                  {material.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Size</label>
            <select
              name="sizeId"
              value={formData.sizeId}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="">Select Size (optional)</option>
              {sizes.map((size) => (
                <option key={size.id} value={size.id}>
                  {size.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Image Aspect Ratio <span className="text-red-500">*</span></label>
            <select
              name="imageAspectRatio"
              value={formData.imageAspectRatio}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="1:1">Square (1:1)</option>
              <option value="9:16">Portrait (9:16)</option>
            </select>
            <p className="mt-1 text-xs text-gray-500">
              This aspect ratio will be used for products in this sub category
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Sub Category Image</label>
            <ImageUploaderNoCrop
              onImagesUploaded={handleImagesUploaded}
              multiple={false}
              maxFiles={1}
              existingImages={uploadedImages}
              imageAspectRatio="1:1"
            />
            <p className="mt-1 text-xs text-gray-500">
              Sub category images will always be uploaded in 1:1 ratio (1000x1000px)
            </p>
            <p className="mt-1 text-xs text-gray-500">
              The selected aspect ratio will be used for products in this sub category
            </p>
          </div>


          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-green-300"
            >
              {isSubmitting ? "Adding..." : "Add Sub Category"}
            </button>
          </div>
        </form>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">Sub Categories List</h2>
        {loading ? (
          <p className="text-center py-4">Loading sub categories...</p>
        ) : brands.length === 0 ? (
          <p className="text-center py-4">No sub categories found</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Image
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Material
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Size
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {brands.map((brand) => (
                  <tr key={brand.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {brand.name}
                    </td>
                    <td className="px-6 py-4">
                      {brand.imageUrl ? (
                        <img
                          src={brand.imageUrl}
                          alt={brand.name}
                          className="h-16 w-16 object-cover rounded-md"
                        />
                      ) : (
                        <span className="text-gray-400">No image</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {brand.price ? `₹${brand.price}` : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {brand.materialId ? materials.find(m => m.id === brand.materialId)?.name || '-' : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {brand.sizeId ? sizes.find(s => s.id === brand.sizeId)?.name || '-' : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex gap-2">
                        <button
                          onClick={() => router.push(`/admin/brands/${brand.id}/edit`)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <PencilIcon className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => handleDelete(brand.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
