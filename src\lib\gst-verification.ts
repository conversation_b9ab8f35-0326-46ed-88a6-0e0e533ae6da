import axios from "axios";

interface GSTVerificationResponse {
  success: boolean;
  message?: string;
  businessName?: string;
  address?: string;
  street?: string;
  city?: string;
  state?: string;
  pincode?: string;
  gstDetails?: {
    gstin: string;
    panNumber: string;
    legalName: string;
    businessName: string;
    status: string;
    registrationDate: string;
    businessType: string;
    taxpayerType: string;
    natureOfBusiness: string[];
  };
}

/**
 * Extract address components from a full address string
 * Handles complex Indian addresses with multiple location identifiers
 * @param address Full address string
 * @returns Object with extracted address components
 */
function extractAddressComponents(address: string): { street: string; city: string; pincode: string } {
  // Default values
  let street = "";
  let city = "";
  let pincode = "";
  console.log("Extracting address components from:", address);

  if (!address) {
    return { street, city, pincode };
  }

  // Split the address by commas
  const addressParts = address.split(',').map(part => part.trim());

  // Try to extract pincode - Indian pincodes are 6 digits
  const pincodeRegex = /\b\d{6}\b/;
  let pincodePartIndex = -1;

  // Find which part contains the pincode
  for (let i = 0; i < addressParts.length; i++) {
    const match = addressParts[i].match(pincodeRegex);
    if (match) {
      pincode = match[0];
      pincodePartIndex = i;
      break;
    }
  }

  // If pincode found
  if (pincodePartIndex !== -1) {
    // In Indian addresses, city is typically 2-3 parts before the pincode
    // Common pattern: [street parts], [area], [city], [state], [pincode]

    // Extract city - usually 2 parts before pincode or the part right before state
    const stateIndex = pincodePartIndex - 1; // State is usually right before pincode

    if (stateIndex >= 0) {
      // Check if there are repeated city names (like "Ghaziabad, Ghaziabad")
      if (stateIndex > 0 &&
          addressParts[stateIndex - 1].toLowerCase() === addressParts[stateIndex].toLowerCase()) {
        // Use the repeated name as city
        city = addressParts[stateIndex - 1];
      } else if (stateIndex > 0) {
        // Use the part before state as city
        city = addressParts[stateIndex - 1];
      }
    }

    // If city is still empty, try to find it by looking for common city names
    if (!city && stateIndex > 1) {
      city = addressParts[stateIndex - 1];
    }

    // Street is everything before the city
    const cityIndex = addressParts.findIndex(part =>
      part.toLowerCase() === city.toLowerCase());

    if (cityIndex > 0) {
      street = addressParts.slice(0, cityIndex).join(', ');
    } else {
      // If city not found in parts, use everything except state and pincode as street
      street = addressParts.slice(0, pincodePartIndex - 1).join(', ');
    }
  } else {
    // If no pincode found, make a best guess
    if (addressParts.length >= 3) {
      // Assume second-to-last part is city, last part might be state
      city = addressParts[addressParts.length - 2];
      // Assume first parts are street
      street = addressParts.slice(0, addressParts.length - 2).join(', ');
    } else if (addressParts.length === 2) {
      street = addressParts[0];
      city = addressParts[1];
    } else {
      street = address;
    }
  }

  console.log("Extracted address components:", { street, city, pincode });
  return { street, city, pincode };
}


/**
 * Verify GST number using Surepass API
 * @param gstNumber The GST number to verify
 * @returns Promise with verification result
 */
export async function verifyGSTNumber(gstNumber: string): Promise<GSTVerificationResponse> {
  try {
    // Get the API token from environment variables
    const apiToken = process.env.SUREPASS_API_TOKEN;

    if (!apiToken) {
      console.error("SUREPASS_API_TOKEN is not defined in environment variables");
      return {
        success: false,
        message: "API configuration error"
      };
    }

    // Make the API call to Surepass GST API
    const response = await axios({
      method: 'post',
      url: 'https://sandbox.surepass.io/api/v1/corporate/gstin',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiToken}`
      },
      data: {
        id_number: gstNumber
      }
    });

    // Check if the API call was successful
    if (response.data && response.data.success) {
      const gstData = response.data.data;

      // Extract address components
      const fullAddress = gstData.address || "";
      const { street, city, pincode } = extractAddressComponents(fullAddress);

      // Get state from state_jurisdiction
      let state = "";
      if (gstData.state_jurisdiction) {
        const stateMatch = gstData.state_jurisdiction.match(/State\s*-\s*([^,]+)/i);
        if (stateMatch && stateMatch[1]) {
          state = stateMatch[1].trim();
        }
      }

      // Format the response to match our application's expected format
      return {
        success: true,
        businessName: gstData.business_name || gstData.legal_name,
        address: fullAddress,
        street: street,
        city: city,
        state: state,
        pincode: pincode,
        gstDetails: {
          gstin: gstData.gstin,
          panNumber: gstData.pan_number,
          legalName: gstData.legal_name,
          businessName: gstData.business_name,
          status: gstData.gstin_status,
          registrationDate: gstData.date_of_registration,
          businessType: gstData.constitution_of_business,
          taxpayerType: gstData.taxpayer_type,
          natureOfBusiness: gstData.nature_bus_activities,
        }
      };
    } else {
      // Handle API error response
      return {
        success: false,
        message: response.data?.message || "Failed to verify GST details"
      };
    }
  } catch (error) {
    console.error("GST verification error:", error);

    // Handle different types of errors
    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data?.message || "Failed to verify GST details";

      return {
        success: false,
        message: errorMessage
      };
    }

    return {
      success: false,
      message: "Failed to verify GST details"
    };
  }
}
