"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  ShoppingCart,
  ClipboardList,
  Home,
  ShoppingBag,
  UserX,
  Menu,
  X
} from "lucide-react";
import { useState, useEffect } from "react";

export default function Sidebar() {
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if we're on mobile when component mounts and when window resizes
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Close mobile menu when navigating to a new page
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [pathname]);

  const isActive = (path: string) => pathname === path ||
    (path !== "/vendor/dashboard" && pathname.startsWith(path));

  const navigation = [
    {
      name: "Dashboard",
      href: "/vendor/dashboard",
      icon: Home,
    },
    {
      name: "Products",
      href: "/vendor/products",
      icon: ShoppingBag,
    },
    {
      name: "Cart",
      href: "/vendor/cart",
      icon: ShoppingCart,
    },
    {
      name: "Orders",
      href: "/vendor/orders",
      icon: ClipboardList,
    },
    // {
    //   name: "Shipments",
    //   href: "/vendor/shipments",
    //   icon: Package,
    // },
    // {
    //   name: "Payments",
    //   href: "/vendor/payments",
    //   icon: CreditCard,
    // },
    // {
    //   name: "Profile",
    //   href: "/vendor/profile",
    //   icon: User,
    // },
  ];

  return (
    <>
      {/* Mobile menu toggle button - only visible on mobile */}
      {isMobile && (
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="md:hidden fixed top-4 left-4 z-30 p-2 rounded-md bg-white shadow-md"
          aria-label="Toggle menu"
        >
          {isMobileMenuOpen ? (
            <X className="h-6 w-6 text-gray-600" />
          ) : (
            <Menu className="h-6 w-6 text-gray-600" />
          )}
        </button>
      )}

      {/* Sidebar for desktop and mobile */}
      <div
        className={`${
          isMobile
            ? `fixed inset-y-0 left-0 z-20 transform ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'} transition-transform duration-300 ease-in-out`
            : 'relative'
        } w-64 bg-white shadow-sm h-full border-r flex-shrink-0 overflow-y-auto`}
      >
        <div className="py-6">
          <div className="px-6 mb-8">
            <h2 className="text-lg font-medium text-gray-900 mb-2">Vendor Portal</h2>
            <div className="h-0.5 w-12 bg-blue-600"></div>
          </div>
          <nav className="space-y-1">
            <div className="mb-4 px-6">
              <p className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
                Shop
              </p>
              {navigation.slice(0, 4).map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`flex items-center py-2.5 px-3 text-sm font-medium transition-colors rounded-md mb-1 ${
                      isActive(item.href)
                        ? "text-blue-600 bg-blue-50"
                        : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                    }`}
                  >
                    <Icon className={`w-5 h-5 mr-3 flex-shrink-0 ${isActive(item.href) ? "text-blue-600" : "text-gray-500"}`} />
                    <span>{item.name}</span>
                  </Link>
                );
              })}
            </div>

            <div className="px-6">
              <p className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
                Account
              </p>
              <Link
                href="/vendor/delete-account"
                className={`flex items-center py-2.5 px-3 text-sm font-medium transition-colors rounded-md mb-1 ${
                  isActive("/vendor/delete-account")
                    ? "text-red-600 bg-red-50"
                    : "text-gray-700 hover:bg-gray-50 hover:text-red-600"
                }`}
              >
                <UserX className={`w-5 h-5 mr-3 flex-shrink-0 ${isActive("/vendor/delete-account") ? "text-red-600" : "text-gray-500"}`} />
                <span>Delete Account</span>
              </Link>
            </div>
          </nav>
        </div>
      </div>

      {/* Overlay for mobile menu */}
      {isMobile && isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-10"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </>
  );
}
