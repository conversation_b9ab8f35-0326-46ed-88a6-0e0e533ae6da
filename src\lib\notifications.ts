import { prisma } from "@/lib/prisma";
import { NotificationType } from "@prisma/client";

interface CreateNotificationParams {
  title: string;
  body: string;
  type: NotificationType;
  recipientId?: string | null; // If null, it's a general notification for all users of that type
  recipientType: "vendor" | "admin";
  data?: any; // Additional data for navigation (e.g., order_id, vendor_id)
}

export async function createNotification({
  title,
  body,
  type,
  recipientId,
  recipientType,
  data,
}: CreateNotificationParams) {
  try {
    const notification = await prisma.notification.create({
      data: {
        title,
        body,
        type,
        recipientId,
        recipientType,
        data,
      },
    });

    console.log(`Notification created: ${notification.id} for ${recipientType} ${recipientId || 'all'}`);
    return notification;
  } catch (error) {
    console.error("Error creating notification:", error);
    throw error;
  }
}

// Helper functions for common notification types

export async function createOrderNotification({
  orderId,
  vendorId,
  title,
  body,
  type,
}: {
  orderId: string;
  vendorId: string;
  title: string;
  body: string;
  type: NotificationType;
}) {
  return createNotification({
    title,
    body,
    type,
    recipientId: vendorId,
    recipientType: "vendor",
    data: { order_id: orderId },
  });
}

export async function createVendorRegistrationNotification({
  vendorId,
  vendorName,
}: {
  vendorId: string;
  vendorName: string;
}) {
  // Notification for admin about new vendor registration
  return createNotification({
    title: "New Vendor Registration",
    body: `${vendorName} has registered and is pending approval.`,
    type: "VENDOR_REGISTRATION",
    recipientId: undefined, // Send to all admins
    recipientType: "admin",
    data: { vendor_id: vendorId },
  });
}

export async function createAccountStatusNotification({
  vendorId,
  status,
}: {
  vendorId: string;
  status: string;
}) {
  const statusMessages = {
    ACTIVE: "Your account has been approved and is now active.",
    SUSPENDED: "Your account has been suspended. Please contact support.",
    INACTIVE: "Your account has been deactivated.",
    PENDING: "Your account is pending approval.",
  };

  return createNotification({
    title: "Account Status Update",
    body: statusMessages[status as keyof typeof statusMessages] || `Your account status has been updated to ${status}.`,
    type: "ACCOUNT_STATUS_CHANGED",
    recipientId: vendorId,
    recipientType: "vendor",
  });
}

export async function createPaymentNotification({
  vendorId,
  amount,
  orderId,
}: {
  vendorId: string;
  amount: number;
  orderId: string;
}) {
  return createNotification({
    title: "Payment Received",
    body: `Payment of ₹${amount} has been received for order ${orderId}.`,
    type: "PAYMENT_RECEIVED",
    recipientId: vendorId,
    recipientType: "vendor",
    data: { order_id: orderId },
  });
}

export async function createGeneralNotification({
  title,
  body,
  recipientType,
  recipientId,
}: {
  title: string;
  body: string;
  recipientType: "vendor" | "admin";
  recipientId?: string | null;
}) {
  return createNotification({
    title,
    body,
    type: "GENERAL",
    recipientId,
    recipientType,
  });
}
