import * as admin from 'firebase-admin';

// Initialize Firebase Admin SDK if it hasn't been initialized already
if (!admin.apps.length) {
  try {
    // Check if we have a service account key in environment variables
    const serviceAccount = process.env.NEXT_PUBLIC_FIREBASE_SERVICE_ACCOUNT
      ? JSON.parse(process.env.NEXT_PUBLIC_FIREBASE_SERVICE_ACCOUNT)
      : undefined;

    admin.initializeApp({
      credential: serviceAccount
        ? admin.credential.cert(serviceAccount)
        : admin.credential.applicationDefault(),
      // Add any other configuration options here
    });

    console.log('Firebase Admin SDK initialized successfully');
  } catch (error) {
    console.error('Error initializing Firebase Admin SDK:', error);
  }
}

/**
 * Send a notification to a specific device
 * @param token FCM token of the device
 * @param title Notification title
 * @param body Notification body
 * @param data Additional data to send with the notification
 * @returns Promise with the messaging response
 */
export async function sendNotificationToDevice(
  token: string,
  title: string,
  body: string,
  data?: Record<string, string>
) {
  try {
    const message: admin.messaging.Message = {
      notification: {
        title,
        body,
      },
      data,
      token,
    };

    const response = await admin.messaging().send(message);
    console.log('Successfully sent message:', response);
    return { success: true, messageId: response };
  } catch (error) {
    console.error('Error sending message:', error);
    return { success: false, error };
  }
}

/**
 * Send a notification to multiple devices
 * @param tokens Array of FCM tokens
 * @param title Notification title
 * @param body Notification body
 * @param data Additional data to send with the notification
 * @returns Promise with the messaging response
 */
export async function sendNotificationToDevices(
  tokens: string[],
  title: string,
  body: string,
  data?: Record<string, string>
) {
  try {
    // Create a message for each token
    const successResults = [];
    const failureResults = [];

    // Send messages one by one
    for (const token of tokens) {
      try {
        const message: admin.messaging.Message = {
          notification: {
            title,
            body,
          },
          data,
          token,
        };

        const response = await admin.messaging().send(message);
        successResults.push(response);
      } catch (err) {
        failureResults.push({ token, error: err });
      }
    }

    console.log(
      `Successfully sent message to ${successResults.length} devices. Failed: ${failureResults.length}`
    );

    return {
      success: true,
      successCount: successResults.length,
      failureCount: failureResults.length,
      responses: successResults,
      failures: failureResults
    };
  } catch (error) {
    console.error('Error sending messages:', error);
    return { success: false, error };
  }
}

/**
 * Send a notification to a topic
 * @param topic Topic name
 * @param title Notification title
 * @param body Notification body
 * @param data Additional data to send with the notification
 * @returns Promise with the messaging response
 */
export async function sendNotificationToTopic(
  topic: string,
  title: string,
  body: string,
  data?: Record<string, string>
) {
  try {
    const message: admin.messaging.Message = {
      notification: {
        title,
        body,
      },
      data,
      topic,
    };

    const response = await admin.messaging().send(message);
    console.log('Successfully sent message to topic:', response);
    return { success: true, messageId: response };
  } catch (error) {
    console.error('Error sending message to topic:', error);
    return { success: false, error };
  }
}

export default admin;
