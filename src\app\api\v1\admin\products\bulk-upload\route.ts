import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import slugify from "slugify";

// Bulk upload products
interface BulkUploadProductRequest {
  baseName: string;
  description: string;
  images: string[];
  categoryId: string;
  brandId: string;
  materialId: string;
  sizeId: string;
  basePrice: number;
  gstPercentage: number;
  packagingSize: number;
  packagingUnit: string;
  imageAspectRatio?: string;
  isActive?: boolean;
  isPublished?: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const body = (await request.json()) as BulkUploadProductRequest;
    const {
      baseName,
      description,
      images,
      categoryId,
      brandId,
      materialId,
      sizeId,
      basePrice,
      gstPercentage,
      packagingSize,
      packagingUnit,
      imageAspectRatio = "9:16",
      isActive = true,
      isPublished = false
    } = body;

    // Validate required fields
    if (!baseName || !description || !images || !categoryId || !brandId || !materialId || !sizeId || basePrice === undefined || !packagingSize || !packagingUnit) {
      return NextResponse.json(
        {
          message: "Missing required fields",
          success: false,
        },
        { status: 400 }
      );
    }

    // Make sure images is an array with at least one item
    if (!Array.isArray(images) || images.length === 0) {
      return NextResponse.json(
        {
          message: "At least one image is required",
          success: false,
        },
        { status: 400 }
      );
    }

    // Check if a price master entry exists for this combination
    const existingPriceMaster = await prisma.priceMaster.findFirst({
      where: {
        categoryId,
        brandId,
        materialId,
        sizeId,
      },
    });

    // If price master exists, update its price to match the new product price
    if (existingPriceMaster) {
      console.log("Updating price master with new price:", basePrice);
      await prisma.priceMaster.update({
        where: { id: existingPriceMaster.id },
        data: { price: basePrice },
      });
    } else {
      // If no price master entry exists, create one with the provided price
      try {
        await prisma.priceMaster.create({
          data: {
            price: basePrice,
            categoryId,
            brandId,
            materialId,
            sizeId,
            isActive: true,
          },
        });
        console.log("Created new price master entry for the combination");
      } catch (error) {
        console.error("Failed to create price master entry:", error);
        // Continue with product creation even if price master creation fails
      }
    }

    // Get category, brand, and material names for slug generation
    const category = await prisma.category.findUnique({
      where: { id: categoryId },
      select: { name: true },
    });

    const brand = await prisma.brand.findUnique({
      where: { id: brandId },
      select: { name: true },
    });

    const material = await prisma.material.findUnique({
      where: { id: materialId },
      select: { name: true },
    });

    if (!category || !brand || !material) {
      return NextResponse.json(
        {
          message: "Invalid category, brand, or material",
          success: false,
        },
        { status: 400 }
      );
    }

    // Create products (one per image)
    const createdProducts = [];

    for (let i = 0; i < images.length; i++) {
      const productName = `${baseName} ${i + 1}`;

      // Generate slug from category, brand, material, and product name
      const slugBase = `${category.name}-${brand.name}-${material.name}-${productName}`;
      let slug = slugify(slugBase, { lower: true, strict: true });

      // Check if slug exists and make it unique if needed
      const existingProduct = await prisma.product.findUnique({
        where: { slug },
      });

      if (existingProduct) {
        slug = `${slug}-${Date.now()}-${i}`;
      }

      // Clean image URL
      const imageUrl = typeof images[i] === "string"
        ? images[i].replace(/^"|"$/g, "").replace(/\\"/g, '"')
        : images[i];

      // Create the product
      const product = await prisma.product.create({
        data: {
          name: productName,
          slug,
          description: description,
          basePrice: basePrice,
          gstPercentage: gstPercentage || 18, // Default to 18% if not provided
          packagingSize: packagingSize || 10, // Default to 10 if not provided
          packagingUnit: packagingUnit || "piece", // Default to "piece" if not provided
          images: [imageUrl],
          imageAspectRatio: imageAspectRatio, // Use the provided aspect ratio
          isActive: isActive,
          isPublished: isPublished,
          categoryId,
          brandId,
          materialId,
          sizeId,
        },
      });

      createdProducts.push(product);
    }

    return NextResponse.json({
      message: `${createdProducts.length} products created successfully`,
      success: true,
      data: createdProducts,
    });
  } catch (error: any) {
    console.error("Bulk product creation error:", error);
    return NextResponse.json(
      {
        message: "Failed to create products",
        success: false,
        error: error.message,
      },
      { status: 500 }
    );
  }
}
