import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

// Get all brands
export async function GET() {
  try {
    const brands = await prisma.brand.findMany({
      orderBy: {
        name: "asc",
      },
      select: {
        id: true,
        name: true,
        description: true,
        imageUrl: true,
        imageAspectRatio: true,
        packagingSizeImage: true,
        price: true,
        materialId: true,
        sizeId: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return NextResponse.json({
      message: "Brands fetched successfully",
      success: true,
      data: brands,
    });
  } catch (error) {
    console.error("Error fetching brands:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch brands",
        success: false,
      },
      { status: 500 }
    );
  }
}

// Create a new brand
interface CreateBrandRequest {
  name: string;
  imageUrl?: string;
  imageAspectRatio?: string;
  packagingSizeImage?: string;
  price?: number;
  materialId?: string;
  sizeId?: string;
  isActive?: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as CreateBrandRequest;
    const { name, imageUrl, imageAspectRatio = "1:1", packagingSizeImage, price, materialId, sizeId, isActive = true } = body;

    // Check if brand with the same name already exists
    const existingBrand = await prisma.brand.findUnique({
      where: { name },
    });

    if (existingBrand) {
      return NextResponse.json(
        {
          message: "Brand with this name already exists",
          success: false,
        },
        { status: 400 }
      );
    }

    const brand = await prisma.brand.create({
      data: {
        name,
        imageUrl,
        imageAspectRatio,
        packagingSizeImage,
        price,
        materialId,
        sizeId,
        isActive,
      },
    });

    return NextResponse.json({
      message: "Brand created successfully",
      success: true,
      data: brand,
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating brand:", error);
    return NextResponse.json(
      {
        message: "Failed to create brand",
        success: false,
      },
      { status: 500 }
    );
  }
}
