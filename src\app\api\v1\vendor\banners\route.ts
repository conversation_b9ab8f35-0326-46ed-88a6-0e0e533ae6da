import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

// GET active banners for vendor dashboard
export async function GET() {
  try {
    const banners = await prisma.vendorBanner.findMany({
      where: {
        isActive: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({
      message: "Banners fetched successfully",
      success: true,
      data: banners,
    });
  } catch (error) {
    console.error("Error fetching banners:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch banners",
        success: false,
      },
      { status: 500 }
    );
  }
}
