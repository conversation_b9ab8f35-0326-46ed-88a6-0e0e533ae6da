import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

export async function PATCH(request: NextRequest) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
      id: string;
      email: string;
      role: string;
    };

    if (decoded.role !== "VENDOR") {
      return NextResponse.json(
        { message: "Unauthorized - Not a vendor", success: false },
        { status: 401 }
      );
    }

    const vendor = await prisma.vendor.findUnique({
      where: { id: decoded.id },
    });

    if (!vendor) {
      return NextResponse.json(
        { message: "Vendor not found", success: false },
        { status: 404 }
      );
    }

    // Mark all notifications as read for this vendor
    const result = await prisma.notification.updateMany({
      where: {
        OR: [
          { recipientId: vendor.id, recipientType: "vendor" },
          { recipientId: null, recipientType: "vendor" },
        ],
        isRead: false,
      },
      data: {
        isRead: true,
      },
    });

    return NextResponse.json({
      message: "All notifications marked as read",
      success: true,
      data: {
        updatedCount: result.count,
      },
    });
  } catch (error) {
    console.error("Error marking all notifications as read:", error);
    return NextResponse.json(
      { message: "Failed to mark all notifications as read", success: false },
      { status: 500 }
    );
  }
}
