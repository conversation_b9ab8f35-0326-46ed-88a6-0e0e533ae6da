"use client";

import { useState, useEffect } from "react";
import { toast, ToastContainer } from "react-toastify";
import { adminAPI as api } from "@/lib/axios";
import "react-toastify/dist/ReactToastify.css";
import Link from "next/link";
import { FiTag, FiLayers, FiGrid, FiEdit, FiTrash2 } from "react-icons/fi";
import { useRouter } from "next/navigation";

interface Size {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function SizesPage() {
  const router = useRouter();
  const [sizes, setSizes] = useState<Size[]>([]);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchSizes();
  }, []);

  const fetchSizes = async () => {
    try {
      setLoading(true);
      const { data } = await api.get("/sizes");
      if (data.success) {
        setSizes(data.data);
      }
    } catch (error) {
      console.error("Error fetching sizes:", error);
      toast.error("Failed to fetch sizes");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name) {
      toast.error("Size name is required");
      return;
    }

    try {
      setIsSubmitting(true);
      const { data } = await api.post("/sizes", {
        name: formData.name,
      });

      if (data.success) {
        toast.success("Size created successfully");
        setFormData({
          name: "",
        });
        fetchSizes();
      }
    } catch (error) {
      console.error("Error creating size:", error);
      toast.error("Failed to create size");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this size?")) {
      return;
    }

    try {
      const { data } = await api.delete(`/sizes/${id}`);
      if (data.success) {
        toast.success("Size deleted successfully");
        fetchSizes();
      }
    } catch (error: any) {
      console.error("Error deleting size:", error);
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Failed to delete size");
      }
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <ToastContainer />
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold flex items-center"><FiGrid className="mr-2" /> Sizes</h1>
        <div className="flex space-x-4">
          <Link href="/admin/brands" className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
            <FiTag className="mr-2" /> Sub Categories
          </Link>
          <Link href="/admin/materials" className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
            <FiLayers className="mr-2" /> Materials
          </Link>

        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow mb-8">
        <h2 className="text-xl font-semibold mb-4">Add New Size</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Size Name</label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              required
            />
          </div>



          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-green-300"
            >
              {isSubmitting ? "Adding..." : "Add Size"}
            </button>
          </div>
        </form>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">Sizes List</h2>
        {loading ? (
          <p className="text-center py-4">Loading sizes...</p>
        ) : sizes.length === 0 ? (
          <p className="text-center py-4">No sizes found</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  {/* <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th> */}
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sizes.map((size) => (
                  <tr key={size.id} >
                    <td className="px-6 py-4 whitespace-nowrap">
                      {size.name}
                    </td>
                    {/* <td className="px-6 py-4">
                      {size.description || "-"}
                    </td> */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex space-x-3">
                        <button
                          onClick={() => router.push(`/admin/sizes/${size.id}/edit`)}
                          className="text-blue-600 hover:text-blue-900 flex items-center"
                        >
                          <FiEdit className="mr-1" /> Edit
                        </button>
                        <button
                          onClick={() => handleDelete(size.id)}
                          className="text-red-600 hover:text-red-900 flex items-center"
                        >
                          <FiTrash2 className="mr-1" /> Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
