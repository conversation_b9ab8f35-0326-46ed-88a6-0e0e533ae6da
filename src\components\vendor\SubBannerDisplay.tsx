"use client";
import { useEffect, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { vendorAPI as api } from "@/lib/axios";

interface SubBanner {
  id: string;
  imageUrl: string;
  name: string;
  isActive: boolean;
}

const SubBannerDisplay = () => {
  const [subBanners, setSubBanners] = useState<SubBanner[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchSubBanners = async () => {
      try {
        setLoading(true);
        const response = await api.get("/sub-banners");
        if (response.data.success) {
          console.log("Sub-banners fetched:", response.data.data);
          setSubBanners(response.data.data);
        } else {
          setError(true);
        }
      } catch (error) {
        console.error("Error fetching sub-banners:", error);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    fetchSubBanners();
  }, []);

  if (loading) {
    return null; // Don't show anything while loading
  }

  if (error || subBanners.length === 0) {
    return null; // Don't show anything if there's an error or no sub-banners
  }

  return (
    <div className="w-full mb-8 sm:mb-12 border">
      <div className="grid grid-cols-1  gap-4 w-full">
        {subBanners.map((subBanner) => (
          <div
            key={subBanner.id}
            className="relative rounded-lg overflow-hidden cursor-pointer w-full"
            onClick={() => router.push('/vendor/products')}
          >
            {/* Gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-gray-900/70 to-gray-900/30 z-10"></div>

            <div className="relative h-[200px] sm:h-[400px] w-full">
              <Image
                src={subBanner.imageUrl}
                alt={subBanner.name || "Sub-banner image"}
                fill
                 sizes="(max-width: 640px) 100vw, (max-width: 768px) 100vw, (max-width: 1024px) 100vw, 100vw"
                className="object-cover"
                priority
              />
            </div>

            {/* Content overlay */}
            {/* <div className="absolute inset-0 flex flex-col justify-end z-20 p-4 text-white">
              <h3 className="text-white font-medium text-lg">{subBanner.name}</h3>
              <button
                className="bg-white text-gray-900 px-3 py-1 rounded-sm font-medium hover:bg-gray-100 transition-colors mt-2 text-xs w-fit"
              >
                Shop Now
              </button>
            </div> */}
          </div>
        ))}
      </div>
    </div>
  );
};

export default SubBannerDisplay;
