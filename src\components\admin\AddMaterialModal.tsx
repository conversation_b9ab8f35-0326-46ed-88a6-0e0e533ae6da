"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { toast } from "react-toastify";
import { adminAPI as api } from "@/lib/axios";
import Modal from "./Modal";

// Define the schema for material creation
const materialSchema = yup.object({
  name: yup.string().required("Material name is required"),
  isActive: yup.boolean(),
});

type MaterialFormData = yup.InferType<typeof materialSchema>;

interface AddMaterialModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (material: any) => void;
}

const AddMaterialModal: React.FC<AddMaterialModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<MaterialFormData>({
    resolver: yupResolver(materialSchema),
    defaultValues: {
      isActive: true,
    },
  });

  const onSubmit = async (data: MaterialFormData) => {
    try {
      setIsSubmitting(true);
      
      const response = await api.post("/materials", data);

      if (response.data.success) {
        toast.success("Material created successfully");
        onSuccess(response.data.data);
        reset();
        onClose();
      } else {
        toast.error(response.data.message || "Failed to create material");
      }
    } catch (error: any) {
      console.error("Error creating material:", error);
      toast.error(error.response?.data?.message || "Failed to create material");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Add New Material" size="md">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Material Name <span className="text-red-500">*</span>
          </label>
          <input
            {...register("name")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="Enter material name"
          />
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
          )}
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="isActive"
            {...register("isActive")}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
            Active
          </label>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={() => {
              reset();
              onClose();
            }}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Creating..." : "Create Material"}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default AddMaterialModal;
