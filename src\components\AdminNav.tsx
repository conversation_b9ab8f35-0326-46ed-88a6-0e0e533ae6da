// src/components/AdminNav.tsx
"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import axios from "axios";
import { usePathname, useRouter } from "next/navigation";
import { FiPackage, FiTag, FiLayers, FiGrid, FiUpload, FiImage, FiCreditCard } from "react-icons/fi";

const BASEURL = process.env.NEXT_PUBLIC_BASEURL;

// Define types
interface DropdownStyles {
  transition: string;
  opacity: number;
  visibility: 'hidden' | 'visible';
}

const dropdownStyles: DropdownStyles = {
  transition: 'opacity 150ms ease-in-out',
  opacity: 0,
  visibility: 'hidden',
};

const dropdownVisibleStyles: Partial<DropdownStyles> = {
  opacity: 1,
  visibility: 'visible',
};

export default function AdminNav() {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  // Add state for each dropdown
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  let timeoutId: NodeJS.Timeout | undefined;

  const handleMouseEnter = (dropdownId: string) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    setActiveDropdown(dropdownId);
  };

  const handleMouseLeave = () => {
    timeoutId = setTimeout(() => {
      setActiveDropdown(null);
    }, 100);
  };

  const handleLogout = async () => {
    try {
      await axios.post(`${BASEURL}/api/v1/auth/admin/logout`);
      router.push("/admin/login");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  // Hide navigation items on login page
  const isLoginPage = pathname === "/admin/login";
  const isAdminHomePage = pathname === "/admin";

  if (isLoginPage || isAdminHomePage) {
    return <nav className="bg-white shadow-lg"></nav>;
  }

  return (
    <nav className="bg-white shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <Image
                src="https://buymetropolis.com/cdn/shop/files/Metropolis_logo_-_transparent.png?v=1726657630&width=280"
                alt="Metropolis"
                width={120}
                height={35}
                className="h-8 w-auto"
                priority
              />
            </div>

            <div className="hidden md:ml-6 md:flex md:space-x-4 lg:space-x-8">
              <Link
                href="/admin/dashboard"
                className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 border-b-2 text-sm font-medium"
              >
                Dashboard
              </Link>

              {/* Products Dropdown */}
              <div
                className="flex items-center h-full relative"
                onMouseEnter={() => handleMouseEnter('products')}
                onMouseLeave={handleMouseLeave}
              >
                <button className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 border-b-2 text-sm font-medium h-full">
                  Products
                </button>
                <div
                  className="absolute top-full left-0 z-10 w-48 bg-white rounded-md shadow-lg"
                  style={{
                    ...dropdownStyles,
                    ...(activeDropdown === 'products' ? dropdownVisibleStyles : {}),
                  }}
                >
                  <div className="py-1">
                    <Link
                      href="/admin/products"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      View Products
                    </Link>
                    <Link
                      href="/admin/products/add"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Add Product
                    </Link>
                    <Link
                      href="/admin/products/bulk-upload"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <span className="flex items-center">
                        <FiUpload className="mr-1" /> Bulk Upload
                      </span>
                    </Link>
                  </div>
                </div>
              </div>

              {/* Dealers Dropdown */}
              <div
                className="flex items-center h-full relative"
                onMouseEnter={() => handleMouseEnter('dealers')}
                onMouseLeave={handleMouseLeave}
              >
                <button className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 border-b-2 text-sm font-medium h-full">
                  Dealers
                </button>
                <div
                  className="absolute top-full left-0 z-10 w-48 bg-white rounded-md shadow-lg"
                  style={{
                    ...dropdownStyles,
                    ...(activeDropdown === 'dealers' ? dropdownVisibleStyles : {}),
                  }}
                >
                  <div className="py-1">
                    <Link
                      href="/admin/vendors"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      View Dealers
                    </Link>
                    <Link
                      href="/admin/vendors/add"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Add Dealer
                    </Link>
                  </div>
                </div>
              </div>

              {/* Categories Dropdown */}
              <div
                className="flex items-center h-full relative"
                onMouseEnter={() => handleMouseEnter('categories')}
                onMouseLeave={handleMouseLeave}
              >
                <button className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 border-b-2 text-sm font-medium h-full">
                  Categories
                </button>
                <div
                  className="absolute top-full left-0 z-10 w-48 bg-white rounded-md shadow-lg"
                  style={{
                    ...dropdownStyles,
                    ...(activeDropdown === 'categories' ? dropdownVisibleStyles : {}),
                  }}
                >
                  <div className="py-1">
                    <Link
                      href="/admin/categories"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      View Categories
                    </Link>
                    <Link
                      href="/admin/categories/add"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Add Category
                    </Link>
                  </div>
                </div>
              </div>

              {/* Product Attributes Dropdown */}
              <div
                className="flex items-center h-full relative"
                onMouseEnter={() => handleMouseEnter('attributes')}
                onMouseLeave={handleMouseLeave}
              >
                <button className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 border-b-2 text-sm font-medium h-full">
                  Attributes
                </button>
                <div
                  className="absolute top-full left-0 z-10 w-48 bg-white rounded-md shadow-lg"
                  style={{
                    ...dropdownStyles,
                    ...(activeDropdown === 'attributes' ? dropdownVisibleStyles : {}),
                  }}
                >
                  <div className="py-1">
                    <Link
                      href="/admin/brands"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Sub Categories
                    </Link>
                    <Link
                      href="/admin/materials"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Materials
                    </Link>
                    <Link
                      href="/admin/sizes"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Sizes
                    </Link>
                     {/* Banners Link */}
              <Link
                href="/admin/banners"
                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                Banners
              </Link>

              {/* Sub-Banners Link */}
              <Link
                href="/admin/sub-banners"
                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                Sub-Banners
              </Link>

              {/* Payment QR Code Link */}
              <Link
                href="/admin/payment-qrcode"
                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                Payment QR
              </Link>
                  </div>
                </div>
              </div>





              {/* Orders Dropdown */}
              <div
                className="flex items-center h-full relative"
                onMouseEnter={() => handleMouseEnter('orders')}
                onMouseLeave={handleMouseLeave}
              >
                <button className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 border-b-2 text-sm font-medium h-full">
                  Orders
                </button>
                <div
                  className="absolute top-full left-0 z-10 w-48 bg-white rounded-md shadow-lg"
                  style={{
                    ...dropdownStyles,
                    ...(activeDropdown === 'orders' ? dropdownVisibleStyles : {}),
                  }}
                >
                  <div className="py-1">
                    <Link
                      href="/admin/orders"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      View Orders
                    </Link>
                  </div>
                </div>
              </div>

              <button
                onClick={handleLogout}
                className="text-red-600 hover:text-red-800 inline-flex items-center px-1  text-sm font-medium"
              >
                Logout
              </button>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="flex items-center md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors"
              aria-expanded={isOpen}
            >
              <span className="sr-only">{isOpen ? 'Close main menu' : 'Open main menu'}</span>
              {!isOpen ? (
                <svg
                  className="block h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              ) : (
                <svg
                  className="block h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className={`md:hidden transition-all duration-300 ease-in-out ${isOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}>
        <div className="px-2 pt-2 pb-3 space-y-1 border-t border-gray-200">
          {/* Dashboard */}
          <Link
            href="/admin/dashboard"
            className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50"
          >
            <span className="flex items-center"><FiGrid className="mr-2 flex-shrink-0" /> Dashboard</span>
          </Link>

          {/* Products Section */}
          <div className="py-2">
            <div className="px-3 py-2 text-base font-medium text-gray-700">
              <span className="flex items-center"><FiPackage className="mr-2 flex-shrink-0" /> Products</span>
            </div>
            <div className="pl-6 space-y-1">
              <Link
                href="/admin/products"
                className="block px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              >
                View Products
              </Link>
              <Link
                href="/admin/products/add"
                className="block px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              >
                Add Product
              </Link>
              <Link
                href="/admin/products/bulk-upload"
                className="block px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              >
                <span className="flex items-center"><FiUpload className="mr-2 flex-shrink-0" /> Bulk Upload</span>
              </Link>
            </div>
          </div>

          {/* Dealers Section */}
          <div className="py-2">
            <div className="px-3 py-2 text-base font-medium text-gray-700">
              <span className="flex items-center">Dealers</span>
            </div>
            <div className="pl-6 space-y-1">
              <Link
                href="/admin/vendors"
                className="block px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              >
                View Dealers
              </Link>
              <Link
                href="/admin/vendors/add"
                className="block px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              >
                Add Dealer
              </Link>
            </div>
          </div>

          {/* Categories Section */}
          <div className="py-2">
            <div className="px-3 py-2 text-base font-medium text-gray-700">
              <span className="flex items-center"><FiGrid className="mr-2 flex-shrink-0" /> Categories</span>
            </div>
            <div className="pl-6 space-y-1">
              <Link
                href="/admin/categories"
                className="block px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              >
                View Categories
              </Link>
              <Link
                href="/admin/categories/add"
                className="block px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              >
                Add Category
              </Link>
            </div>
          </div>

          {/* Attributes Section */}
          <div className="py-2">
            <div className="px-3 py-2 text-base font-medium text-gray-700">
              <span className="flex items-center"><FiTag className="mr-2 flex-shrink-0" /> Attributes</span>
            </div>
            <div className="pl-6 space-y-1">
              <Link
                href="/admin/brands"
                className="block px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              >
                Sub Categories
              </Link>
              <Link
                href="/admin/materials"
                className="block px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              >
                <span className="flex items-center"><FiLayers className="mr-2 flex-shrink-0" /> Materials</span>
              </Link>
              <Link
                href="/admin/sizes"
                className="block px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              >
                Sizes
              </Link>
            </div>
          </div>



          {/* Banners */}
          <Link
            href="/admin/banners"
            className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50"
          >
            <span className="flex items-center"><FiImage className="mr-2 flex-shrink-0" /> Banners</span>
          </Link>

          {/* Sub-Banners */}
          <Link
            href="/admin/sub-banners"
            className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50"
          >
            <span className="flex items-center"><FiImage className="mr-2 flex-shrink-0" /> Sub-Banners</span>
          </Link>

          {/* Payment QR Code */}
          <Link
            href="/admin/payment-qrcode"
            className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50"
          >
            <span className="flex items-center"><FiCreditCard className="mr-2 flex-shrink-0" /> Payment QR</span>
          </Link>

          {/* Orders */}
          <Link
            href="/admin/orders"
            className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50"
          >
            <span className="flex items-center">Orders</span>
          </Link>

          {/* Logout */}
          <div className="pt-4 pb-2 border-t border-gray-200">
            <button
              onClick={handleLogout}
              className="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-red-600 hover:text-red-800 hover:bg-red-50"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
}
