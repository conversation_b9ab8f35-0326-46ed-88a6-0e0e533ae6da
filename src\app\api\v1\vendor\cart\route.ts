import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";


export async function POST(request: NextRequest) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
      email: string;
      role: string;
    };

    const vendor = await prisma.vendor.findUnique({
      where: { email: decoded.email },
    });

    if (!vendor) {
      return NextResponse.json(
        { message: "Vendor not found", success: false },
        { status: 404 }
      );
    }

    const { productId, quantity } = await request.json();

    let cart = await prisma.cart.findFirst({
      where: { vendorId: vendor.id },
    });

    if (!cart) {
      cart = await prisma.cart.create({
        data: {
          vendorId: vendor.id,
        },
      });
    }

    const existingItem = await prisma.cartItem.findFirst({
      where: {
        cartId: cart.id,
        productId,
      },
    });

    if (existingItem) {
      await prisma.cartItem.update({
        where: { id: existingItem.id },
        data: {
          quantity: existingItem.quantity + quantity,
        },
      });
    } else {
      await prisma.cartItem.create({
        data: {
          cartId: cart.id,
          productId,
          vendorId: vendor.id,
          quantity,
        },
      });
    }

    return NextResponse.json({
      message: "Product added to cart successfully",
      success: true,
    });
  } catch (error) {
    console.error("Error adding to cart:", error);
    return NextResponse.json(
      { message: "Failed to add product to cart", success: false },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
      email: string;
      role: string;
      id: string;
    };

    const vendor = await prisma.vendor.findUnique({
      where: { email: decoded.email },
    });

    if (!vendor) {
      return NextResponse.json(
        { message: "Vendor not found", success: false },
        { status: 404 }
      );
    }

    const cart = await prisma.cart.findFirst({
      where: { vendorId: vendor.id },
      include: {
        items: {
          include: {
            product: {
              include: {
                material: true,
                size: true,
                brand: true,
              }
            },
          },
        },
      },
    });

    return NextResponse.json({
      message: "Cart fetched successfully",
      success: true,
      data: cart,
    });
  } catch (error) {
    console.error("Error fetching cart:", error);
    return NextResponse.json(
      { message: "Failed to fetch cart", success: false },
      { status: 500 }
    );
  }
}
