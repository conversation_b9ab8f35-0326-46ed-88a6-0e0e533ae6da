"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { toast, ToastContainer } from "react-toastify";
import { useRouter } from "next/navigation";
import "react-toastify/dist/ReactToastify.css";
import { adminAPI as api } from "@/lib/axios";
import ImageUploaderNoCrop from "@/components/admin/ImageUploaderNoCrop";
import Link from "next/link";
import { FiArrowLeft, FiUpload, FiPlus } from "react-icons/fi";
import AddCategoryModal from "@/components/admin/AddCategoryModal";
import AddBrandModal from "@/components/admin/AddBrandModal";
import AddMaterialModal from "@/components/admin/AddMaterialModal";
import AddSizeModal from "@/components/admin/AddSizeModal";

// Define the schema for bulk upload
const bulkUploadSchema = yup.object({
  baseName: yup.string().required("Base product name is required"),
  description: yup.string().required("Description is required"),
  categoryId: yup.string().required("Category is required"),
  brandId: yup.string().required("Brand is required"),
  materialId: yup.string().required("Material is required"),
  sizeId: yup.string().required("Size is required"),
  basePrice: yup
    .number()
    .positive("Price must be positive")
    .required("Price is required"),
  gstPercentage: yup
    .number()
    .required("GST percentage is required"),
  packagingSize: yup
    .number()
    .integer("Packaging size must be a whole number")
    .min(1, "Packaging size must be at least 1")
    .required("Packaging size is required"),
  packagingUnit: yup.string().required("Packaging unit is required"),
  isActive: yup.boolean(),
  isPublished: yup.boolean(),
});

type BulkUploadFormData = yup.InferType<typeof bulkUploadSchema>;

interface Category {
  id: string;
  name: string;
  imageAspectRatio?: string;
}

interface Brand {
  id: string;
  name: string;
  imageAspectRatio?: string;
}

interface Material {
  id: string;
  name: string;
}

interface Size {
  id: string;
  name: string;
}

export default function BulkUploadPage() {
  const router = useRouter();
  const [productImages, setProductImages] = useState<string[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [sizes, setSizes] = useState<Size[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [loadingBrands, setLoadingBrands] = useState(false);
  const [loadingMaterials, setLoadingMaterials] = useState(false);
  const [loadingSizes, setLoadingSizes] = useState(false);
  const [selectedBrandAspectRatio, setSelectedBrandAspectRatio] = useState<string>("9:16");

  // Modal states
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false);
  const [isBrandModalOpen, setIsBrandModalOpen] = useState(false);
  const [isMaterialModalOpen, setIsMaterialModalOpen] = useState(false);
  const [isSizeModalOpen, setSizeModalOpen] = useState(false);
  // Price state is used to store the fetched price from price master

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<BulkUploadFormData>({
    resolver: yupResolver(bulkUploadSchema),
    defaultValues: {
      basePrice: 0,
      gstPercentage: 18, // Default GST percentage is 18%
      packagingSize: 10, // Default packaging size is 10
      packagingUnit: "piece",
      isActive: true,
      isPublished: false,
    }
  });

  useEffect(() => {
    fetchCategories();
    fetchBrands();
    fetchMaterials();
    fetchSizes();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoadingCategories(true);
      const { data } = await api.get("/categories");
      if (data.success) {
        setCategories(data.data);
      } else {
        toast.error("Failed to fetch categories");
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      toast.error("Failed to fetch categories");
    } finally {
      setLoadingCategories(false);
    }
  };

  const fetchBrands = async () => {
    try {
      setLoadingBrands(true);
      const { data } = await api.get("/brands");
      if (data.success) {
        setBrands(data.data);
      } else {
        toast.error("Failed to fetch brands");
      }
    } catch (error) {
      console.error("Error fetching brands:", error);
      toast.error("Failed to fetch brands");
    } finally {
      setLoadingBrands(false);
    }
  };

  const fetchMaterials = async () => {
    try {
      setLoadingMaterials(true);
      const { data } = await api.get("/materials");
      if (data.success) {
        setMaterials(data.data);
      } else {
        toast.error("Failed to fetch materials");
      }
    } catch (error) {
      console.error("Error fetching materials:", error);
      toast.error("Failed to fetch materials");
    } finally {
      setLoadingMaterials(false);
    }
  };

  const fetchSizes = async () => {
    try {
      setLoadingSizes(true);
      const { data } = await api.get("/sizes");
      if (data.success) {
        setSizes(data.data);
      } else {
        toast.error("Failed to fetch sizes");
      }
    } catch (error) {
      console.error("Error fetching sizes:", error);
      toast.error("Failed to fetch sizes");
    } finally {
      setLoadingSizes(false);
    }
  };

  // Price Master functionality has been removed



  // Handle brand selection change to update aspect ratio
  const handleBrandChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const brandId = e.target.value;
    const selectedBrand = brands.find(b => b.id === brandId);
    if (selectedBrand && selectedBrand.imageAspectRatio) {
      setSelectedBrandAspectRatio(selectedBrand.imageAspectRatio);
    }
  };

  // Handle images uploaded through our component
  const handleImagesUploaded = (imageUrls: string[]) => {
    const cleanUrls = imageUrls.map((url) =>
      typeof url === "string"
        ? url.replace(/^"|"$/g, "").replace(/\\"/g, '"')
        : url
    );
    console.log("Received image URLs:", imageUrls);
    console.log("Cleaned image URLs:", cleanUrls);
    setProductImages(cleanUrls);
  };

  const onSubmit = async (data: BulkUploadFormData) => {
    if (productImages.length === 0) {
      toast.error("Please upload at least one image");
      return;
    }

    try {
      setLoading(true);

      const { data: apiData } = await api.post("/products/bulk-upload", {
        ...data,
        images: productImages,
        isActive: data.isActive,
        isPublished: data.isPublished,
        imageAspectRatio: selectedBrandAspectRatio,
      });

      if (apiData.success) {
        toast.success(`${apiData.data.length} products created successfully`);
        router.push("/admin/products");
      }
    } catch (error: any) {
      console.error("Error creating products:", error);
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Error creating products");
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle successful creation of entities
  const handleCategoryCreated = (category: Category) => {
    setCategories((prev) => [...prev, category]);
  };

  const handleBrandCreated = (brand: Brand) => {
    setBrands((prev) => [...prev, brand]);
  };

  const handleMaterialCreated = (material: Material) => {
    setMaterials((prev) => [...prev, material]);
  };

  const handleSizeCreated = (size: Size) => {
    setSizes((prev) => [...prev, size]);
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <ToastContainer />
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Bulk Upload Products</h1>
        <Link
          href="/admin/products"
          className="flex items-center text-blue-500 hover:text-blue-700"
        >
          <FiArrowLeft className="mr-1" /> Back to Products
        </Link>
      </div>

      {/* Modal components */}
      <AddCategoryModal
        isOpen={isCategoryModalOpen}
        onClose={() => setIsCategoryModalOpen(false)}
        onSuccess={handleCategoryCreated}
      />
      <AddBrandModal
        isOpen={isBrandModalOpen}
        onClose={() => setIsBrandModalOpen(false)}
        onSuccess={handleBrandCreated}
      />
      <AddMaterialModal
        isOpen={isMaterialModalOpen}
        onClose={() => setIsMaterialModalOpen(false)}
        onSuccess={handleMaterialCreated}
      />
      <AddSizeModal
        isOpen={isSizeModalOpen}
        onClose={() => setSizeModalOpen(false)}
        onSuccess={handleSizeCreated}
      />

      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Base Product Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                {...register("baseName")}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="e.g. Jeans"
              />
              {errors.baseName && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.baseName.message}
                </p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                Products will be named incrementally (e.g. Jeans 1, Jeans 2)
              </p>
            </div>

            <div>
              <div className="flex justify-between items-center mb-1">
                <label className="block text-sm font-medium text-gray-700">
                  Category <span className="text-red-500">*</span>
                </label>
                <button
                  type="button"
                  onClick={() => setIsCategoryModalOpen(true)}
                  className="text-blue-500 hover:text-blue-700 text-sm flex items-center"
                >
                  <FiPlus className="mr-1" size={14} /> Add Category
                </button>
              </div>
              <select
                {...register("categoryId")}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                disabled={loadingCategories}
              >
                <option value="">Select Category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {errors.categoryId && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.categoryId.message}
                </p>
              )}
            </div>

            <div>
              <div className="flex justify-between items-center mb-1">
                <label className="block text-sm font-medium text-gray-700">
                  Sub Category <span className="text-red-500">*</span>
                </label>
                <button
                  type="button"
                  onClick={() => setIsBrandModalOpen(true)}
                  className="text-blue-500 hover:text-blue-700 text-sm flex items-center"
                >
                  <FiPlus className="mr-1" size={14} /> Add Sub Category
                </button>
              </div>
              <select
                {...register("brandId")}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                disabled={loadingBrands}
                onChange={handleBrandChange}
              >
                <option value="">Select Sub Category</option>
                {brands.map((brand) => (
                  <option key={brand.id} value={brand.id}>
                    {brand.name}
                  </option>
                ))}
              </select>
              {errors.brandId && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.brandId.message}
                </p>
              )}
            </div>

            <div>
              <div className="flex justify-between items-center mb-1">
                <label className="block text-sm font-medium text-gray-700">
                  Material <span className="text-red-500">*</span>
                </label>
                <button
                  type="button"
                  onClick={() => setIsMaterialModalOpen(true)}
                  className="text-blue-500 hover:text-blue-700 text-sm flex items-center"
                >
                  <FiPlus className="mr-1" size={14} /> Add Material
                </button>
              </div>
              <select
                {...register("materialId")}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                disabled={loadingMaterials}
              >
                <option value="">Select Material</option>
                {materials.map((material) => (
                  <option key={material.id} value={material.id}>
                    {material.name}
                  </option>
                ))}
              </select>
              {errors.materialId && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.materialId.message}
                </p>
              )}
            </div>

            <div>
              <div className="flex justify-between items-center mb-1">
                <label className="block text-sm font-medium text-gray-700">
                  Size <span className="text-red-500">*</span>
                </label>
                <button
                  type="button"
                  onClick={() => setSizeModalOpen(true)}
                  className="text-blue-500 hover:text-blue-700 text-sm flex items-center"
                >
                  <FiPlus className="mr-1" size={14} /> Add Size
                </button>
              </div>
              <select
                {...register("sizeId")}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                disabled={loadingSizes}
              >
                <option value="">Select Size</option>
                {sizes.map((size) => (
                  <option key={size.id} value={size.id}>
                    {size.name}
                  </option>
                ))}
              </select>
              {errors.sizeId && (
                <p className="mt-1 text-sm text-red-600">{errors.sizeId.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Price <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  {...register("basePrice")}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
              {errors.basePrice && (
                <p className="mt-1 text-sm text-red-600">{errors.basePrice.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                GST Percentage <span className="text-red-500">*</span>
              </label>
              <select
                {...register("gstPercentage")}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="5">5%</option>
                <option value="12">12%</option>
                <option value="18">18%</option>
              </select>
              {errors.gstPercentage && (
                <p className="mt-1 text-sm text-red-600">{errors.gstPercentage.message}</p>
              )}
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description <span className="text-red-500">*</span>
              </label>
              <textarea
                {...register("description")}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                rows={4}
                placeholder="Enter product description"
              ></textarea>
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Packaging Size - 1 Bale <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                min="1"
                step="1"
                {...register("packagingSize")}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
              {errors.packagingSize && (
                <p className="mt-1 text-sm text-red-600">{errors.packagingSize.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Packaging Unit <span className="text-red-500">*</span>
              </label>
              <select
                {...register("packagingUnit")}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="piece">Piece</option>
                <option value="unit">Unit</option>
              </select>
              {errors.packagingUnit && (
                <p className="mt-1 text-sm text-red-600">{errors.packagingUnit.message}</p>
              )}
            </div>



            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Product Images <span className="text-red-500">*</span>
              </label>
              <ImageUploaderNoCrop
                onImagesUploaded={handleImagesUploaded}
                multiple={true}
                maxFiles={50}
                imageAspectRatio={selectedBrandAspectRatio}
              />
              <p className="mt-1 text-xs text-gray-500">
                One product will be created for each uploaded image
              </p>
              <p className="mt-1 text-xs text-gray-500">
                {selectedBrandAspectRatio === "1:1"
                  ? "Images will be automatically resized to 1:1 ratio (1000x1000px) without cropping"
                  : "Images will be automatically resized to 9:16 ratio (562x1000px) without cropping"}
              </p>
              <p className="mt-1 text-xs text-gray-500">
                Aspect ratio is determined by the selected sub category
              </p>
            </div>

            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  {...register("isActive")}
                  className="rounded"
                />
                <span className="text-sm font-medium text-gray-700">Active</span>
              </label>
            </div>

            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  {...register("isPublished")}
                  className="rounded"
                />
                <span className="text-sm font-medium text-gray-700">
                  Published
                </span>
              </label>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300"
              disabled={loading}
            >
              {loading ? (
                "Processing..."
              ) : (
                <>
                  <FiUpload className="mr-2" /> Bulk Upload Products
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
