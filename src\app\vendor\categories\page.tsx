"use client";
import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { vendorAPI as api } from "@/lib/axios";
import { ChevronRight } from "lucide-react";
import ProductImage from "@/components/vendor/ProductImage";

interface Category {
  id: string;
  name: string;
  description: string;
}

interface Product {
  id: string;
  name: string;
  description: string;
  basePrice: number;
  packagingSize: number;
  packagingUnit: string;
  images?: string[];
  category: {
    name: string;
  };
  brand?: {
    name: string;
    imageAspectRatio?: string;
  };
  material?: {
    name: string;
  };
  size?: {
    name: string;
  };
}

export default function Categories() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingProducts, setLoadingProducts] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();

  const sort = searchParams.get("sort") || "newest";
  const minPrice = searchParams.get("minPrice");
  const maxPrice = searchParams.get("maxPrice");

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    if (selectedCategory) {
      fetchCategoryProducts(selectedCategory);
    } else {
      setProducts([]); // Clear products when no category is selected
    }
  }, [selectedCategory, sort, minPrice, maxPrice]);

  const fetchCategories = async () => {
    try {
      console.log("Fetching categories...");
      const { data } = await api.get("/categories");
      console.log("Categories response:", data);
      if (data.success) {
        setCategories(data.data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategoryProducts = async (categoryId: string) => {
    try {
      setLoadingProducts(true);
      console.log(`Fetching products for category ${categoryId}...`);
      const params = new URLSearchParams(searchParams.toString());
      const { data } = await api.get(
        `/categories/${categoryId}/products?${params.toString()}`
      );
      console.log("Products response:", data);
      if (data.success) {
        setProducts(data.data);
      }
    } catch (error) {
      console.error("Error fetching category products:", error);
    } finally {
      setLoadingProducts(false);
    }
  };

  const addToCart = async (productId: string, quantity: number) => {
    try {
      const { data } = await api.post("/cart", { productId, quantity });
      if (data.success) {
        alert("Product added to cart successfully");
      }
    } catch (error) {
      console.error("Error adding to cart:", error);
      alert("Failed to add product to cart");
    }
  };

  if (loading) {
    return <div className="p-4">Loading categories...</div>;
  }

  return (
    <div className="flex h-full">
      {/* Categories Sidebar */}
      <div className="w-64 border-r">
        <div className="p-4">
          <h2 className="font-semibold mb-4">Categories</h2>
          <div className="space-y-2">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`w-full text-left px-3 py-2 rounded-md flex items-center justify-between ${
                  selectedCategory === category.id
                    ? "bg-blue-50 text-blue-600"
                    : "hover:bg-gray-50"
                }`}
              >
                <span>{category.name}</span>
                <ChevronRight className="w-4 h-4" />
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Products Area */}
      <div className="flex-1 p-6">
        {selectedCategory ? (
          <>
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-xl font-semibold">
                {categories.find((c) => c.id === selectedCategory)?.name}
              </h1>
              <div className="flex gap-4">
                <select
                  value={sort}
                  onChange={(e) => {
                    const params = new URLSearchParams(searchParams.toString());
                    params.set("sort", e.target.value);
                    router.push(`?${params.toString()}`);
                  }}
                  className="border rounded-md px-3 py-2"
                >
                  <option value="newest">Newest</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                </select>
              </div>
            </div>

            <div className="mb-6">
              <div className="flex gap-4 items-center">
                <input
                  type="number"
                  placeholder="Min Price"
                  value={minPrice || ""}
                  onChange={(e) => {
                    const params = new URLSearchParams(searchParams.toString());
                    if (e.target.value) {
                      params.set("minPrice", e.target.value);
                    } else {
                      params.delete("minPrice");
                    }
                    router.push(`?${params.toString()}`);
                  }}
                  className="border rounded-md px-3 py-2 w-32"
                />
                <span>to</span>
                <input
                  type="number"
                  placeholder="Max Price"
                  value={maxPrice || ""}
                  onChange={(e) => {
                    const params = new URLSearchParams(searchParams.toString());
                    if (e.target.value) {
                      params.set("maxPrice", e.target.value);
                    } else {
                      params.delete("maxPrice");
                    }
                    router.push(`?${params.toString()}`);
                  }}
                  className="border rounded-md px-3 py-2 w-32"
                />
              </div>
            </div>

            {loadingProducts ? (
              <div>Loading products...</div>
            ) : products.length === 0 ? (
              <div className="text-center text-gray-500 py-12 bg-gray-50 rounded-lg">
                No products found with selected combination
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {products.map((product) => (
                  <div
                    key={product.id}
                    className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden h-[450px] flex flex-col"
                  >
                    <div className="relative w-full" style={{ height: '240px' }}>
                      {/* Category badge removed */}

                      <ProductImage
                        src={product.images?.[0] || null}
                        alt={product.name}
                        aspectRatio={product.brand?.imageAspectRatio || "9:16"}
                      />
                    </div>
                    <div className="p-4 flex-grow flex flex-col">
                      <h3 className="font-bold text-gray-800 truncate uppercase">{product.name}</h3>

                      {/* Description - fixed height section */}
                      <div className="min-h-[36px]">
                        {product.description && (
                          <p className="text-sm text-gray-600 mt-1 line-clamp-2 overflow-hidden">
                            {product.description}
                          </p>
                        )}
                      </div>

                      {/* Product specs - fixed height section */}
                      <div className="min-h-[48px] mt-1">
                        {product.size && (
                          <p className="text-sm text-gray-700">
                            <span className="font-medium">Size:</span> {product.size.name}
                          </p>
                        )}
                        {product.material && (
                          <p className="text-sm text-gray-700">
                            <span className="font-medium">Material:</span> {product.material.name}
                          </p>
                        )}
                      </div>

                      {/* Sub-category info - fixed height section */}
                      <div className="min-h-[20px]">
                        {product.brand && (
                          <p className="flex items-center">
                            <span className="font-medium mr-1">Sub Category:</span>
                            <span className="text-blue-600">{product.brand.name}</span>
                          </p>
                        )}
                      </div>

                      {/* Price and add to cart - fixed at bottom */}
                      <div className="mt-auto pt-1 flex justify-between items-end">
                        <div>
                          <span className="text-xl font-bold text-blue-600">
                            ₹{product.basePrice}
                          </span>
                          <span className="text-sm text-gray-500 ml-1">
                            per {product.packagingUnit || 'piece'}
                          </span>
                          <p className="text-xs text-gray-500 mt-1">
                            Pack: {product.packagingSize} {product.packagingUnit || 'piece'}{product.packagingSize > 1 ? 's' : ''}
                          </p>
                        </div>

                        <button
                          onClick={() => addToCart(product.id, 1)}
                          className="flex items-center justify-center bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                          </svg>
                          Add
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        ) : (
          <div className="text-center text-gray-500">
            Select a category to view products
          </div>
        )}
      </div>
    </div>
  );
}
