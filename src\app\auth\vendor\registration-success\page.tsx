"use client";
import Link from "next/link";
import { useEffect, useState, Suspense } from "react";
import { useSearchParams } from "next/navigation";

function RegistrationSuccessContent() {
  const searchParams = useSearchParams();
  const [helplineNumber, setHelplineNumber] = useState("1800-123-4567"); // Default number

  useEffect(() => {
    // You can fetch the helpline number from an API if needed
    // For now, we'll use a static number or get it from URL params
    const number = searchParams.get("helpline");
    if (number) {
      setHelplineNumber(number);
    }
  }, [searchParams]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-lg border border-gray-100">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="rounded-full bg-green-100 p-3">
              <svg
                className="h-12 w-12 text-green-600"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
          </div>

          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            Thank You for Registering with Metropolis
          </h1>

          <p className="text-gray-600 mb-6">
            Please wait for approval to get activated. Approval usually takes 24–48 hours.
          </p>

          <div className="bg-blue-50 p-4 rounded-md mb-6">
            <p className="text-gray-700">
              For any support, please call our helpline number:
              <br />
              <span className="font-medium text-blue-700 text-lg">
                {helplineNumber}
              </span>
            </p>
          </div>

          <Link
            href="/auth/vendor/login"
            className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition duration-150 ease-in-out"
          >
            Back to Login
          </Link>
        </div>
      </div>
    </div>
  );
}

// Loading fallback
function LoadingState() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
        <p className="mt-4 text-gray-600">Loading...</p>
      </div>
    </div>
  );
}

export default function RegistrationSuccessPage() {
  return (
    <Suspense fallback={<LoadingState />}>
      <RegistrationSuccessContent />
    </Suspense>
  );
}
