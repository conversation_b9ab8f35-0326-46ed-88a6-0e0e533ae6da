"use client";
import { useState, useEffect } from "react";
import { adminAPI as api } from "@/lib/axios";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Image from "next/image";
import { Trash2, Plus, Edit } from "lucide-react";
import AddQRCodeModal from "@/components/admin/AddQRCodeModal";
import EditQRCodeModal from "@/components/admin/EditQRCodeModal";

interface PaymentQRCode {
  id: string;
  imageUrl: string;
  name: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function PaymentQRCodePage() {
  const [qrCodes, setQRCodes] = useState<PaymentQRCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedQRCode, setSelectedQRCode] = useState<PaymentQRCode | null>(null);

  useEffect(() => {
    fetchQRCodes();
  }, []);

  const fetchQRCodes = async () => {
    try {
      setLoading(true);
      const response = await api.get("/payment-qrcode");
      if (response.data.success) {
        setQRCodes(response.data.data);
      } else {
        toast.error("Failed to fetch payment QR codes");
      }
    } catch (error) {
      console.error("Error fetching payment QR codes:", error);
      toast.error("Failed to fetch payment QR codes");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this payment QR code?")) return;

    try {
      const response = await api.delete(`/payment-qrcode/${id}`);
      if (response.data.success) {
        toast.success("Payment QR code deleted successfully");
        fetchQRCodes();
      } else {
        toast.error("Failed to delete payment QR code");
      }
    } catch (error) {
      console.error("Error deleting payment QR code:", error);
      toast.error("Failed to delete payment QR code");
    }
  };

  const handleAddSuccess = () => {
    setIsAddModalOpen(false);
    fetchQRCodes();
    toast.success("Payment QR code added successfully");
  };

  const handleEditSuccess = () => {
    setIsEditModalOpen(false);
    setSelectedQRCode(null);
    fetchQRCodes();
    toast.success("Payment QR code updated successfully");
  };

  const openEditModal = (qrCode: PaymentQRCode) => {
    setSelectedQRCode(qrCode);
    setIsEditModalOpen(true);
  };

  return (
    <div className="p-6">
      <ToastContainer position="top-right" autoClose={3000} />
      
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Payment QR Codes</h1>
        <button
          onClick={() => setIsAddModalOpen(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center gap-2 hover:bg-blue-700 transition-colors"
        >
          <Plus size={18} />
          Add QR Code
        </button>
      </div>

      {loading ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      ) : qrCodes.length === 0 ? (
        <div className="bg-gray-50 rounded-lg p-12 text-center">
          <p className="text-gray-500 text-lg mb-4">No payment QR codes found</p>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center gap-2 mx-auto hover:bg-blue-700 transition-colors"
          >
            <Plus size={18} />
            Add Your First QR Code
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {qrCodes.map((qrCode) => (
            <div key={qrCode.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="relative h-64 flex items-center justify-center p-4 bg-gray-50">
                <Image
                  src={qrCode.imageUrl}
                  alt={qrCode.name}
                  width={200}
                  height={200}
                  className="object-contain"
                />
              </div>
              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-semibold text-lg">{qrCode.name}</h3>
                  <span className={`px-2 py-1 text-xs rounded-full ${qrCode.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {qrCode.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <div className="flex justify-end gap-2 mt-4">
                  <button
                    onClick={() => openEditModal(qrCode)}
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                  >
                    <Edit size={18} />
                  </button>
                  <button
                    onClick={() => handleDelete(qrCode.id)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-full transition-colors"
                  >
                    <Trash2 size={18} />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add QR Code Modal */}
      <AddQRCodeModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSuccess={handleAddSuccess}
      />

      {/* Edit QR Code Modal */}
      {selectedQRCode && (
        <EditQRCodeModal
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedQRCode(null);
          }}
          onSuccess={handleEditSuccess}
          qrCode={selectedQRCode}
        />
      )}
    </div>
  );
}
