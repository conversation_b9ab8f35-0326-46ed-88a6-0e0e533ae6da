// update products based on id
// delete product
import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log("Fetching product with ID:", params.id);

    const product = await prisma.product.findUnique({
      where: {
        id: params.id,
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
          },
        },
        brand: {
          select: {
            id: true,
            name: true,
          },
        },
        material: {
          select: {
            id: true,
            name: true,
          },
        },
        size: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!product) {
      console.log("Product not found with ID:", params.id);
      return NextResponse.json(
        {
          success: false,
          message: "Product not found",
        },
        { status: 404 }
      );
    }

    console.log("Product found:", {
      id: product.id,
      name: product.name,
      categoryId: product.categoryId,
      category: product.category?.name,
      brandId: product.brandId,
      brand: product.brand?.name,
      materialId: product.materialId,
      material: product.material?.name,
      sizeId: product.sizeId,
      size: product.size?.name,
    });

    return NextResponse.json({
      success: true,
      data: product,
    });
  } catch (error) {
    console.error("Error fetching product:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch product",
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the auth token from cookies
    const authToken = request.cookies.get("authToken");

    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized - No token", success: false },
        { status: 401 }
      );
    }

    try {
      // Verify the token
      const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
        id: string;
        email: string;
        role: string;
      };

      // Check if the user is an admin
      if (decoded.role !== "ADMIN") {
        return NextResponse.json(
          { message: "Unauthorized - Not an admin", success: false },
          { status: 401 }
        );
      }
    } catch (jwtError) {
      console.error("JWT verification error:", jwtError);
      return NextResponse.json(
        { message: "Unauthorized - Invalid token", success: false },
        { status: 401 }
      );
    }

    const {
      name,
      description,
      basePrice,
      gstPercentage,
      isActive,
      isPublished,
      categoryId,
      brandId,
      materialId,
      sizeId,
      packagingUnit,
      packagingSize,
      images,
      imageAspectRatio,
    } = await request.json();

    // Get the existing product to check if key fields have changed
    const existingProduct = await prisma.product.findUnique({
      where: { id: params.id },
    });

    if (!existingProduct) {
      return NextResponse.json(
        { message: "Product not found", success: false },
        { status: 404 }
      );
    }

    // Check if any fields that affect the slug have changed
    const slugFieldsChanged =
      name !== existingProduct.name ||
      categoryId !== existingProduct.categoryId ||
      brandId !== existingProduct.brandId ||
      materialId !== existingProduct.materialId;

    // Generate a new slug if necessary
    let slug = existingProduct.slug;

    if (slugFieldsChanged) {
      try {
        // Fetch category, brand, and material names for slug generation
        const category = await prisma.category.findUnique({ where: { id: categoryId } });
        const brand = await prisma.brand.findUnique({ where: { id: brandId } });
        const material = await prisma.material.findUnique({ where: { id: materialId } });

        // Generate slug from category, brand, material, and product name
        slug = `${category?.name || ''}-${brand?.name || ''}-${material?.name || ''}-${name}`
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '');
      } catch (error) {
        console.error("Error generating slug with related entities:", error);
        // Fallback to simple name-based slug
        slug = name
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, "-")
          .replace(/(^-|-$)/g, "");
      }
    }

    // Price Master functionality has been removed

    const updatedProduct = await prisma.product.update({
      where: { id: params.id },
      data: {
        name,
        description,
        basePrice,
        gstPercentage,
        isActive,
        isPublished, // Add isPublished field to update
        categoryId,
        brandId,
        materialId,
        sizeId,
        packagingUnit,
        packagingSize,
        slug, // Update the slug if it changed
        images, // Update the images array
        imageAspectRatio: imageAspectRatio || undefined, // Update the image aspect ratio if provided
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
          },
        },
        brand: {
          select: {
            id: true,
            name: true,
          },
        },
        material: {
          select: {
            id: true,
            name: true,
          },
        },
        size: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: "Product updated successfully",
      success: true,
      data: updatedProduct,
    });
  } catch (error) {
    console.error("Error updating product:", error);
    return NextResponse.json(
      { message: "Failed to update product", success: false },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the auth token from cookies
    const authToken = request.cookies.get("authToken");

    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized - No token", success: false },
        { status: 401 }
      );
    }

    try {
      // Verify the token
      const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
        id: string;
        email: string;
        role: string;
      };

      // Check if the user is an admin
      if (decoded.role !== "ADMIN") {
        return NextResponse.json(
          { message: "Unauthorized - Not an admin", success: false },
          { status: 401 }
        );
      }
    } catch (jwtError) {
      console.error("JWT verification error:", jwtError);
      return NextResponse.json(
        { message: "Unauthorized - Invalid token", success: false },
        { status: 401 }
      );
    }

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: params.id },
      include: {
        orderItems: true,
        cartItems: true
      }
    });

    if (!product) {
      return NextResponse.json(
        { message: "Product not found", success: false },
        { status: 404 }
      );
    }

    // Check if product has related items
    if (product.orderItems.length > 0 || product.cartItems.length > 0) {
      // Product has related items, so we'll do a soft delete instead
      await prisma.product.update({
        where: { id: params.id },
        data: { isActive: false }
      });

      return NextResponse.json({
        message: "Product has been deactivated because it has related orders or cart items",
        success: true,
      });
    }

    // Hard delete from database if no related items
    await prisma.product.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      message: "Product deleted successfully",
      success: true,
    });
  } catch (error) {
    console.error("Error deleting product:", error);
    return NextResponse.json(
      { message: "Failed to delete product", success: false },
      { status: 500 }
    );
  }
}
