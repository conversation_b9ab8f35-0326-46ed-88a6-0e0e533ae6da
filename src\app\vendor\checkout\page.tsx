"use client";
import { useState, FormEvent } from "react";
import api from "@/lib/axios";

export default function CheckoutPage() {
  const [loading, setLoading] = useState(false);

  const handleCheckout = async (formData: any) => {
    try {
      setLoading(true);
      const { data } = await api.post("/checkout", formData);
      if (data.success) {
        // Handle successful checkout
      }
    } catch (error) {
      console.error("Error during checkout:", error);
    } finally {
      setLoading(false);
    }
  };

  // Example of using these variables in the component
  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const checkoutData = Object.fromEntries(formData.entries());
    handleCheckout(checkoutData);
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Checkout</h1>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Form fields would go here */}
        <div className="flex items-center">
          <button
            type="submit"
            className="bg-blue-500 text-white px-4 py-2 rounded"
            disabled={loading}
          >
            {loading ? "Processing..." : "Complete Checkout"}
          </button>
        </div>
      </form>
    </div>
  );
}
