import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const categoryId = params.id;
    console.log("Fetching products for categoryId:", categoryId);

    // First verify the category exists
    const category = await prisma.category.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      console.log("Category not found");
      return NextResponse.json(
        { message: "Category not found", success: false },
        { status: 404 }
      );
    }

    console.log("Found category:", category);

    const searchParams = request.nextUrl.searchParams;
    const sort = searchParams.get("sort") || "newest";
    const minPrice = searchParams.get("minPrice");
    const maxPrice = searchParams.get("maxPrice");

    let where: any = {
      categoryId,
      isActive: true,
      isPublished: true,
    };

    if (minPrice) {
      where.basePrice = {
        ...where.basePrice,
        gte: parseFloat(minPrice),
      };
    }

    if (maxPrice) {
      where.basePrice = {
        ...where.basePrice,
        lte: parseFloat(maxPrice),
      };
    }

    console.log("Query conditions:", where);

    let orderBy: any = {};
    if (sort === "newest") {
      orderBy.createdAt = "desc";
    } else if (sort === "price-low") {
      orderBy.basePrice = "asc";
    } else if (sort === "price-high") {
      orderBy.basePrice = "desc";
    }

    // First count total products
    const totalProducts = await prisma.product.count({
      where,
    });

    console.log("Total products found:", totalProducts);

    const products = await prisma.product.findMany({
      where,
      orderBy,
      select: {
        id: true,
        name: true,
        description: true,
        basePrice: true,
        packagingSize: true,
        packagingUnit: true,
        images: true,
        category: {
          select: {
            name: true,
          },
        },
        brand: {
          select: {
            name: true,
            imageAspectRatio: true,
          },
        },
        material: {
          select: {
            name: true,
          },
        },
        size: {
          select: {
            name: true,
          },
        },
      },
    });

    console.log("Products found:", products.length);
    console.log("Product details:", products);

    return NextResponse.json({
      message: "Products fetched successfully",
      success: true,
      data: products,
    });
  } catch (error) {
    console.error("Error fetching category products:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch products",
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
