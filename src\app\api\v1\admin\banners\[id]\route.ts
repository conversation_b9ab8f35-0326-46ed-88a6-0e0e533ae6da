import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

// Interface for updating a banner
interface UpdateBannerRequest {
  imageUrl?: string;
  name?: string;
  isActive?: boolean;
}

// GET a specific banner by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const banner = await prisma.vendorBanner.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!banner) {
      return NextResponse.json(
        { message: "Banner not found", success: false },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: "Banner fetched successfully",
      success: true,
      data: banner,
    });
  } catch (error) {
    console.error("Error fetching banner:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch banner",
        success: false,
      },
      { status: 500 }
    );
  }
}

// UPDATE a banner
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json() as UpdateBannerRequest;
    const { imageUrl, name, isActive } = body;

    // Check if banner exists
    const existingBanner = await prisma.vendorBanner.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!existingBanner) {
      return NextResponse.json(
        { message: "Banner not found", success: false },
        { status: 404 }
      );
    }

    // Update the banner
    const updatedBanner = await prisma.vendorBanner.update({
      where: {
        id: params.id,
      },
      data: {
        imageUrl,
        name,
        isActive,
      },
    });

    return NextResponse.json({
      message: "Banner updated successfully",
      success: true,
      data: updatedBanner,
    });
  } catch (error) {
    console.error("Error updating banner:", error);
    return NextResponse.json(
      {
        message: "Failed to update banner",
        success: false,
      },
      { status: 500 }
    );
  }
}

// DELETE a banner
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if banner exists
    const existingBanner = await prisma.vendorBanner.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!existingBanner) {
      return NextResponse.json(
        { message: "Banner not found", success: false },
        { status: 404 }
      );
    }

    // Delete the banner
    await prisma.vendorBanner.delete({
      where: {
        id: params.id,
      },
    });

    return NextResponse.json({
      message: "Banner deleted successfully",
      success: true,
    });
  } catch (error) {
    console.error("Error deleting banner:", error);
    return NextResponse.json(
      {
        message: "Failed to delete banner",
        success: false,
      },
      { status: 500 }
    );
  }
}
