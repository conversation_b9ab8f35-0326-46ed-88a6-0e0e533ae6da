import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    if (!params.id) {
      throw new Error("id is required");
    }

    const vendor_profile_by_Id = await prisma.vendor.findUnique({
      where: {
        id: params.id,
      },
    });

    if (vendor_profile_by_Id === null) {
      throw new Error("Vendor not found");
    }

    return NextResponse.json({
      success: true,
      message: `Vendor Profile for vendorId: ${params.id}`,
      data: vendor_profile_by_Id,
    });
  } catch (error: unknown) {
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "An unknown error occurred",
        success: false,
      },
      { status: 500 }
    );
  }
}
