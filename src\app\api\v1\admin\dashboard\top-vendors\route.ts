import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import moment from "moment";

// GET top vendors
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get("limit") || "4");
    const startDateParam = url.searchParams.get("startDate");
    const endDateParam = url.searchParams.get("endDate");

    console.log("Top vendors API called with date range:", { startDateParam, endDateParam });

    // Parse date parameters or use defaults with Moment.js
    let startDate: Date;
    let endDate: Date;

    if (startDateParam && moment(startDateParam, 'YYYY-MM-DD', true).isValid()) {
      // Parse YYYY-MM-DD format using Moment.js
      startDate = moment(startDateParam, 'YYYY-MM-DD').toDate();
    } else {
      // Default to first day of current month
      startDate = moment().startOf('month').toDate();
    }

    if (endDateParam && moment(endDateParam, 'YYYY-MM-DD', true).isValid()) {
      // Parse YYYY-MM-DD format using Moment.js
      // Set to end of day (23:59:59.999)
      endDate = moment(endDateParam, 'YYYY-MM-DD').endOf('day').toDate();
    } else {
      // Default to current date at end of day
      endDate = moment().endOf('day').toDate();
    }

    console.log("Date range for filtering:", {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    });

    // Calculate the previous period of the same length using Moment.js
    const periodLength = moment(endDate).diff(moment(startDate));
    const previousEndDate = moment(startDate).toDate();
    const previousStartDate = moment(startDate).subtract(periodLength, 'milliseconds').toDate();

    // Get all active vendors
    const vendors = await prisma.vendor.findMany({
      where: {
        status: "ACTIVE",
      },
      select: {
        id: true,
        businessName: true,
        orders: true,
      },
      orderBy: {
        orders: {
          _count: "desc",
        },
      },
      take: limit,
    });

    // Process vendor data with performance metrics
    const topVendorsPromises = vendors.map(async (vendor) => {
      // Calculate current period orders and revenue
      const currentPeriodOrders = vendor.orders.filter(
        (order) =>
          new Date(order.createdAt) >= startDate &&
          new Date(order.createdAt) < endDate
      );

      const currentPeriodRevenue = currentPeriodOrders.reduce(
        (sum, order) => sum + order.totalAmount,
        0
      );

      // Calculate previous period orders and revenue
      const previousPeriodOrders = vendor.orders.filter(
        (order) =>
          new Date(order.createdAt) >= previousStartDate &&
          new Date(order.createdAt) < previousEndDate
      );

      const previousPeriodRevenue = previousPeriodOrders.reduce(
        (sum, order) => sum + order.totalAmount,
        0
      );

      // Calculate period revenue (revenue for the selected period)
      const periodRevenue = currentPeriodOrders.reduce(
        (sum, order) => sum + order.totalAmount,
        0
      );

      // Format revenue in lakhs (for Indian format)
      const formattedRevenue = `₹${(periodRevenue / 100000).toFixed(1)}L`;

      // Calculate performance based on revenue growth
      let performanceValue = 0;
      if (previousPeriodRevenue > 0) {
        // Calculate period-over-period growth
        const revenueGrowth = ((currentPeriodRevenue - previousPeriodRevenue) / previousPeriodRevenue) * 100;
        performanceValue = Math.round(revenueGrowth);
      } else if (currentPeriodRevenue > 0) {
        // If no previous period revenue but current period has revenue, show as positive
        performanceValue = 100;
      }

      // Format performance with + or - sign
      const performance = `${performanceValue >= 0 ? '+' : ''}${performanceValue}%`;

      return {
        id: vendor.id,
        name: vendor.businessName,
        orders: currentPeriodOrders.length.toString(),
        revenue: formattedRevenue,
        performance: performance,
      };
    });

    const topVendors = await Promise.all(topVendorsPromises);

    // Sort by order count (descending)
    topVendors.sort((a, b) => parseInt(b.orders) - parseInt(a.orders));

    return NextResponse.json({
      message: "Top vendors fetched successfully",
      success: true,
      data: topVendors,
    });
  } catch (error) {
    console.error("Error fetching top vendors:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch top vendors",
        success: false,
      },
      { status: 500 }
    );
  }
}
