import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

// Get a specific material
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const material = await prisma.material.findUnique({
      where: { id: params.id },
    });

    if (!material) {
      return NextResponse.json(
        {
          message: "Material not found",
          success: false,
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: "Material fetched successfully",
      success: true,
      data: material,
    });
  } catch (error) {
    console.error("Error fetching material:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch material",
        success: false,
      },
      { status: 500 }
    );
  }
}

// Update a material
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { name, description, isActive } = await request.json();

    // Check if material exists
    const existingMaterial = await prisma.material.findUnique({
      where: { id: params.id },
    });

    if (!existingMaterial) {
      return NextResponse.json(
        {
          message: "Material not found",
          success: false,
        },
        { status: 404 }
      );
    }

    // Check if another material with the same name exists
    if (name && name !== existingMaterial.name) {
      const materialWithSameName = await prisma.material.findUnique({
        where: { name },
      });

      if (materialWithSameName) {
        return NextResponse.json(
          {
            message: "Material with this name already exists",
            success: false,
          },
          { status: 400 }
        );
      }
    }

    const updatedMaterial = await prisma.material.update({
      where: { id: params.id },
      data: {
        name: name !== undefined ? name : undefined,
        description: description !== undefined ? description : undefined,
        isActive: isActive !== undefined ? isActive : undefined,
      },
    });

    return NextResponse.json({
      message: "Material updated successfully",
      success: true,
      data: updatedMaterial,
    });
  } catch (error) {
    console.error("Error updating material:", error);
    return NextResponse.json(
      {
        message: "Failed to update material",
        success: false,
      },
      { status: 500 }
    );
  }
}

// Delete a material
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if material exists
    const existingMaterial = await prisma.material.findUnique({
      where: { id: params.id },
      include: {
        products: {
          select: { id: true },
          take: 1,
        },
        priceMasters: {
          select: { id: true },
          take: 1,
        },
      },
    });

    if (!existingMaterial) {
      return NextResponse.json(
        {
          message: "Material not found",
          success: false,
        },
        { status: 404 }
      );
    }

    // Check if material is being used by products or price masters
    if (existingMaterial.products.length > 0 || existingMaterial.priceMasters.length > 0) {
      return NextResponse.json(
        {
          message: "Cannot delete material as it is being used by products or price masters",
          success: false,
        },
        { status: 400 }
      );
    }

    await prisma.material.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      message: "Material deleted successfully",
      success: true,
    });
  } catch (error) {
    console.error("Error deleting material:", error);
    return NextResponse.json(
      {
        message: "Failed to delete material",
        success: false,
      },
      { status: 500 }
    );
  }
}
