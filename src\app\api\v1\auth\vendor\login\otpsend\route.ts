import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { sendOtpEmail } from "@/lib/zepto-mail";
import { sendOtpSMS } from "@/lib/sms-service";

export async function POST(request: NextRequest) {
  try {
    const { identifier } = await request.json();
    const isEmail = identifier.includes('@');

    const user = await prisma.vendor.findUnique({
      where: isEmail ? { email: identifier } : { phoneNumber: identifier },
    });


    if (!user) {
      return NextResponse.json(
        { message: "Invalid credentials", success: false },
        { status: 401 }
      );
    }

    if (user.status !== "ACTIVE") {
      return NextResponse.json(
        {
          message: "Account is not active",
          success: false,
          data: {
            status: user.status,
            email: user.email,
            redirectUrl: "/auth/vendor/pending-approval"
          }
        },
        { status: 403 }
      );
    }

    // Special case for test accounts
    let otpCode;
    if (identifier === "**********" || identifier === "<EMAIL>") {
      otpCode = "0000";
    } else {
      otpCode = Math.floor(1000 + Math.random() * 9000).toString();
    }
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000);

    await prisma.otp.deleteMany({
      where: {
        email: user.email,
        isUsed: false,
      },
    });

    await prisma.otp.create({
      data: {
        email: user.email,
        code: otpCode,
        expiresAt: expiresAt,
      },
    });

    if (isEmail) {
      try {
        await sendOtpEmail(user.email, otpCode);
      } catch (emailError) {
        console.error("Error sending OTP email:", emailError);
      }
    }
    else {
      try {
        await sendOtpSMS(user.phoneNumber, otpCode);
      } catch (smsError) {
        console.error("Error sending OTP SMS:", smsError);
      }
    }

    // Prepare response based on environment variable
    const response: any = {
      message: "OTP sent successfully",
      success: true
    };

    // Only include OTP in response if SHOW_OTP_IN_RESPONSE is true
    if (process.env.SHOW_OTP_IN_RESPONSE === 'true') {
      response.data = {
        otp: otpCode
      };
    }

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error("Error sending OTP:", error);


    return NextResponse.json(
      { message: "An error occurred while sending OTP", success: false },
      { status: 500 }
    );
  }
}
