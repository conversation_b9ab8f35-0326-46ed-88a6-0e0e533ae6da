"use client";

import React, { useState, useEffect } from "react";
import { adminAPI, vendorAPI } from "@/lib/axios";
import { toast } from "react-toastify";

interface Notification {
  id: string;
  title: string;
  type: string;
  desc: string;
  createdAt: string;
  isRead: boolean;
  order_id?: string;
  vendor_id?: string;
}

interface NotificationListProps {
  userType: "admin" | "vendor";
}

const NotificationList: React.FC<NotificationListProps> = ({ userType }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const api = userType === "admin" ? adminAPI : vendorAPI;

  const fetchNotifications = async (page = 1) => {
    try {
      setLoading(true);
      const { data } = await api.get(`/notifications?page=${page}&limit=10`);
      
      if (data.success) {
        setNotifications(data.data.notifications);
        setCurrentPage(data.data.pagination.currentPage);
        setTotalPages(data.data.pagination.totalPages);
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
      toast.error("Failed to fetch notifications");
    } finally {
      setLoading(false);
    }
  };

  const fetchUnreadCount = async () => {
    try {
      const { data } = await api.get("/notifications/unread-count");
      if (data.success) {
        setUnreadCount(data.data.unreadCount);
      }
    } catch (error) {
      console.error("Error fetching unread count:", error);
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      await api.patch("/notifications", { notificationId });
      
      // Update local state
      setNotifications(prev => 
        prev.map(notif => 
          notif.id === notificationId 
            ? { ...notif, isRead: true }
            : notif
        )
      );
      
      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error("Error marking notification as read:", error);
      toast.error("Failed to mark notification as read");
    }
  };

  const markAllAsRead = async () => {
    try {
      await api.patch("/notifications/mark-all-read");
      
      // Update local state
      setNotifications(prev => 
        prev.map(notif => ({ ...notif, isRead: true }))
      );
      
      setUnreadCount(0);
      toast.success("All notifications marked as read");
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      toast.error("Failed to mark all notifications as read");
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    // Mark as read if not already read
    if (!notification.isRead) {
      markAsRead(notification.id);
    }

    // Handle navigation based on notification type and data
    if (notification.order_id) {
      // Navigate to order details
      const orderUrl = userType === "admin" 
        ? `/admin/orders/${notification.order_id}`
        : `/vendor/orders/${notification.order_id}`;
      window.location.href = orderUrl;
    } else if (notification.vendor_id && userType === "admin") {
      // Navigate to vendor details (admin only)
      window.location.href = `/admin/vendors/${notification.vendor_id}`;
    }
  };

  useEffect(() => {
    fetchNotifications();
    fetchUnreadCount();
  }, []);

  if (loading) {
    return (
      <div className="p-4">
        <div className="animate-pulse space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">
          Notifications {unreadCount > 0 && (
            <span className="bg-red-500 text-white text-sm px-2 py-1 rounded-full ml-2">
              {unreadCount}
            </span>
          )}
        </h1>
        
        {unreadCount > 0 && (
          <button
            onClick={markAllAsRead}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Mark All as Read
          </button>
        )}
      </div>

      <div className="space-y-4">
        {notifications.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No notifications found
          </div>
        ) : (
          notifications.map((notification) => (
            <div
              key={notification.id}
              onClick={() => handleNotificationClick(notification)}
              className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                notification.isRead 
                  ? "bg-white border-gray-200" 
                  : "bg-blue-50 border-blue-200"
              } hover:bg-gray-50`}
            >
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className={`font-semibold ${!notification.isRead ? "text-blue-900" : "text-gray-900"}`}>
                    {notification.title}
                  </h3>
                  <p className="text-gray-600 mt-1">{notification.desc}</p>
                  <div className="flex items-center mt-2 text-sm text-gray-500">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs mr-2">
                      {notification.type.replace(/_/g, " ").toLowerCase()}
                    </span>
                    <span>{notification.createdAt}</span>
                  </div>
                </div>
                
                {!notification.isRead && (
                  <div className="w-3 h-3 bg-blue-500 rounded-full ml-4 mt-1"></div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6 space-x-2">
          <button
            onClick={() => fetchNotifications(currentPage - 1)}
            disabled={currentPage === 1}
            className="px-4 py-2 border rounded disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          <span className="px-4 py-2">
            Page {currentPage} of {totalPages}
          </span>
          
          <button
            onClick={() => fetchNotifications(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="px-4 py-2 border rounded disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
};

export default NotificationList;
