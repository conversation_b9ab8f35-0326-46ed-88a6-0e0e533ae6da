Metropolis is a B2B Ecommerce web application and mobile app.

## Run the following commands to get started

First, run the development server:

```bash
npm run dev
```

For creating a build(mandatory before deploying)

```bash
npm run build
```

#### Important branches

- dev
- main

Open [http://localhost:3000](http://localhost:3000) with your browser to see the portal.

## Stack

- NextJS v14.2.14
- Typescript
- Prisma ORM
- PostgreSQL
- REST APIs

## Deployed on Digitalocean

- Frontend and Backend is deployed on Digitalocean App platform
- Database is also hosted on Digitalocean cloud as managed database
- Database could be locally utilized using docker

## Docker for Database on local environment

```bash
docker-compose up -d
```

### Using Prisma for first time setup

Run the below command to generate schema

```bash
npx prisma generate
```

For any model changes to take effect in the database run the following

```bash
npx prisma db push
```

### Setting up the environment file

```bash
copy the example.env to .env
```

## API Documentation

API documentation is available in [API Documentation](https://github.com/YDLabs/metropolis-admin/blob/dev/docs/api-documentation.md).

This also includes the JSON for collection which could be used in Postman

### Folder Structure


Folder structure could be checked at [Folder Structure](https://github.com/YDLabs/metropolis-admin/blob/dev/docs/folder-structure.md)
