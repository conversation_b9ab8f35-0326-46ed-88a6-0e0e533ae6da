// src/components/admin/BannerImageUploader.tsx
import React, { useState, useCallback } from "react";
import { useDropzone, FileWithPath } from "react-dropzone";
import axios from "axios";
import { toast } from "react-toastify";
import { Info } from "lucide-react";

interface BannerImageUploaderProps {
  onImagesUploaded: (urls: string[]) => void;
  existingImages?: string[];
  multiple?: boolean;
  maxFiles?: number;
}

const BannerImageUploader: React.FC<BannerImageUploaderProps> = ({
  onImagesUploaded,
  existingImages = [],
  multiple = true,
  maxFiles = 20,
}) => {
  const [uploadedImages, setUploadedImages] = useState<string[]>(existingImages);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [processingCount, setProcessingCount] = useState<number>(0);
  const [totalToProcess, setTotalToProcess] = useState<number>(0);
  const [modalImage, setModalImage] = useState<string | null>(null);

  const onDrop = useCallback(
    async (acceptedFiles: FileWithPath[]) => {
      if (uploadedImages.length + acceptedFiles.length > maxFiles) {
        toast.error(`You can upload a maximum of ${maxFiles} images`);
        return;
      }

      if (acceptedFiles.length === 0) return;

      setIsUploading(true);
      setTotalToProcess(acceptedFiles.length);
      setProcessingCount(0);

      try {
        const uploadPromises = acceptedFiles.map(async (file, index) => {
          const formData = new FormData();
          formData.append("file", file);
          formData.append("resize", "true"); 
          formData.append("width", "1920");
          formData.append("height", "1080");
          formData.append("type", "banner");

          const response = await axios.post("/api/v1/admin/upload", formData, {
            headers: {
              "Content-Type": "multipart/form-data",
            },
            onUploadProgress: (progressEvent) => {
              if (progressEvent.total) {
                const percentCompleted = Math.round(
                  (progressEvent.loaded * 100) / progressEvent.total
                );
                console.log(`File ${index + 1} upload progress: ${percentCompleted}%`);
              }
            },
          });

          setProcessingCount(prev => prev + 1);

          if (response.data.success) {
            return response.data.path as string;
          } else {
            throw new Error(`Failed to upload image ${index + 1}`);
          }
        });

        // Process all uploads in parallel
        const newImageUrls = await Promise.all(uploadPromises);

        // Update state with new images
        const allImages = [...uploadedImages, ...newImageUrls];
        setUploadedImages(allImages);
        onImagesUploaded(allImages);

        toast.success(`Successfully uploaded ${newImageUrls.length} images`);
      } catch (error) {
        console.error("Upload error:", error);
        toast.error(
          `Failed to upload images: ${error instanceof Error ? error.message : "Unknown error"}`
        );
      } finally {
        setIsUploading(false);
      }
    },
    [uploadedImages, maxFiles, onImagesUploaded]
  );

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".webp"],
    },
    maxSize: 10485760, // 10MB
    disabled: isUploading,
  });

  // Remove uploaded image
  const removeImage = (index: number): void => {
    const newImages = [...uploadedImages];
    newImages.splice(index, 1);
    setUploadedImages(newImages);
    onImagesUploaded(newImages);
  };

  // Open image in modal
  const openImageModal = (image: string): void => {
    setModalImage(image);
  };

  // Close image modal
  const closeImageModal = (): void => {
    setModalImage(null);
  };

  return (
    <div className="banner-image-uploader">
      {/* Banner size info */}
      <div className="flex items-center gap-2 mb-2 text-sm text-blue-600 bg-blue-50 p-2 rounded-md">
        <Info size={16} />
        <span>Banner images will be resized to 1920x1080px for optimal display</span>
      </div>
      
      {/* Dropzone area */}
      <div
        {...getRootProps()}
        className={`mt-1 flex justify-center px-3 sm:px-6 pt-4 sm:pt-5 pb-4 sm:pb-6 border-2 border-gray-300 border-dashed rounded-md cursor-pointer ${
          isUploading ? "opacity-50 cursor-not-allowed" : ""
        }`}
      >
        <div className="space-y-1 text-center">
          <input {...getInputProps()} />
          <svg
            className="mx-auto h-8 sm:h-12 w-8 sm:w-12 text-gray-400"
            stroke="currentColor"
            fill="none"
            viewBox="0 0 48 48"
          >
            <path
              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <div className="flex flex-col sm:flex-row text-sm text-gray-600 justify-center items-center">
            <label className="relative cursor-pointer rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
              <span>Upload banner</span>
            </label>
            <p className="sm:pl-1">or drag and drop</p>
          </div>
          <p className="text-xs text-gray-500">
            PNG, JPG, WEBP up to 10MB
          </p>
          <p className="text-xs text-gray-500">
            Recommended aspect ratio: 16:9 (1920x1080px)
          </p>
        </div>
      </div>

      {/* Upload progress */}
      {isUploading && (
        <div className="mt-4">
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-blue-600 h-2.5 rounded-full"
              style={{ width: `${(processingCount / totalToProcess) * 100}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Processing {processingCount} of {totalToProcess} images...
          </p>
        </div>
      )}

      {/* Image previews */}
      {uploadedImages.length > 0 && (
        <div className="mt-4">
          <div className="grid grid-cols-1 gap-4">
            {uploadedImages.map((image, index) => (
              <div key={index} className="relative">
                <div className="aspect-[16/9] w-full bg-gray-100 rounded-md overflow-hidden">
                  <img
                    src={image}
                    alt={`Banner ${index + 1}`}
                    className="w-full h-full object-cover cursor-pointer"
                    onClick={() => openImageModal(image)}
                  />
                </div>
                <button
                  type="button"
                  className="absolute -top-2 -right-2 rounded-full bg-red-500 text-white p-1 w-6 h-6 flex items-center justify-center"
                  onClick={() => removeImage(index)}
                >
                  ×
                </button>
              </div>
            ))}
          </div>
          <p className="mt-2 text-xs text-gray-500 text-center">
            Click on the image to view in full size (1920x1080px)
          </p>
        </div>
      )}

      {/* Full-size image modal */}
      {modalImage && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 p-4" onClick={closeImageModal}>
          <div className="relative max-w-6xl w-full max-h-[90vh] flex items-center justify-center">
            <button
              type="button"
              className="absolute top-2 right-2 z-10 rounded-full bg-red-500 text-white p-1 w-8 h-8 flex items-center justify-center"
              onClick={closeImageModal}
            >
              ×
            </button>
            <img
              src={modalImage}
              alt="Full size banner preview"
              className="max-w-full max-h-[85vh] object-contain"
              onClick={(e) => e.stopPropagation()}
            />
            <div className="absolute bottom-4 left-0 right-0 text-center text-white text-sm bg-black bg-opacity-50 py-2">
              Banner size: 1920x1080px | Click anywhere outside to close
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BannerImageUploader;
