import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const subBanners = await prisma.subBanner.findMany({
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({
      message: "Sub-banners fetched successfully",
      success: true,
      data: subBanners,
    });
  } catch (error) {
    console.error("Error fetching sub-banners:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch sub-banners",
        success: false,
      },
      { status: 500 }
    );
  }
}