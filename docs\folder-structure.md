```
src/
├── app/
│   ├── api/
│   │   ├── vendor/
│   │   │   ├── categories/
│   │   │   │   ├── [categoryId]/
│   │   │   │   │   ├── products/
│   │   │   │   │   │   └── route.ts    # List products in category
│   │   │   │   │   └── route.ts        # Get category details
│   │   │   │   └── route.ts            # List all categories
│   │   │   └── products/
│   │   │       ├── [id]/
│   │   │       │   └── route.ts        # Get specific product
│   │   ├── admin/
│   │   │   ├── categories/
│   │   │   │   ├── [categoryId]/
│   │   │   │   │   ├── products/
│   │   │   │   │   │   ├── [id]/
│   │   │   │   │   │   │   └── route.ts  # Update/Delete product
│   │   │   │   │   │   └── route.ts      # Add product/List products
│   │   │   ├── dashboard/
│   │   │   │   ├── recent-orders/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── stats/
│   │   │   │   │   └── route.ts
│   │   │   │   └── top-vendors/
│   │   │   │       └── route.ts
│   │   │   ├── orders/
│   │   │   │   ├── [id]/
│   │   │   │   │   ├── approve/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── reject/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── status/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   └── route.ts
│   │   │   │   └── route.ts
│   │   │   ├── products/
│   │   │   │   ├── [id]/
│   │   │   │   │   └── route.ts
│   │   │   │   └── route.ts
│   │   │   ├── reports/
│   │   │   │   ├── sales/
│   │   │   │   │   └── route.ts
│   │   │   │   └── vendors/
│   │   │   │       └── route.ts
│   │   │   ├── settings/
│   │   │   │   └── route.ts
│   │   │   └── vendors/
│   │   │       ├── [id]/
│   │   │       │   ├── documents/
│   │   │       │   │   └── route.ts
│   │   │       │   ├── status/
│   │   │       │   │   └── route.ts
│   │   │       │   └── route.ts
│   │   │       └── route.ts
│   │   └── vendor/
│   │       ├── cart/
│   │       │   ├── [itemId]/
│   │       │   │   └── route.ts
│   │       │   └── route.ts
│   │       ├── dashboard/
│   │       │   └── stats/
│   │       │       └── route.ts
│   │       ├── notifications/
│   │       │   ├── [id]/
│   │       │   │   └── read/
│   │       │   │       └── route.ts
│   │       │   └── route.ts
│   │       ├── orders/
│   │       │   ├── [id]/
│   │       │   │   └── route.ts
│   │       │   └── route.ts
│   │       ├── products/
│   │       │   ├── [id]/
│   │       │   │   └── route.ts
│   │       │   └── route.ts
│   │       └── profile/
│   │           ├── documents/
│   │           │   └── route.ts
│   │           ├── password/
│   │           │   └── route.ts
│   │           └── route.ts
│   └── middleware.ts
├── lib/
│   ├── auth/
│   │   ├── jwt.ts
│   │   └── session.ts
│   ├── db/
│   │   ├── migrations/
│   │   ├── models/
│   │   │   ├── admin.model.ts
│   │   │   ├── category.model.ts
│   │   │   ├── order.model.ts
│   │   │   ├── product.model.ts
│   │   │   └── vendor.model.ts
│   │   └── prisma.ts
│   └── utils/
│       ├── api-response.ts
│       ├── constants.ts
│       ├── error-handler.ts
│       ├── logger.ts
│       └── validators.ts
├── middleware/
│   ├── admin-auth.middleware.ts
│   ├── error.middleware.ts
│   ├── logger.middleware.ts
│   └── vendor-auth.middleware.ts
└── types/
    ├── admin.types.ts
    ├── api.types.ts
    ├── order.types.ts
    ├── product.types.ts
    └── vendor.types.ts
```

Key directories and their purposes:

1. `app/api/`: Route handlers for all API endpoints

   - Organized by role (auth, admin, vendor)
   - Each endpoint has its own route.ts file
   - Nested routing handled via folders

2. `lib/`: Core utilities and services

   - `auth/`: Authentication related utilities
   - `db/`: Database configuration and models
   - `utils/`: Common utility functions

3. `middleware/`: Custom middleware

   - Role-based authentication
   - Error handling
   - Logging

4. `types/`: TypeScript type definitions
   - Separate type files for each domain
   - Common API types

Important Next.js 14 Features Used:

1. Route Handlers:

```typescript
// app/api/vendor/products/route.ts
import { NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  // Handle GET request
}

export async function POST(request: NextRequest) {
  // Handle POST request
}
```

2. Middleware:

```typescript
// middleware.ts
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(request: NextRequest) {
  // Middleware logic
}

export const config = {
  matcher: "/api/:path*",
};
```

3. Error Handling:

```typescript
// lib/utils/error-handler.ts
export class ApiError extends Error {
  constructor(public statusCode: number, message: string, public errors?: any) {
    super(message);
  }
}
```
