import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

interface JWTPayload {
  id: string;
  email: string;
  role: string;
}

/**
 * Register or update FCM token for a vendor
 * POST /api/v1/fcm/vendor-token
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized - No auth token found", success: false },
        { status: 401 }
      );
    }

    // Check if JWT_KEY is defined
    if (!process.env.JWT_KEY) {
      console.error("JWT_KEY is not defined in environment variables");
      return NextResponse.json(
        { message: "Server configuration error", success: false },
        { status: 500 }
      );
    }

    // Decode JWT token
    let decoded: JWTPayload;
    try {
      decoded = jwt.verify(
        authToken.value,
        process.env.JWT_KEY
      ) as JWTPayload;
    } catch (jwtError) {
      console.error("JWT verification error:", jwtError);
      return NextResponse.json(
        {
          message: "Invalid authentication token",
          success: false,
          error: jwtError instanceof Error ? jwtError.message : String(jwtError)
        },
        { status: 401 }
      );
    }

    // Verify user is a vendor (case-insensitive comparison)
    if (decoded.role.toUpperCase() !== "VENDOR") {
      return NextResponse.json(
        { message: "Unauthorized - Vendor access required", success: false },
        { status: 403 }
      );
    }

    // Get request body
    let token, deviceInfo;
    try {
      const body = await request.json();
      token = body.token;
      deviceInfo = body.deviceInfo;
    } catch (parseError) {
      console.error("Error parsing request body:", parseError);
      return NextResponse.json(
        {
          message: "Invalid request body - JSON parsing failed",
          success: false,
          error: parseError instanceof Error ? parseError.message : String(parseError)
        },
        { status: 400 }
      );
    }

    if (!token) {
      return NextResponse.json(
        { message: "FCM token is required", success: false },
        { status: 400 }
      );
    }

    // Find vendor
    const vendor = await prisma.vendor.findFirst({
      where: { email: decoded.email },
    });

    if (!vendor) {
      return NextResponse.json(
        { message: "Vendor not found", success: false },
        { status: 404 }
      );
    }

    // Check if token already exists
    const existingToken = await prisma.fCMToken.findUnique({
      where: { token },
    });

    if (existingToken) {
      // Update existing token
      const updatedToken = await prisma.fCMToken.update({
        where: { id: existingToken.id },
        data: {
          userId: vendor.id,
          userType: "vendor",
          deviceInfo: deviceInfo ? (typeof deviceInfo === 'string' ? deviceInfo : JSON.stringify(deviceInfo)) : existingToken.deviceInfo,
          isActive: true,
          updatedAt: new Date(),
        },
      });

      return NextResponse.json({
        message: "Vendor FCM token updated successfully",
        success: true,
        data: updatedToken,
      });
    } else {
      // Create new token
      const newToken = await prisma.fCMToken.create({
        data: {
          token,
          userId: vendor.id,
          userType: "vendor",
          deviceInfo: deviceInfo ? (typeof deviceInfo === 'string' ? deviceInfo : JSON.stringify(deviceInfo)) : null,
          isActive: true,
        },
      });

      return NextResponse.json({
        message: "Vendor FCM token registered successfully",
        success: true,
        data: newToken,
      });
    }
  } catch (error) {
    console.error("Error registering vendor FCM token:", error);
    return NextResponse.json(
      {
        message: "Failed to register FCM token",
        success: false,
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}