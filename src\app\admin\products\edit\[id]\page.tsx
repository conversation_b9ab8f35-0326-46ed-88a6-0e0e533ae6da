"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import EditProductForm from "@/components/admin/EditProductForm";
import { Product } from "@prisma/client";
import { adminAPI as api } from "@/lib/axios";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

export default function AdminEditProductPage() {
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const params = useParams();
  const router = useRouter();

  // Add a key to force re-render when navigating back to this page
  const [refreshKey, setRefreshKey] = useState(0);

  // Add a timestamp to the URL to force a fresh fetch from the server
  useEffect(() => {
    const timestamp = new Date().getTime();
    if (window.location.href.indexOf('?') === -1) {
      window.history.replaceState(null, '', `${window.location.href}?t=${timestamp}`);
    } else if (window.location.href.indexOf('t=') === -1) {
      window.history.replaceState(null, '', `${window.location.href}&t=${timestamp}`);
    } else {
      const newUrl = window.location.href.replace(/t=\d+/, `t=${timestamp}`);
      window.history.replaceState(null, '', newUrl);
    }
  }, []);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        console.log("Fetching product:", params.id); // Debug log
        const { data } = await api.get(`/products/${params.id}`);
        console.log("Response:", data); // Debug log
        console.log("Product data:", data.data); // Debug log

        if (data.data?.category) {
          console.log("Category from API:", data.data.category);
        }

        if (data.data?.brand) {
          console.log("Brand from API:", data.data.brand);
        }

        if (data.data?.material) {
          console.log("Material from API:", data.data.material);
        }

        if (data.data?.size) {
          console.log("Size from API:", data.data.size);
        }

        if (data.success) {
          setProduct(data.data);
        } else {
          setError(data.message || "Failed to fetch product");
        }
      } catch (error: any) {
        console.error("Error details:", error.response || error); // Enhanced error logging
        const errorMessage =
          error.response?.data?.message || "Failed to fetch product";
        setError(errorMessage);

        if (error.response?.status === 401) {
          toast.error("Please login to continue");
          router.push("/admin/login");
        } else {
          toast.error(errorMessage);
        }
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchProduct();
    }
  }, [params.id, router, refreshKey]);

  if (loading) {
    return (
      <div className="p-6">
        <ToastContainer />
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-lg">Loading...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <ToastContainer />
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="p-6">
        <ToastContainer />
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          Product not found
        </div>
      </div>
    );
  }

  return (
    <div className="p-3 sm:p-6 max-w-full overflow-hidden">
      <ToastContainer />
      <h1 className="text-xl sm:text-2xl font-semibold mb-4 sm:mb-6">Edit Product</h1>
      <EditProductForm product={product} />
    </div>
  );
}
