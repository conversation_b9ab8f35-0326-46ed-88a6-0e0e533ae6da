"use client";
import { useEffect, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { vendorAPI as api } from "@/lib/axios";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

interface SubCategory {
  id: string;
  name: string;
  imageUrl: string;
  description: string;
  price: number | null;
  materialId: string | null;
  sizeId: string | null;
  packagingSizeImage?: string;
  material?: {
    name: string;
  };
  size?: {
    name: string;
  };
}

interface Category {
  id: string;
  name: string;
  imageUrl: string;
  description: string;
}

interface Material {
  id: string;
  name: string;
}

interface Size {
  id: string;
  name: string;
}

export default function CategoryBrandsPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { id: categoryId } = params;
  const [category, setCategory] = useState<Category | null>(null);
  const [subCategories, setSubCategories] = useState<SubCategory[]>([]);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [sizes, setSizes] = useState<Size[]>([]);
  const [loading, setLoading] = useState(true);

  // Filter states
  const [minPrice, setMinPrice] = useState<string>("");
  const [maxPrice, setMaxPrice] = useState<string>("");
  const [materialFilter, setMaterialFilter] = useState<string>("");
  const [sizeFilter, setSizeFilter] = useState<string>("");
  const [sort, setSort] = useState<string>("price_desc");
  const [priceFilterOpen, setPriceFilterOpen] = useState<boolean>(false);
const [tempMinPrice, setTempMinPrice] = useState<string>("");
const [tempMaxPrice, setTempMaxPrice] = useState<string>("");

  const getImageUrl = (url: string) => {
    if (!url) return null;
    if (url.startsWith("http://") || url.startsWith("https://")) {
      return url;
    }
    return `${process.env.NEXT_PUBLIC_API_URL || ""}${
      url.startsWith("/") ? "" : "/"
    }${url}`;
  };

  useEffect(() => {
    fetchCategoryDetails();
    fetchFilterOptions();
  }, [categoryId]);

  // Apply filters automatically whenever any filter changes
  useEffect(() => {
    applyFilters();
  }, [materialFilter, sizeFilter, minPrice, maxPrice, sort, categoryId]);

  const fetchCategoryDetails = async () => {
    try {
      const { data } = await api.get(`/categories/${categoryId}`);
      if (data.success) {
        setCategory(data.data);
      }
    } catch (error) {
      console.error("Error fetching category details:", error);
    }
  };

  const fetchFilterOptions = async () => {
    try {
      setLoading(true);
      const { data } = await api.get("/filters");
      if (data.success) {
        setMaterials(data.data.materials);
        setSizes(data.data.sizes);
      } else {
        setMaterials([]);
        setSizes([]);
      }
    } catch (error) {
      setMaterials([]);
      setSizes([]);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();

      if (minPrice) params.append("minPrice", minPrice);
      if (maxPrice) params.append("maxPrice", maxPrice);

      // **Send both filters if set — support simultaneous filtering**
      if (materialFilter) {
        params.append("materialId", materialFilter);
      }
      if (sizeFilter) {
        params.append("sizeId", sizeFilter);
      }

      params.append("sort", sort || "price_desc");

      const url = `/categories/${categoryId}/brands?${params.toString()}`;

      const { data } = await api.get(url);

      if (data.success) {
        let filteredSubCategories = data.data;

        if (materialFilter) {
          filteredSubCategories = filteredSubCategories.filter(
            (sc: SubCategory) => sc.materialId === materialFilter
          );
        }
        if (sizeFilter) {
          filteredSubCategories = filteredSubCategories.filter(
            (sc: SubCategory) => sc.sizeId === sizeFilter
          );
        }

        setSubCategories(filteredSubCategories);
      } else {
        setSubCategories([]);
      }
    } catch (error) {
      console.error("Error applying filters:", error);
      setSubCategories([]);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <div className="relative w-20 h-20">
          <div className="absolute top-0 left-0 right-0 bottom-0 animate-spin rounded-full h-20 w-20 border-4 border-t-blue-500 border-b-blue-700 border-l-transparent border-r-transparent"></div>
          <div className="absolute top-2 left-2 right-2 bottom-2 animate-pulse bg-white rounded-full flex items-center justify-center">
            <div className="h-8 w-8 text-blue-600">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"
                />
              </svg>
            </div>
          </div>
        </div>
        <p className="text-gray-600 font-medium mt-4">Loading sub-categories...</p>
      </div>
    );
  }

  return (
    <div>
      <ToastContainer />

      <div className="mb-8">
        {/* Back button */}
        <button
          onClick={() => router.push("/vendor/dashboard")}
          className="flex items-center text-blue-600 hover:text-blue-800 mb-4"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
          Back to Categories
        </button>

        <h1 className="text-2xl font-bold mb-2">{category?.name}</h1>
        {category?.description && (
          <p className="text-gray-600">{category?.description}</p>
        )}
      </div>

      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold uppercase tracking-wider">
          SUB CATEGORIES IN {category?.name.toUpperCase()}
        </h2>
      </div>

      {/* Filter Section */}
      <div className="mb-8 border-b pb-4">
        <div className="flex flex-wrap items-center gap-3">
          <span className="text-gray-600 font-medium flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
              />
            </svg>
            Filters:
          </span>

          <button
            onClick={() => {
              setMinPrice("");
              setMaxPrice("");
              setMaterialFilter("");
              setSizeFilter("");
              setSort("price_desc");
            }}
            className="text-blue-600 hover:text-blue-800 text-sm"
          >
            Reset Filters
          </button>

          <div className="relative">
            <select
              value={materialFilter}
              onChange={(e) => {
                setMaterialFilter(e.target.value);
              }}
              className="px-3 py-1.5 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm min-w-[140px]"
            >
              <option value="">All Materials</option>
              {materials.map((material) => (
                <option key={material.id} value={material.id}>
                  {material.name}
                </option>
              ))}
            </select>
          </div>

          <div className="relative">
            <select
              value={sizeFilter}
              onChange={(e) => {
                setSizeFilter(e.target.value);
              }}
              className="px-3 py-1.5 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm min-w-[140px]"
            >
              <option value="">All Sizes</option>
              {sizes.map((size) => (
                <option key={size.id} value={size.id}>
                  {size.name}
                </option>
              ))}
            </select>
          </div>

          <div className="relative">
            <button
              onClick={() => setPriceFilterOpen(!priceFilterOpen)}
              className="flex items-center gap-1 px-3 py-1.5 border rounded-md text-sm"
            >
              <span className="text-blue-600">₹</span> Price Range
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-4 w-4 ml-1 transition-transform ${
                  priceFilterOpen ? "rotate-180" : ""
                }`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            {priceFilterOpen && (
              <div className="absolute z-20 mt-1 w-[300px] bg-white border rounded-md shadow-lg p-4">
                <h3 className="text-gray-700 font-medium mb-3">Set Price Range</h3>
                <div className="flex gap-4 mb-4">
                  <div className="flex-1">
                    <label className="block text-sm text-gray-600 mb-1">
                      Min Price
                    </label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                        ₹
                      </span>
                      <input
                        type="text"
                         value={tempMinPrice}
                          onChange={(e) => setTempMinPrice(e.target.value)}
                        className="pl-7 pr-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm w-full"
                        placeholder="0"
                      />
                    </div>
                  </div>
                  <div className="flex-1">
                    <label className="block text-sm text-gray-600 mb-1">
                      Max Price
                    </label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                        ₹
                      </span>
                      <input
                        type="text"
                        value={tempMaxPrice}
                        onChange={(e) => setTempMaxPrice(e.target.value)}
                        className="pl-7 pr-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm w-full"
                        placeholder="10000"
                      />
                    </div>
                  </div>
                </div>
                <div className="flex justify-between">
                  <button
                    onClick={() => {
                       setTempMinPrice("");
  setTempMaxPrice("");
  setMinPrice("");
  setMaxPrice("");
  setPriceFilterOpen(false);
                    }}
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    Clear
                  </button>
                  <button
                    onClick={() => {
                       setMinPrice(tempMinPrice);
  setMaxPrice(tempMaxPrice);
  setPriceFilterOpen(false);
                    }}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                  >
                    Apply
                  </button>
                </div>
              </div>
            )}
          </div>

          <div className="ml-auto">
            <select
              value={sort}
              onChange={(e) => {
                setSort(e.target.value);
              }}
              className="px-3 py-1.5 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
            >
              <option value="price_desc">Price (High to Low)</option>
              <option value="price_asc">Price (Low to High)</option>
              <option value="name_asc">Name (A-Z)</option>
              <option value="name_desc">Name (Z-A)</option>
            </select>
          </div>
        </div>
      </div>

      {subCategories.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <p className="text-gray-500 text-lg">
            No sub categories found in this category.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {subCategories.map((subCategory) => (
            <div
              key={subCategory.id}
              className="cursor-pointer group relative overflow-hidden"
              onClick={() => {
                // Encode subcategory data to pass as URL parameter
                const subcategoryData = encodeURIComponent(JSON.stringify({
                  id: subCategory.id,
                  name: subCategory.name,
                  packagingSizeImage: subCategory.packagingSizeImage
                }));
                router.push(
                  `/vendor/categories/${categoryId}/brands/${subCategory.id}/products?subcategory=${subcategoryData}`
                );
              }}
            >
              <div className="aspect-square relative overflow-hidden">
                {subCategory.imageUrl ? (
                  <Image
                    src={getImageUrl(subCategory.imageUrl) || "/placeholder.png"}
                    alt={subCategory.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-700"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                    <span className="text-gray-400">No image</span>
                  </div>
                )}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-100 transition-opacity duration-300"></div>
                <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                  <h3 className="font-bold text-xl uppercase tracking-wide">
                    {subCategory.name}
                  </h3>
                  {subCategory.price && (
                    <p className="text-white text-sm mt-1">
                      Price: ₹{subCategory.price}
                    </p>
                  )}
                  {subCategory.materialId && (
                    <p className="text-white text-sm">
                      Material:{" "}
                      {subCategory.material?.name ||
                        materials.find((m) => m.id === subCategory.materialId)
                          ?.name ||
                        "Unknown"}
                    </p>
                  )}
                  {subCategory.sizeId && (
                    <p className="text-white text-sm">
                      Size:{" "}
                      {subCategory.size?.name ||
                        sizes.find((s) => s.id === subCategory.sizeId)?.name ||
                        "Unknown"}
                    </p>
                  )}
                  <p className="text-white text-xs mt-1 opacity-70">
                    ID: {subCategory.id.substring(0, 6)}...
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
