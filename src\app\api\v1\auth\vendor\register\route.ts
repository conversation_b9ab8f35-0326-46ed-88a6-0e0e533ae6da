import { prisma } from "@/lib/prisma";
import { Prisma, NotificationType } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";
import { notifyAllAdmins } from "@/lib/notification-service";

interface CreateVendorRequest {
  email: string;
  businessName: string;
  taxId: string;
  contactPerson: string;
  phoneNumber: string;
  address: string;
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  currentDealers?: string; // Make it optional
  isPhoneVerified?: boolean; // Flag to indicate if phone is verified
}

function validateRequest(body: any): string | null {
  if (!body.email?.trim()) return "Email is required";
  if (!body.businessName?.trim()) return "Business name is required";
  if (!body.taxId?.trim()) return "Tax ID is required";
  if (!body.phoneNumber?.trim()) return "Phone number is required";
  if (!body.contactPerson?.trim()) return "Contact person is required";
  if (!body.address?.trim()) return "Address is required";
  if (!body.city?.trim()) return "City is required";
  if (!body.state?.trim()) return "State is required";
  if (!body.postalCode?.trim()) return "Postal code is required";
  return null;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    const validationError = validateRequest(body);
    if (validationError) {
      return NextResponse.json(
        { success: false, message: validationError },
        { status: 400 }
      );
    }

    const {
      email,
      businessName,
      taxId,
      contactPerson,
      phoneNumber,
      address,
      street,
      city,
      state,
      postalCode,
      country,
      currentDealers,
    }: CreateVendorRequest = body;

    // Check if phone number is already registered
    const existingVendorWithPhone = await prisma.vendor.findUnique({
      where: { phoneNumber },
    });

    if (existingVendorWithPhone) {
      return NextResponse.json(
        {
          success: false,
          message: "A vendor with this phone number already exists",
        },
        { status: 409 }
      );
    }

    const vendor = await prisma.vendor.create({
      data: {
        email,
        businessName,
        taxId,
        contactPerson,
        phoneNumber,
        address,
        street,
        city,
        state,
        postalCode,
        country: country || "India",
        status: "PENDING",
        currentDealers: currentDealers || null, // Handle optional field
        isPhoneVerified: false, // Initially set to false, will be updated after OTP verification
      },
    });

    // Send notification to all admins about new vendor registration
    try {
      await notifyAllAdmins(
        "New Vendor Registration",
        `${businessName} has registered as a vendor and is pending approval.`,
        NotificationType.VENDOR_REGISTRATION,
        {
          vendorId: vendor.id,
          businessName: vendor.businessName,
          email: vendor.email,
          phoneNumber: vendor.phoneNumber,
        }
      );
    } catch (notificationError) {
      console.error("Error sending admin notification:", notificationError);
      // Continue even if notification fails
    }

    return NextResponse.json(
      {
        message: "Vendor Created Successfully",
        success: true,
        data: vendor
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2002') {
        const field = error.meta?.target as string[];
        const duplicateField = field?.[0] || 'field';
        return NextResponse.json(
          {
            success: false,
            message: `A vendor with this ${duplicateField} already exists`,
          },
          { status: 409 }
        );
      }
    }

    console.error("Vendor registration error:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to register vendor",
        error: error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 }
    );
  }
}
