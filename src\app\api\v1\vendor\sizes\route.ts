import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const sizes = await prisma.size.findMany({
      where: {
        isActive: true,
      },
      orderBy: {
        name: "asc",
      },
    });

    return NextResponse.json({
      message: "<PERSON><PERSON> fetched successfully",
      success: true,
      data: sizes,
    });
  } catch (error) {
    console.error("Error fetching sizes:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch sizes",
        success: false,
      },
      { status: 500 }
    );
  }
}
