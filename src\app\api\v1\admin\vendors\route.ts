// get all vendors

import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { Prisma } from "@prisma/client";

// add a vendor
export async function GET() {
  try {
    const vendors = await prisma.vendor.findMany({
      select: {
        id: true,
        businessName: true,
        email: true,
        phoneNumber: true,
        status: true,
        createdAt: true,
        contactPerson: true,
        maxCredit: true,
        isCreditGiven: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({
      message: "Vendors fetched successfully",
      success: true,
      data: vendors,
    });
  } catch (error) {
    console.error("Error fetching vendors:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch vendors",
        success: false,
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const {
      businessName,
      email,
      phoneNumber,
      contactPerson,
      taxId,
      address,
      street,
      city,
      state,
      postalCode,
      country
    } = await request.json();

    const vendor = await prisma.vendor.create({
      data: {
        businessName,
        email,
        phoneNumber,
        contactPerson,
        taxId,
        address,
        street,
        city,
        state,
        postalCode,
        country: country || "India",
        status: "PENDING",
      },
    });



    return NextResponse.json(
      { success: true, message: "Vendor created successfully", data: vendor },
      { status: 201 }
    );

  } catch (error) {
    console.error("Vendor creation error:", error);

    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      // Handle unique constraint violations
      if (error.code === 'P2002') {
        const field = error.meta?.target as string[];
        const duplicateField = field?.[0] || 'field';
        return NextResponse.json(
          {
            success: false,
            message: `A vendor with this ${duplicateField} already exists`,
          },
          { status: 409 }
        );
      }

      // Handle validation errors
      if (error.code === 'P2009') {
        return NextResponse.json(
          {
            success: false,
            message: "Invalid data provided",
          },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to create vendor",
        error: error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 }
    );
  }
}
