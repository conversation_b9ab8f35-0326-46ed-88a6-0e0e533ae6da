// src/app/api/upload/route.ts
import { NextRequest, NextResponse } from "next/server";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { v4 as uuidv4 } from "uuid";
import sharp from "sharp";

// Set up S3 client for DigitalOcean Spaces
const s3Client = new S3Client({
  endpoint: "https://blr1.digitaloceanspaces.com",
  region: "blr1",
  credentials: {
    accessKeyId: process.env.DO_SPACES_KEY || "DO00K2V3BVCBPJMK9HJV",
    secretAccessKey:
      process.env.DO_SPACES_SECRET ||
      "Ts+XJ6aS42PAW4nJLaPqimsjUVSSAKGQQwyMSm0tC2c",
  },
  forcePathStyle: true, // Required for DO Spaces
});

const BUCKET_NAME = process.env.DO_SPACES_BUCKET || "mpolis";
const CDN_ENDPOINT = "https://mpolis.blr1.cdn.digitaloceanspaces.com";

// Function to resize product image to specified aspect ratio
async function resizeProductImage(buffer: Buffer, aspectRatio: string = "9:16"): Promise<Buffer> {
  try {
    // Define dimensions based on aspect ratio
    let width, height;

    if (aspectRatio === "1:1") {
      // Square ratio
      width = 1000;
      height = 1000;
    } else {
      // Default to 9:16 ratio (portrait)
      width = 562; // This maintains a 9:16 ratio with height=1000
      height = 1000;
    }

    // Create a white background canvas with the specified ratio
    const canvas = sharp({
      create: {
        width: width,
        height: height,
        channels: 4,
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      }
    });

    // Resize the original image to fit within the 9:16 dimensions while preserving aspect ratio
    const resizedImage = await sharp(buffer)
      .resize({
        width: width,
        height: height,
        fit: 'inside', // Ensures the image fits within the dimensions without cropping
        withoutEnlargement: false, // Allow small images to be enlarged
      })
      .toBuffer();

    // Get the dimensions of the resized image
    const resizedMetadata = await sharp(resizedImage).metadata();
    const resizedWidth = resizedMetadata.width || 0;
    const resizedHeight = resizedMetadata.height || 0;

    // Calculate position to center the image on the canvas
    const left = Math.floor((width - resizedWidth) / 2);
    const top = Math.floor((height - resizedHeight) / 2);

    // Composite the resized image onto the white canvas
    return await canvas
      .composite([
        {
          input: resizedImage,
          left,
          top,
        },
      ])
      .jpeg({ quality: 90 }) // Use JPEG format with 90% quality
      .toBuffer();
  } catch (error) {
    console.error("Error resizing product image:", error);
    throw error;
  }
}

// Function to resize banner image to 1920x1080
async function resizeBannerImage(buffer: Buffer): Promise<Buffer> {
  try {
    // Resize the image to 1920x1080 while preserving aspect ratio and cropping if needed
    return await sharp(buffer)
      .resize({
        width: 1920,
        height: 1080,
        fit: 'cover', // Cover ensures the image fills the entire dimensions, cropping if necessary
        position: 'center', // Center the crop
      })
      .jpeg({ quality: 90 }) // Use JPEG format with 90% quality
      .toBuffer();
  } catch (error) {
    console.error("Error resizing banner image:", error);
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    // Parse the form data from the request
    const formData = await request.formData();
    const file = formData.get("file") as File | null;
    const shouldResize = formData.get("resize") === "true";
    const imageType = formData.get("type") as string || "product";
    const aspectRatio = formData.get("aspectRatio") as string || "9:16";
    const customWidth = formData.get("width") ? parseInt(formData.get("width") as string) : null;
    const customHeight = formData.get("height") ? parseInt(formData.get("height") as string) : null;

    if (!file) {
      return NextResponse.json(
        { success: false, message: "No file uploaded" },
        { status: 400 }
      );
    }

    // Get file extension and create a unique filename
    const fileExtension = file.name.split(".").pop()?.toLowerCase() || "jpg";
    const uniqueFileName = `uploads/${uuidv4()}.${fileExtension}`;

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    let buffer = Buffer.from(arrayBuffer);

    // Resize the image if requested
    if (shouldResize) {
      if (imageType === "banner") {
        // Use banner resize function for banner images
        buffer = await resizeBannerImage(buffer);
      } else {
        // Use product resize function for product images and other types
        buffer = await resizeProductImage(buffer, aspectRatio);
      }
    }

    // Upload to DigitalOcean Spaces
    await s3Client.send(
      new PutObjectCommand({
        Bucket: BUCKET_NAME,
        Key: uniqueFileName,
        Body: buffer,
        ContentType: "image/jpeg", // We're converting everything to JPEG for consistency
        ACL: "public-read",
      })
    );

    // Construct the full URL to the uploaded file using the CDN endpoint
    // const fileUrl = `${CDN_ENDPOINT}/${uniqueFileName}`;
    const fileUrl = CDN_ENDPOINT + "/" + uniqueFileName;

    return NextResponse.json({
      success: true,
      path: fileUrl,
      message: "File uploaded successfully",
    });
  } catch (error) {
    console.error("Upload error:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to upload file",
        error: (error as Error).message,
      },
      { status: 500 }
    );
  }
}

// Increase the body size limit for file uploads (default is 2MB)
// export const config = {
//   api: {
//     bodyParser: {
//       sizeLimit: "5mb",
//     },
//   },
// };
