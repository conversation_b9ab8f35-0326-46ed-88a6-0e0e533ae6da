import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

interface JWTPayload {
  id: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

/**
 * Mark a notification as read
 * PUT /api/v1/notifications/:id/read
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Verify authentication
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    // Decode JWT token
    const decoded = jwt.verify(
      authToken.value,
      process.env.JWT_KEY!
    ) as JWTPayload;

    // Determine user type and get user ID
    const userType = decoded.role === "admin" ? "admin" : "vendor";
    let userId: string;

    if (userType === "admin") {
      const admin = await prisma.adminUser.findFirst({
        where: { email: decoded.email },
      });
      if (!admin) {
        return NextResponse.json(
          { message: "Admin user not found", success: false },
          { status: 404 }
        );
      }
      userId = admin.id;
    } else {
      const vendor = await prisma.vendor.findFirst({
        where: { email: decoded.email },
      });
      if (!vendor) {
        return NextResponse.json(
          { message: "Vendor not found", success: false },
          { status: 404 }
        );
      }
      userId = vendor.id;
    }

    // Find the notification
    const notification = await prisma.notification.findUnique({
      where: { id },
    });

    if (!notification) {
      return NextResponse.json(
        { message: "Notification not found", success: false },
        { status: 404 }
      );
    }

    // Check if the notification belongs to the user
    if (
      notification.recipientType !== userType ||
      notification.recipientId !== userId
    ) {
      return NextResponse.json(
        { message: "Unauthorized to access this notification", success: false },
        { status: 403 }
      );
    }

    // Update the notification
    const updatedNotification = await prisma.notification.update({
      where: { id },
      data: { isRead: true },
    });

    return NextResponse.json({
      message: "Notification marked as read",
      success: true,
      data: updatedNotification,
    });
  } catch (error) {
    console.error("Error marking notification as read:", error);
    return NextResponse.json(
      {
        message: "Failed to mark notification as read",
        success: false,
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
