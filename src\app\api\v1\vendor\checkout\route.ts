import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";
import { Prisma, NotificationType } from "@prisma/client";
import { notifyAllAdmins } from "@/lib/notification-service";


export async function POST(request: NextRequest) {
  try {
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
      email: string;
      role: string;
    };

    const vendor = await prisma.vendor.findUnique({
      where: { email: decoded.email },
    });

    if (!vendor) {
      return NextResponse.json(
        { message: "Vendor not found", success: false },
        { status: 404 }
      );
    }

    const { shippingAddress, paymentMethod, transport, referenceId } = await request.json();

    // Validate required fields
    if (!shippingAddress) {
      return NextResponse.json(
        { message: "Shipping address is required", success: false },
        { status: 400 }
      );
    }

    // Transport is now optional, so we don't validate it

    if (!paymentMethod) {
      return NextResponse.json(
        { message: "Payment method is required", success: false },
        { status: 400 }
      );
    }

    if (paymentMethod === "PAY_ONLINE" && !referenceId) {
      return NextResponse.json(
        { message: "Payment reference ID is required for online payments", success: false },
        { status: 400 }
      );
    }

    const cart = await prisma.cart.findFirst({
      where: {
        vendorId: vendor.id
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    // If payment method is CREDIT, check if vendor has available credit
    if (paymentMethod === "CREDIT") {
      if (!vendor.isCreditGiven || vendor.maxCredit <= 0) {
        return NextResponse.json(
          { message: "You don't have credit available for this purchase", success: false },
          { status: 400 }
        );
      }

      // Calculate cart total
      const cartTotal = cart?.items.reduce(
        (total, item) => total + item.quantity * item.product.basePrice,
        0
      ) || 0;

      // Check if vendor has enough credit for this purchase
      if (cartTotal > vendor.maxCredit) {
        return NextResponse.json(
          {
            message: "Your purchase amount exceeds your available credit limit",
            success: false
          },
          { status: 400 }
        );
      }
    }

    if (!cart || !cart.items || cart.items.length === 0) {
      return NextResponse.json(
        { message: "Cart is empty", success: false },
        { status: 400 }
      );
    }

    // Start transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      // Create order
      const order = await tx.order.create({
        data: {
          vendorId: vendor.id,
          totalAmount: cart.items.reduce(
            (total, item) => total + item.quantity * item.product.basePrice,
            0
          ),
          choiceOfTransport: transport || "",
          shippingAddress,
          paymentMethod,
          paymentReferenceId: paymentMethod === "PAY_ONLINE" ? referenceId : null,
          status: "PENDING",
          items: {
            create: cart.items.map((item) => ({
              productId: item.productId,
              quantity: item.quantity,
              price: item.product.basePrice,
            })),
          },
        },
      });

      // Stock quantity tracking has been removed
      // No need to update product stock quantities

      // Clear cart
      await tx.cartItem.deleteMany({
        where: { cartId: cart.id },
      });

      await tx.cart.delete({
        where: { id: cart.id },
      });

      return order;
    });

    // Send notification to all admins about new order
    try {
      await notifyAllAdmins(
        "New Order Received",
        `${vendor.businessName} has placed a new order for ₹${result.totalAmount.toFixed(2)}.`,
        NotificationType.ORDER_CREATED,
        {
          orderId: result.id,
          vendorId: vendor.id,
          vendorName: vendor.businessName,
          totalAmount: result.totalAmount,
          paymentMethod,
        }
      );
    } catch (notificationError) {
      console.error("Error sending admin notification:", notificationError);
      // Continue even if notification fails
    }

    return NextResponse.json({
      message: "Order placed successfully",
      success: true,
      data: { id: result.id },
    });
  } catch (error) {
    console.error("Error during checkout:", error);

    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2002') {
        return NextResponse.json(
          { message: "Duplicate order detected", success: false },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      {
        message: "Failed to place order",
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred"
      },
      { status: 500 }
    );
  }
}
