import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";
import { createOrderNotification } from "@/lib/notifications";

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the auth token from cookies
    const authToken = request.cookies.get("authToken");

    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized - No token", success: false },
        { status: 401 }
      );
    }

    try {
      // Verify the token
      const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
        id: string;
        email: string;
        role: string;
      };

      // Check if the user is an admin
      if (decoded.role !== "ADMIN") {
        return NextResponse.json(
          { message: "Unauthorized - Not an admin", success: false },
          { status: 401 }
        );
      }
    } catch (jwtError) {
      console.error("JWT verification error:", jwtError);
      return NextResponse.json(
        { message: "Unauthorized - Invalid token", success: false },
        { status: 401 }
      );
    }

    const { id } = params;
    console.log("Order ID:", id);

    const body = await request.json();
    console.log("Request body:", body);

    const { status } = body;
    console.log("New status:", status);

    // Check if the order exists
    const existingOrder = await prisma.order.findUnique({
      where: { id },
    });

    if (!existingOrder) {
      console.log("Order not found:", id);
      return NextResponse.json(
        { message: "Order not found", success: false },
        { status: 404 }
      );
    }

    console.log("Existing order:", existingOrder);

    // Update the order status
    const order = await prisma.order.update({
      where: { id },
      data: { status },
      include: {
        vendor: true, // Include vendor details for notification
      },
    });

    // Send notification to vendor about order status change
    try {
      let notificationTitle = "Order Status Updated";
      let notificationBody = "";

      switch (status) {
        case "APPROVED":
          notificationTitle = "Order Approved";
          notificationBody = `Your order no. ${order.id.substring(0, 8)} has been approved.`;
          break;
        case "REJECTED":
          notificationTitle = "Order Rejected";
          notificationBody = `Your order no. ${order.id.substring(0, 8)} has been rejected.`;
          break;
        case "PROCESSING":
          notificationTitle = "Order Processing";
          notificationBody = `Your order no. ${order.id.substring(0, 8)} is now being processed.`;
          break;
        case "SHIPPED":
          notificationTitle = "Order Shipped";
          notificationBody = `Your order no. ${order.id.substring(0, 8)} has been shipped.`;
          break;
        case "DELIVERED":
          notificationTitle = "Order Delivered";
          notificationBody = `Your order no. ${order.id.substring(0, 8)} has been delivered.`;
          break;
        case "CANCELLED":
          notificationTitle = "Order Cancelled";
          notificationBody = `Your order no. ${order.id.substring(0, 8)} has been cancelled.`;
          break;
        default:
          notificationBody = `Your order no. ${order.id.substring(0, 8)} status has been updated to ${status}.`;
      }

      await createOrderNotification({
        orderId: order.id,
        vendorId: order.vendor.id,
        title: notificationTitle,
        body: notificationBody,
        type: "ORDER_STATUS_CHANGED",
      });
    } catch (notificationError) {
      console.error("Error sending vendor notification:", notificationError);
      // Continue even if notification fails
    }

    return NextResponse.json({
      message: "Order status updated successfully",
      success: true,
      data: order,
    });
  } catch (error) {
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "An unknown error occurred",
        success: false,
      },
      { status: 500 }
    );
  }
}
