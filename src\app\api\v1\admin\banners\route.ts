import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

// Interface for creating a banner
interface CreateBannerRequest {
  imageUrl: string;
  name: string;
  isActive?: boolean;
}

// GET all banners
export async function GET() {
  try {
    const banners = await prisma.vendorBanner.findMany({
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({
      message: "Banners fetched successfully",
      success: true,
      data: banners,
    });
  } catch (error) {
    console.error("Error fetching banners:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch banners",
        success: false,
      },
      { status: 500 }
    );
  }
}

// POST a new banner
export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as CreateBannerRequest;
    const { imageUrl, name, isActive = true } = body;

    // Validate required fields
    if (!imageUrl || !name) {
      return NextResponse.json(
        {
          message: "Image URL and name are required",
          success: false,
        },
        { status: 400 }
      );
    }

    const banner = await prisma.vendorBanner.create({
      data: {
        imageUrl,
        name,
        isActive,
      },
    });

    return NextResponse.json({
      message: "Banner created successfully",
      success: true,
      data: banner,
    });
  } catch (error) {
    console.error("Banner creation error:", error);
    return NextResponse.json(
      {
        message: "Failed to create banner",
        success: false,
      },
      { status: 500 }
    );
  }
}
