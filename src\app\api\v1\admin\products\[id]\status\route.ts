import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the auth token from cookies
    const authToken = request.cookies.get("authToken");

    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized - No token", success: false },
        { status: 401 }
      );
    }

    try {
      // Verify the token
      const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
        id: string;
        email: string;
        role: string;
      };

      // Check if the user is an admin
      if (decoded.role !== "ADMIN") {
        return NextResponse.json(
          { message: "Unauthorized - Not an admin", success: false },
          { status: 401 }
        );
      }
    } catch (jwtError) {
      console.error("JWT verification error:", jwtError);
      return NextResponse.json(
        { message: "Unauthorized - Invalid token", success: false },
        { status: 401 }
      );
    }

    // Get the product ID from the URL params
    const { id } = params;

    // Get the isActive status from the request body
    const { isActive } = await request.json();

    // Check if the product exists
    const product = await prisma.product.findUnique({
      where: { id },
    });

    if (!product) {
      return NextResponse.json(
        { message: "Product not found", success: false },
        { status: 404 }
      );
    }

    // Update the product status
    const updatedProduct = await prisma.product.update({
      where: { id },
      data: { isActive },
    });

    return NextResponse.json({
      message: "Product status updated successfully",
      success: true,
      data: updatedProduct,
    });
  } catch (error) {
    console.error("Error updating product status:", error);
    return NextResponse.json(
      { message: "Failed to update product status", success: false },
      { status: 500 }
    );
  }
}
