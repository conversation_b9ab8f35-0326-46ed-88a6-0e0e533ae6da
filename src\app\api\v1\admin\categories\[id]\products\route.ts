import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const categoryId = params.id;

    const products = await prisma.product.findMany({
      where: {
        categoryId,
        isActive: true,
      },
      include: {
        category: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    console.log(`Fetched products for category ${categoryId}:`, products);

    return NextResponse.json({
      message: "Products fetched successfully",
      success: true,
      data: products,
    });
  } catch (error) {
    console.error("Error fetching category products:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch products",
        success: false,
      },
      { status: 500 }
    );
  }
}
