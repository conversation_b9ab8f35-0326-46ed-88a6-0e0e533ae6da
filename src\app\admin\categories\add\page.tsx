// src/app/admin/categories/add/page.tsx
"use client";

import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { toast, ToastContainer } from "react-toastify";
import { useRouter } from "next/navigation";
import axios from "axios";
import "react-toastify/dist/ReactToastify.css";
import { useState } from "react";
import ImageUploaderNoCrop from "@/components/admin/ImageUploaderNoCrop";
import { adminAPI as api } from "@/lib/axios";
import slugify from "slugify";

const categorySchema = yup.object({
  name: yup.string().required("Category name is required"),
  isActive: yup.boolean(),
});

type CategoryFormData = yup.InferType<typeof categorySchema> & {
  imageAspectRatio?: string;
};

export default function AddCategory() {
  const router = useRouter();
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [selectedAspectRatio, setSelectedAspectRatio] = useState<string>("1:1");

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<CategoryFormData>({
    resolver: yupResolver(categorySchema),
    defaultValues: {
      isActive: true,
    },
  });

  const handleImagesUploaded = (imageUrls: string[]) => {
    setUploadedImages(imageUrls);
  };

  const onSubmit = async (data: CategoryFormData) => {
    try {
      if (uploadedImages.length === 0) {
        toast.error("Please upload an image for the category");
        return;
      }

      // Generate slug from name
      const slug = slugify(data.name, { lower: true, strict: true });

      await api.post("/categories", {
        ...data,
        slug,
        imageUrl: uploadedImages[0],
        imageAspectRatio: selectedAspectRatio,
      });

      toast.success("Category created successfully");
      router.push("/admin/categories");
    } catch (error) {
      if (axios.isAxiosError(error)) {
        toast.error(error.response?.data?.message || "Error creating category");
      } else {
        toast.error("Error creating category");
      }
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <ToastContainer />
      <h1 className="text-2xl font-bold mb-6">Add New Category</h1>

      <form
        onSubmit={handleSubmit(onSubmit)}
        className="space-y-6 bg-white p-6 rounded-lg shadow"
      >
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Category Name <span className="text-red-500">*</span>
          </label>
          <input
            {...register("name")}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="Enter category name"
          />
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Image Aspect Ratio <span className="text-red-500">*</span>
          </label>
          <select
            value={selectedAspectRatio}
            onChange={(e) => setSelectedAspectRatio(e.target.value)}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="1:1">Square (1:1)</option>
            <option value="9:16">Portrait (9:16)</option>
          </select>
          <p className="mt-1 text-xs text-gray-500">
            This aspect ratio will be used for all products in this category
          </p>
        </div>


        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Category Image <span className="text-red-500">*</span>
          </label>
          <ImageUploaderNoCrop
            onImagesUploaded={handleImagesUploaded}
            multiple={false}
            maxFiles={1}
            existingImages={uploadedImages}
            imageAspectRatio={selectedAspectRatio}
          />
          <p className="mt-1 text-xs text-gray-500">
            {selectedAspectRatio === "1:1"
              ? "Images will be automatically resized to 1:1 ratio (1000x1000px) without cropping"
              : "Images will be automatically resized to 9:16 ratio (562x1000px) without cropping"}
          </p>
        </div>



        <div>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              {...register("isActive")}
              className="rounded"
            />
            <span className="text-sm font-medium text-gray-700">Active</span>
          </label>
        </div>

        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-4 py-2 border rounded-md hover:bg-gray-100"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300"
          >
            {isSubmitting ? "Creating..." : "Create Category"}
          </button>
        </div>
      </form>
    </div>
  );
}
