import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

interface JWTPayload {
  id: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

// GET the active payment QR code for vendors
export async function GET(request: NextRequest) {
  try {
    // Verify the vendor is authenticated using JWT token from cookies
    const authToken = request.cookies.get("authToken");
    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized", success: false },
        { status: 401 }
      );
    }

    // Verify and decode the JWT token
    const decoded = jwt.verify(
      authToken.value,
      process.env.JWT_KEY!
    ) as JWTPayload;

    // Find the vendor associated with this user
    const vendor = await prisma.vendor.findFirst({
      where: { email: decoded.email },
    });

    if (!vendor) {
      return NextResponse.json(
        { message: "Vendor account not found", success: false },
        { status: 404 }
      );
    }

    // Get the active payment QR code from the database
    console.log("Fetching active payment QR code from database");

    const activeQRCode = await prisma.paymentQRCode.findFirst({
      where: { isActive: true },
      orderBy: { updatedAt: 'desc' },
    });

    console.log("Active QR code:", activeQRCode);

    if (!activeQRCode) {
      console.log("No active QR code found, using fallback");
      // Return a fallback QR code if none is found
      return NextResponse.json({
        message: "No active payment QR code found",
        success: false,
        data: {
          id: "fallback-qr-code",
          imageUrl: "/payment-qr-code.png", // Use the existing QR code image
          name: "Payment QR Code",
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
      });
    }

    return NextResponse.json({
      message: "Payment QR code fetched successfully",
      success: true,
      data: activeQRCode,
    });
  } catch (error) {
    console.error("Error fetching payment QR code:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch payment QR code",
        success: false,
      },
      { status: 500 }
    );
  }
}
