import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

// Get a specific size
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const size = await prisma.size.findUnique({
      where: { id: params.id },
    });

    if (!size) {
      return NextResponse.json(
        {
          message: "Size not found",
          success: false,
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: "Size fetched successfully",
      success: true,
      data: size,
    });
  } catch (error) {
    console.error("Error fetching size:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch size",
        success: false,
      },
      { status: 500 }
    );
  }
}

// Update a size
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { name, description, isActive } = await request.json();

    // Check if size exists
    const existingSize = await prisma.size.findUnique({
      where: { id: params.id },
    });

    if (!existingSize) {
      return NextResponse.json(
        {
          message: "Size not found",
          success: false,
        },
        { status: 404 }
      );
    }

    // Check if another size with the same name exists
    if (name && name !== existingSize.name) {
      const sizeWithSameName = await prisma.size.findUnique({
        where: { name },
      });

      if (sizeWithSameName) {
        return NextResponse.json(
          {
            message: "Size with this name already exists",
            success: false,
          },
          { status: 400 }
        );
      }
    }

    const updatedSize = await prisma.size.update({
      where: { id: params.id },
      data: {
        name: name !== undefined ? name : undefined,
        description: description !== undefined ? description : undefined,
        isActive: isActive !== undefined ? isActive : undefined,
      },
    });

    return NextResponse.json({
      message: "Size updated successfully",
      success: true,
      data: updatedSize,
    });
  } catch (error) {
    console.error("Error updating size:", error);
    return NextResponse.json(
      {
        message: "Failed to update size",
        success: false,
      },
      { status: 500 }
    );
  }
}

// Delete a size
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if size exists
    const existingSize = await prisma.size.findUnique({
      where: { id: params.id },
      include: {
        products: {
          select: { id: true },
          take: 1,
        },
        priceMasters: {
          select: { id: true },
          take: 1,
        },
      },
    });

    if (!existingSize) {
      return NextResponse.json(
        {
          message: "Size not found",
          success: false,
        },
        { status: 404 }
      );
    }

    // Check if size is being used by products or price masters
    if (existingSize.products.length > 0 || existingSize.priceMasters.length > 0) {
      return NextResponse.json(
        {
          message: "Cannot delete size as it is being used by products or price masters",
          success: false,
        },
        { status: 400 }
      );
    }

    await prisma.size.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      message: "Size deleted successfully",
      success: true,
    });
  } catch (error) {
    console.error("Error deleting size:", error);
    return NextResponse.json(
      {
        message: "Failed to delete size",
        success: false,
      },
      { status: 500 }
    );
  }
}
