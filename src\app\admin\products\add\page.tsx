// src/app/admin/products/add/page.tsx
"use client";

import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { toast, ToastContainer } from "react-toastify";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import axios from "axios";
import "react-toastify/dist/ReactToastify.css";
import { adminAPI as api } from "@/lib/axios";
import ImageUploaderNoCrop from "@/components/admin/ImageUploaderNoCrop";
import { FiPlus } from "react-icons/fi";
import AddCategoryModal from "@/components/admin/AddCategoryModal";
import AddBrandModal from "@/components/admin/AddBrandModal";
import AddMaterialModal from "@/components/admin/AddMaterialModal";
import AddSizeModal from "@/components/admin/AddSizeModal";

const productSchema = yup.object({
  name: yup.string().required("Product name is required"),
  description: yup.string().required("Description is required"),
  basePrice: yup
    .number()
    .positive("Base price must be positive")
    .required("Base price is required"),
  gstPercentage: yup
    .number()
    .required("GST percentage is required"),
  maxOrderQuantity: yup.number().integer().min(1).nullable(),
  categoryId: yup.string().required("Category is required"),
  brandId: yup.string().required("Brand is required"),
  materialId: yup.string().required("Material is required"),
  sizeId: yup.string().required("Size is required"),
  isActive: yup.boolean(),
  isPublished: yup.boolean(),
  packagingSize: yup
    .number()
    .integer()
    .min(1)
    .required("Packaging size is required"),
  packagingUnit: yup.string().required("Packaging unit is required"),
});

type ProductFormData = yup.InferType<typeof productSchema>;

interface Category {
  id: string;
  name: string;
  imageAspectRatio?: string;
}

interface Brand {
  id: string;
  name: string;
  imageAspectRatio?: string;
}

interface Material {
  id: string;
  name: string;
}

interface Size {
  id: string;
  name: string;
}

export default function AddProduct() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [sizes, setSizes] = useState<Size[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [loadingBrands, setLoadingBrands] = useState(true);
  const [loadingMaterials, setLoadingMaterials] = useState(true);
  const [loadingSizes, setLoadingSizes] = useState(true);
  const [productImages, setProductImages] = useState<string[]>([]);
  const [selectedBrandAspectRatio, setSelectedBrandAspectRatio] = useState<string>("9:16");

  // Modal states
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false);
  const [isBrandModalOpen, setIsBrandModalOpen] = useState(false);
  const [isMaterialModalOpen, setIsMaterialModalOpen] = useState(false);
  const [isSizeModalOpen, setSizeModalOpen] = useState(false);

  useEffect(() => {
    console.log("Fetching categories, brands, materials, and sizes...");
    fetchCategories();
    fetchBrands();
    fetchMaterials();
    fetchSizes();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoadingCategories(true);
      console.log("Making API request to fetch categories...");
      const { data } = await api.get("/categories");
      console.log("Categories response:", data);
      if (data.success) {
        setCategories(data.data);
      } else {
        toast.error("Failed to fetch categories");
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      toast.error("Failed to fetch categories");
    } finally {
      setLoadingCategories(false);
    }
  };

  const fetchBrands = async () => {
    try {
      setLoadingBrands(true);
      const { data } = await api.get("/brands");
      if (data.success) {
        setBrands(data.data);
      } else {
        toast.error("Failed to fetch brands");
      }
    } catch (error) {
      console.error("Error fetching brands:", error);
      toast.error("Failed to fetch brands");
    } finally {
      setLoadingBrands(false);
    }
  };

  const fetchMaterials = async () => {
    try {
      setLoadingMaterials(true);
      const { data } = await api.get("/materials");
      if (data.success) {
        setMaterials(data.data);
      } else {
        toast.error("Failed to fetch materials");
      }
    } catch (error) {
      console.error("Error fetching materials:", error);
      toast.error("Failed to fetch materials");
    } finally {
      setLoadingMaterials(false);
    }
  };

  const fetchSizes = async () => {
    try {
      setLoadingSizes(true);
      const { data } = await api.get("/sizes");
      if (data.success) {
        setSizes(data.data);
      } else {
        toast.error("Failed to fetch sizes");
      }
    } catch (error) {
      console.error("Error fetching sizes:", error);
      toast.error("Failed to fetch sizes");
    } finally {
      setLoadingSizes(false);
    }
  };

  // Price Master functionality has been removed

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<ProductFormData>({
    resolver: yupResolver(productSchema),
    defaultValues: {
      isActive: true,
      isPublished: false,
      packagingSize: 10,
      packagingUnit: "piece",
      gstPercentage: 18, // Default GST percentage is 18%
    },
  });

  // Price Master functionality has been removed

  // Handle successful creation of entities
  const handleCategoryCreated = (category: Category) => {
    setCategories((prev) => [...prev, category]);
  };

  const handleBrandCreated = (brand: Brand) => {
    setBrands((prev) => [...prev, brand]);
  };

  const handleMaterialCreated = (material: Material) => {
    setMaterials((prev) => [...prev, material]);
  };

  const handleSizeCreated = (size: Size) => {
    setSizes((prev) => [...prev, size]);
  };

  const onSubmit = async (data: ProductFormData) => {
    try {
      setLoading(true);

      console.log("Images before submission:", productImages);

      const cleanedImages = productImages.map((img) =>
        img.replace(/^"|"$/g, "").replace(/\\"/g, '"')
      );

      console.log("Cleaned images for submission:", cleanedImages);

      // Get the names of the selected category, brand, material, and size
      const selectedCategory = categories.find(c => c.id === data.categoryId)?.name || '';
      const selectedBrand = brands.find(b => b.id === data.brandId)?.name || '';
      const selectedMaterial = materials.find(m => m.id === data.materialId)?.name || '';

      // Generate slug from category, brand, material, and product name
      const generatedSlug = `${selectedCategory.toLowerCase()}-${selectedBrand.toLowerCase()}-${selectedMaterial.toLowerCase()}-${data.name.toLowerCase()}`
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');

      console.log("Generated slug:", generatedSlug);

      // Price Master functionality has been removed

      // Include uploaded image URLs, generated slug, and image aspect ratio in the product data
      const { data: apiData } = await api.post("/products", {
        ...data,
        slug: generatedSlug,
        images: cleanedImages,
        imageAspectRatio: selectedBrandAspectRatio,
      });

      if (apiData.success) {
        toast.success("Product created successfully");
        router.push("/admin/products");
      }
    } catch (error) {
      console.error("Error creating product:", error);
      if (axios.isAxiosError(error)) {
        toast.error(error.response?.data?.message || "Error creating product");
      } else {
        toast.error("Error creating product");
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle images uploaded through our new component
  const handleImagesUploaded = (imageUrls: string[]) => {
    const cleanUrls = imageUrls.map((url) =>
      typeof url === "string"
        ? url.replace(/^"|"$/g, "").replace(/\\"/g, '"')
        : url
    );
    console.log("Received image URLs:", imageUrls);
    console.log("Cleaned image URLs:", cleanUrls);
    setProductImages(cleanUrls);
  };



  // Handle brand selection change to update aspect ratio
  const handleBrandChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const brandId = e.target.value;
    const selectedBrand = brands.find(b => b.id === brandId);
    if (selectedBrand && selectedBrand.imageAspectRatio) {
      setSelectedBrandAspectRatio(selectedBrand.imageAspectRatio);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <ToastContainer />
      <h1 className="text-2xl font-bold mb-6">Add New Product</h1>

      {/* Modal components */}
      <AddCategoryModal
        isOpen={isCategoryModalOpen}
        onClose={() => setIsCategoryModalOpen(false)}
        onSuccess={handleCategoryCreated}
      />
      <AddBrandModal
        isOpen={isBrandModalOpen}
        onClose={() => setIsBrandModalOpen(false)}
        onSuccess={handleBrandCreated}
      />
      <AddMaterialModal
        isOpen={isMaterialModalOpen}
        onClose={() => setIsMaterialModalOpen(false)}
        onSuccess={handleMaterialCreated}
      />
      <AddSizeModal
        isOpen={isSizeModalOpen}
        onClose={() => setSizeModalOpen(false)}
        onSuccess={handleSizeCreated}
      />

      <form
        onSubmit={handleSubmit(onSubmit)}
        className="space-y-6 bg-white p-6 rounded-lg shadow"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Product Name <span className="text-red-500">*</span>
            </label>
            <input
              {...register("name")}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder="Enter product name"
            />
            {errors.name && (
              <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
            )}
          </div>







          <div>
            <div className="flex justify-between items-center mb-1">
              <label className="block text-sm font-medium text-gray-700">
                Category <span className="text-red-500">*</span>
              </label>
              <button
                type="button"
                onClick={() => setIsCategoryModalOpen(true)}
                className="text-blue-500 hover:text-blue-700 text-sm flex items-center"
              >
                <FiPlus className="mr-1" size={14} /> Add Category
              </button>
            </div>
            <select
              {...register("categoryId")}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              disabled={loadingCategories}
            >
              <option value="">
                {loadingCategories
                  ? "Loading categories..."
                  : "Select category"}
              </option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
            {errors.categoryId && (
              <p className="text-red-500 text-sm mt-1">
                {errors.categoryId.message}
              </p>
            )}
          </div>

          <div>
            <div className="flex justify-between items-center mb-1">
              <label className="block text-sm font-medium text-gray-700">
                Sub Category <span className="text-red-500">*</span>
              </label>
              <button
                type="button"
                onClick={() => setIsBrandModalOpen(true)}
                className="text-blue-500 hover:text-blue-700 text-sm flex items-center"
              >
                <FiPlus className="mr-1" size={14} /> Add Sub Category
              </button>
            </div>
            <select
              {...register("brandId")}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              disabled={loadingBrands}
              onChange={handleBrandChange}
            >
              <option value="">
                {loadingBrands
                  ? "Loading sub categories..."
                  : "Select sub category"}
              </option>
              {brands.map((brand) => (
                <option key={brand.id} value={brand.id}>
                  {brand.name}
                </option>
              ))}
            </select>
            {errors.brandId && (
              <p className="text-red-500 text-sm mt-1">
                {errors.brandId.message}
              </p>
            )}
          </div>

          <div>
            <div className="flex justify-between items-center mb-1">
              <label className="block text-sm font-medium text-gray-700">
                Material <span className="text-red-500">*</span>
              </label>
              <button
                type="button"
                onClick={() => setIsMaterialModalOpen(true)}
                className="text-blue-500 hover:text-blue-700 text-sm flex items-center"
              >
                <FiPlus className="mr-1" size={14} /> Add Material
              </button>
            </div>
            <select
              {...register("materialId")}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              disabled={loadingMaterials}
            >
              <option value="">
                {loadingMaterials
                  ? "Loading materials..."
                  : "Select material"}
              </option>
              {materials.map((material) => (
                <option key={material.id} value={material.id}>
                  {material.name}
                </option>
              ))}
            </select>
            {errors.materialId && (
              <p className="text-red-500 text-sm mt-1">
                {errors.materialId.message}
              </p>
            )}
          </div>

          <div>
            <div className="flex justify-between items-center mb-1">
              <label className="block text-sm font-medium text-gray-700">
                Size <span className="text-red-500">*</span>
              </label>
              <button
                type="button"
                onClick={() => setSizeModalOpen(true)}
                className="text-blue-500 hover:text-blue-700 text-sm flex items-center"
              >
                <FiPlus className="mr-1" size={14} /> Add Size
              </button>
            </div>
            <select
              {...register("sizeId")}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              disabled={loadingSizes}
            >
              <option value="">
                {loadingSizes
                  ? "Loading sizes..."
                  : "Select size"}
              </option>
              {sizes.map((size) => (
                <option key={size.id} value={size.id}>
                  {size.name}
                </option>
              ))}
            </select>
            {errors.sizeId && (
              <p className="text-red-500 text-sm mt-1">
                {errors.sizeId.message}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Price <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              {...register("basePrice")}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            {errors.basePrice && (
              <p className="text-red-500 text-sm mt-1">
                {errors.basePrice.message}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              GST Percentage <span className="text-red-500">*</span>
            </label>
            <select
              {...register("gstPercentage")}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="5">5%</option>
              <option value="12">12%</option>
              <option value="18">18%</option>
            </select>
            {errors.gstPercentage && (
              <p className="text-red-500 text-sm mt-1">
                {errors.gstPercentage.message}
              </p>
            )}
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description <span className="text-red-500">*</span>
            </label>
            <textarea
              {...register("description")}
              rows={4}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder="Enter product description"
            />
            {errors.description && (
              <p className="text-red-500 text-sm mt-1">
                {errors.description.message}
              </p>
            )}
          </div>



          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Packaging Size - 1 Bale <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              min="1"
              step="1"
              {...register("packagingSize")}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            {errors.packagingSize && (
              <p className="text-red-500 text-sm mt-1">
                {errors.packagingSize.message}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Packaging Unit <span className="text-red-500">*</span>
            </label>
            <select
              {...register("packagingUnit")}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="piece">Piece</option>
              <option value="unit">Unit</option>
            </select>
            {errors.packagingUnit && (
              <p className="text-red-500 text-sm mt-1">
                {errors.packagingUnit.message}
              </p>
            )}
          </div>



          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Product Images <span className="text-red-500">*</span>
            </label>
            <ImageUploaderNoCrop
              onImagesUploaded={handleImagesUploaded}
              multiple={true}
              maxFiles={5}
              imageAspectRatio={selectedBrandAspectRatio}
            />
            <p className="mt-1 text-xs text-gray-500">
              {selectedBrandAspectRatio === "1:1"
                ? "Images will be automatically resized to 1:1 ratio (1000x1000px) without cropping"
                : "Images will be automatically resized to 9:16 ratio (562x1000px) without cropping"}
            </p>
            <p className="mt-1 text-xs text-gray-500">
              Aspect ratio is determined by the selected sub category
            </p>
          </div>

          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                {...register("isActive")}
                className="rounded"
              />
              <span className="text-sm font-medium text-gray-700">Active</span>
            </label>
          </div>

          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                {...register("isPublished")}
                className="rounded"
              />
              <span className="text-sm font-medium text-gray-700">
                Published
              </span>
            </label>
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-4 py-2 border rounded-md hover:bg-gray-100"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading || isSubmitting}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300"
          >
            {loading ? "Creating..." : "Create Product"}
          </button>
        </div>
      </form>
    </div>
  );
}
