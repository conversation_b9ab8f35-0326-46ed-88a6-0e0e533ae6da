"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { adminAPI as api } from "@/lib/axios";
import Link from "next/link";
import { FiArrowLeft } from "react-icons/fi";

interface Size {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
}

export default function EditSizePage() {
  const params = useParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [size, setSize] = useState<Size | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    isActive: true,
  });

  useEffect(() => {
    if (params.id) {
      fetchSize();
    }
  }, [params.id]);

  const fetchSize = async () => {
    try {
      setLoading(true);
      const { data } = await api.get(`/sizes/${params.id}`);
      if (data.success) {
        setSize(data.data);
        setFormData({
          name: data.data.name,
          isActive: data.data.isActive,
        });
      } else {
        toast.error("Failed to fetch size details");
        router.push("/admin/sizes");
      }
    } catch (error) {
      console.error("Error fetching size:", error);
      toast.error("Failed to fetch size details");
      router.push("/admin/sizes");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Size name is required");
      return;
    }

    try {
      setSubmitting(true);
      const { data } = await api.put(`/sizes/${params.id}`, formData);
      
      if (data.success) {
        toast.success("Size updated successfully");
        setTimeout(() => {
          router.push("/admin/sizes");
        }, 1500);
      } else {
        toast.error(data.message || "Failed to update size");
      }
    } catch (error: any) {
      console.error("Error updating size:", error);
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Failed to update size");
      }
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <ToastContainer />
      <div className="flex items-center mb-6">
        <Link
          href="/admin/sizes"
          className="flex items-center text-blue-500 hover:text-blue-700 mr-4"
        >
          <FiArrowLeft className="mr-1" /> Back to Sizes
        </Link>
        <h1 className="text-2xl font-bold">Edit Size</h1>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Size Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              required
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              name="isActive"
              id="isActive"
              checked={formData.isActive}
              onChange={handleInputChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
              Active
            </label>
          </div>

          <div className="flex justify-end space-x-3">
            <Link
              href="/admin/sizes"
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={submitting}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300"
            >
              {submitting ? "Saving..." : "Save Changes"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
