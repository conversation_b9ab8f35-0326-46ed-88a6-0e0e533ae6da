import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

interface JWTPayload {
  id: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the auth token from cookies
    const authToken = request.cookies.get("authToken");

    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized - No token", success: false },
        { status: 401 }
      );
    }

    try {
      // Verify the token
      const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as JWTPayload;

      // Check if the user is a vendor
      if (decoded.role !== "VENDOR") {
        return NextResponse.json(
          { message: "Unauthorized - Not a vendor", success: false },
          { status: 401 }
        );
      }

      // Find the vendor associated with this user
      const vendor = await prisma.vendor.findFirst({
        where: { email: decoded.email },
      });

      if (!vendor) {
        console.log("Vendor not found for email:", decoded.email);
        return NextResponse.json(
          { message: "Vendor account not found for this user", success: false },
          { status: 404 }
        );
      }

      const { id } = params;
      console.log("Fetching order details for vendor:", vendor.id, "order ID:", id);

      // Fetch the order with all related data, but only if it belongs to this vendor
      const order = await prisma.order.findFirst({
        where: {
          id: id,
          vendorId: vendor.id, // Ensure the order belongs to this vendor
        },
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  description: true,
                  basePrice: true,
                  gstPercentage: true,
                  packagingSize: true,
                  packagingUnit: true,
                  images: true,
                  imageAspectRatio: true,
                  category: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                  brand: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                  material: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                  size: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              createdAt: "asc",
            },
          },
          vendor: {
            select: {
              id: true,
              businessName: true,
              email: true,
              phoneNumber: true,
              contactPerson: true,
              address: true,
              street: true,
              city: true,
              state: true,
              postalCode: true,
              country: true,
              taxId: true,
              maxCredit: true,
              isCreditGiven: true,
              status: true,
            },
          },
        },
      });

      if (!order) {
        console.log("Order not found or doesn't belong to vendor:", id);
        return NextResponse.json(
          { message: "Order not found", success: false },
          { status: 404 }
        );
      }

      console.log("Order found:", order.id);

      // Calculate order summary
      const orderSummary = {
        subtotal: order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        totalItems: order.items.reduce((sum, item) => sum + item.quantity, 0),
        totalProducts: order.items.length,
        gstAmount: 0, // Will be calculated based on individual product GST
      };

      // Calculate GST for each item
      const itemsWithGst = order.items.map(item => {
        const gstAmount = (item.price * item.quantity * (item.product.gstPercentage / 100));
        orderSummary.gstAmount += gstAmount;
        
        return {
          ...item,
          gstAmount,
          totalWithGst: (item.price * item.quantity) + gstAmount,
        };
      });

      const enrichedOrder = {
        ...order,
        items: itemsWithGst,
        summary: {
          ...orderSummary,
          totalWithGst: orderSummary.subtotal + orderSummary.gstAmount,
        },
      };

      return NextResponse.json({
        message: "Order details fetched successfully",
        success: true,
        data: enrichedOrder,
      });
    } catch (jwtError) {
      console.error("JWT verification error:", jwtError);
      return NextResponse.json(
        { message: "Unauthorized - Invalid token", success: false },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error("Error fetching vendor order details:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch order details",
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// Cancel order (vendor can only cancel pending orders)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authToken = request.cookies.get("authToken");

    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized - No token", success: false },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as JWTPayload;

    if (decoded.role !== "VENDOR") {
      return NextResponse.json(
        { message: "Unauthorized - Not a vendor", success: false },
        { status: 401 }
      );
    }

    const vendor = await prisma.vendor.findFirst({
      where: { email: decoded.email },
    });

    if (!vendor) {
      return NextResponse.json(
        { message: "Vendor account not found", success: false },
        { status: 404 }
      );
    }

    const { id } = params;
    const { action } = await request.json();

    if (action !== "cancel") {
      return NextResponse.json(
        { message: "Invalid action. Only 'cancel' is allowed", success: false },
        { status: 400 }
      );
    }

    // Check if the order exists and belongs to this vendor
    const existingOrder = await prisma.order.findFirst({
      where: {
        id: id,
        vendorId: vendor.id,
      },
    });

    if (!existingOrder) {
      return NextResponse.json(
        { message: "Order not found", success: false },
        { status: 404 }
      );
    }

    // Only allow cancellation of pending orders
    if (existingOrder.status !== "PENDING") {
      return NextResponse.json(
        { 
          message: `Cannot cancel order with status: ${existingOrder.status}. Only pending orders can be cancelled.`, 
          success: false 
        },
        { status: 400 }
      );
    }

    // Update order status to cancelled
    const updatedOrder = await prisma.order.update({
      where: { id },
      data: { status: "CANCELLED" },
      include: {
        items: {
          include: {
            product: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    console.log("Order cancelled by vendor:", id);

    return NextResponse.json({
      message: "Order cancelled successfully",
      success: true,
      data: {
        orderId: updatedOrder.id,
        status: updatedOrder.status,
        totalAmount: updatedOrder.totalAmount,
      },
    });
  } catch (error) {
    console.error("Error cancelling order:", error);
    return NextResponse.json(
      {
        message: "Failed to cancel order",
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
