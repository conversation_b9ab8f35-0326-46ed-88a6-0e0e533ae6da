import { PrismaClient } from "@prisma/client";

// Prevent multiple instances of Prisma Client in development
// eslint-disable-next-line no-undef
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// Create a new PrismaClient if one doesn't exist
export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    log:
      process.env.NODE_ENV === "development"
        ? ["query", "error", "warn"]
        : ["error"],
  });

// If not in production, attach prisma to the global object
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;
