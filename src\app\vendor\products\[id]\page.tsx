"use client";
import { useEffect, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { vendorAPI as api } from "@/lib/axios";
import { ChevronLeft } from "lucide-react";

interface Product {
  id: string;
  name: string;
  description: string;
  basePrice: number;
  packagingSize: number;
  images: string[];
  categoryId: string;
  category?: {
    name: string;
  };
  brand?: {
    name: string;
  };
  material?: {
    name: string;
  };
  size?: {
    name: string;
  };
}

export default function ProductPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [product, setProduct] = useState<Product | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [loading, setLoading] = useState(true);

  const getImageUrl = (url: string) => {
    if (!url) return null;
    if (url.startsWith("http://") || url.startsWith("https://")) {
      return url;
    }
    return `${process.env.NEXT_PUBLIC_API_URL || ""}${
      url.startsWith("/") ? "" : "/"
    }${url}`;
  };

  useEffect(() => {
    if (params.id) {
      fetchProduct();
    }
  }, [params.id]);

  const fetchProduct = async () => {
    try {
      const { data } = await api.get(`/products/${params.id}`);
      if (data.success) {
        setProduct(data.data);
        // Set initial quantity to 1 instead of packaging size
        setQuantity(1);
      }
    } catch (error) {
      console.error("Error fetching product:", error);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async () => {
    if (!product) return;

    try {
      const { data } = await api.post("/cart", {
        productId: product.id,
        quantity
      });

      if (data.success) {
        alert("Product added to cart successfully");
        // Optionally navigate to cart page
        // router.push("/vendor/cart");
      }
    } catch (error) {
      console.error("Error adding to cart:", error);
      alert("Failed to add product to cart");
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <div className="relative w-20 h-20">
          <div className="absolute top-0 left-0 right-0 bottom-0 animate-spin rounded-full h-20 w-20 border-4 border-t-blue-500 border-b-blue-700 border-l-transparent border-r-transparent"></div>
          <div className="absolute top-2 left-2 right-2 bottom-2 animate-pulse bg-white rounded-full flex items-center justify-center">
            <div className="h-8 w-8 text-blue-600">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
            </div>
          </div>
        </div>
        <p className="text-gray-600 font-medium mt-4">Loading product details...</p>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <p className="text-gray-600">Product not found</p>
        <button
          onClick={() => router.back()}
          className="mt-4 inline-flex items-center text-blue-600 hover:text-blue-800"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">


      <button
        onClick={() => router.back()}
        className="mb-6 inline-flex items-center text-blue-600 hover:text-blue-800"
      >
        <ChevronLeft className="h-4 w-4 mr-1" />
        Back to Products
      </button>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Product Image */}
        <div className="aspect-square relative bg-gray-100">
          {product.images?.[0] ? (
            <Image
              src={getImageUrl(product.images[0]) || "/placeholder.png"}
              alt={product.name}
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
              <span className="text-gray-400">No image</span>
            </div>
          )}
        </div>

        {/* Product Details */}
        <div>
          <h1 className="text-2xl font-bold mb-2 uppercase">{product.name}</h1>
          <p className="text-gray-600 mb-4">{product.description}</p>

          <div className="mb-6">
            <p className="text-3xl font-bold">₹{product.basePrice}</p>
            <p className="text-sm text-gray-500 mt-1">
              Package Size: {product.packagingSize} units (for information only)
            </p>
            <p className="text-xs text-gray-500 mt-1">
              You can purchase any quantity, not just multiples of the package size.
            </p>
          </div>

          <div className="mb-6">
            <label className="block text-gray-700 mb-2">Quantity</label>
            <div className="flex items-center">
              <button
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                className="px-3 py-1 border border-gray-300 rounded-l-md bg-gray-100"
              >
                -
              </button>
              <input
                type="number"
                min="1"
                value={quantity}
                onChange={(e) => {
                  const value = parseInt(e.target.value);
                  if (!isNaN(value) && value >= 1) {
                    setQuantity(value);
                  }
                }}
                className="w-16 text-center border-t border-b border-gray-300 py-1"
              />
              <button
                onClick={() => setQuantity(quantity + 1)}
                className="px-3 py-1 border border-gray-300 rounded-r-md bg-gray-100"
              >
                +
              </button>
            </div>
            {quantity === product.packagingSize && (
              <p className="text-sm text-green-600 mt-1">
                You're ordering exactly one complete package.
              </p>
            )}
          </div>

          <button
            onClick={addToCart}
            className="w-full bg-blue-600 text-white py-3 rounded-sm hover:bg-blue-700 transition-colors"
          >
            Add to Cart
          </button>

          <div className="mt-8 border-t pt-6">
            <h3 className="font-semibold mb-2">Product Details</h3>
            <ul className="space-y-2 text-gray-600">
              {product.category && <li><span className="font-medium">Category:</span> {product.category.name}</li>}
              {product.brand && <li><span className="font-medium">Brand:</span> {product.brand.name}</li>}
              {product.material && <li><span className="font-medium">Material:</span> {product.material.name}</li>}
              {product.size && <li><span className="font-medium">Size:</span> {product.size.name}</li>}
              <li>Package Size: {product.packagingSize} piece{product.packagingSize > 1 ? 's' : ''} per packet</li>
              <li className="text-sm text-gray-500">Note: Package size is for information only. You can order any quantity.</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
