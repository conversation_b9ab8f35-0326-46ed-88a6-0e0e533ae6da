"use client";
import { useEffect, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { vendorAPI as api } from "@/lib/axios";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import LoadingSpinner from "@/components/vendor/LoadingSpinner";
import BannerSlider from "@/components/vendor/BannerSlider";
import SubBannerDisplay from "@/components/vendor/SubBannerDisplay";

interface Category {
  id: string;
  name: string;
  imageUrl: string;
  description: string;
}

export default function VendorDashboard() {
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  const getImageUrl = (url: string) => {
    if (!url) return null;
    if (url.startsWith("http://") || url.startsWith("https://")) {
      return url;
    }
    return `${process.env.NEXT_PUBLIC_API_URL || ""}${
      url.startsWith("/") ? "" : "/"
    }${url}`;
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      console.log("Fetching categories...");
      const { data } = await api.get("/categories");
      console.log("Categories response:", data);
      if (data.success) {
        setCategories(data.data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    } finally {
      setLoading(false);
    }
  };



  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <div className="relative w-20 h-20">
          <div className="absolute top-0 left-0 right-0 bottom-0 animate-spin rounded-full h-20 w-20 border-4 border-t-blue-500 border-b-blue-700 border-l-transparent border-r-transparent"></div>
          <div className="absolute top-2 left-2 right-2 bottom-2 animate-pulse bg-white rounded-full flex items-center justify-center">
            <div className="h-8 w-8 text-blue-600">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
            </div>
          </div>
        </div>
        <p className="text-gray-600 font-medium mt-4">Loading categories...</p>
      </div>
    );
  }

  return (
    <div>
      <ToastContainer />



      {/* Banner Slider */}
      <BannerSlider />

      {/* Sub-Banner Display */}
      <SubBannerDisplay />

      <div className="text-center mb-8 sm:mb-12">
        <h2 className="text-2xl sm:text-3xl font-bold uppercase tracking-wider">
          ALL COLLECTIONS
        </h2>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        {categories.map((category) => (
          <div
            key={category.id}
            className="cursor-pointer group relative overflow-hidden"
            onClick={() => router.push(`/vendor/categories/${category.id}`)}
          >
            <div className="aspect-square relative overflow-hidden rounded-lg">
              {category.imageUrl ? (
                <Image
                  src={getImageUrl(category.imageUrl) || "/placeholder.png"}
                  alt={category.name}
                  fill
                  sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                  className="object-cover group-hover:scale-105 transition-transform duration-700"
                />
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-400">No image</span>
                </div>
              )}
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-100 transition-opacity duration-300"></div>
              <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6 text-white">
                <h3 className="font-bold text-lg sm:text-xl uppercase tracking-wide">{category.name}</h3>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
