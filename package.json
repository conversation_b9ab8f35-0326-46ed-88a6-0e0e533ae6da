{"name": "metropolis-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix"}, "dependencies": {"@auth/prisma-adapter": "^2.7.4", "@aws-sdk/client-s3": "^3.758.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@prisma/client": "^6.2.1", "axios": "^1.8.2", "bcryptjs": "^2.4.3", "date-fns": "^4.1.0", "firebase-admin": "^13.4.0", "jose": "^5.10.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.475.0", "moment": "^2.30.1", "next": "14.2.14", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "prisma": "^6.2.1", "react": "^18", "react-dom": "^18", "react-dropzone": "^14.3.8", "react-easy-crop": "^5.4.1", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-image-crop": "^11.0.7", "react-otp-input": "^3.1.1", "react-slick": "^0.30.3", "react-toastify": "^11.0.5", "sharp": "^0.34.1", "slick-carousel": "^1.8.1", "slugify": "^1.6.6", "uuid": "^11.1.0", "yup": "^1.6.1", "zeptomail": "^6.2.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.8", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-slick": "^0.23.13", "@types/uuid": "^10.0.0", "eslint": "^8.57.1", "eslint-config-next": "14.2.14", "eslint-config-prettier": "^10.0.2", "eslint-plugin-next": "^0.0.0", "eslint-plugin-prettier": "^5.2.3", "postcss": "^8", "prettier": "^3.5.2", "tailwindcss": "^3.4.1", "typescript": "^5"}}