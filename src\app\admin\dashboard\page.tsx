// src/app/admin/dashboard/page.tsx
"use client";

import { useEffect, useState } from "react";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import moment from "moment";
import {
  FiUsers,
  FiPackage,
  FiShoppingCart,
  FiDollarSign,
  FiRefreshCw,
  FiCalendar,
  FiSearch,
} from "react-icons/fi";
import { adminAPI as api } from "@/lib/axios";

// Types for dashboard data
interface DashboardStats {
  period: {
    name: string;
    key: string;
  };
  vendors: {
    total: number;
    current: number;
    growth: number;
  };
  products: {
    total: number;
    current: number;
    growth: number;
  };
  orders: {
    total: number;
    current: number;
    growth: number;
  };
  revenue: {
    total: number;
    period: number;
    formatted: string;
    growth: number;
  };
  comparison: string;
}

interface RecentOrder {
  id: string;
  vendor: string;
  amount: string;
  status: string;
  createdAt: string;
}

interface TopVendor {
  id: string;
  name: string;
  orders: string;
  revenue: string;
  performance: string;
}

// Helper function to format date as DD/MM/YYYY for display
const formatDateForInput = (date: Date): string => {
  return moment(date).format('DD/MM/YYYY');
};

// Helper function to parse DD/MM/YYYY to YYYY-MM-DD for date input
const formatDateForAPI = (dateString: string): string => {
  // Check if the date format is valid (DD/MM/YYYY)
  const isValidDate = moment(dateString, 'DD/MM/YYYY', true).isValid();

  if (isValidDate) {
    // Convert to YYYY-MM-DD format for HTML date input
    return moment(dateString, 'DD/MM/YYYY').format('YYYY-MM-DD');
  }

  // If format is invalid, return today's date in YYYY-MM-DD format
  return moment().format('YYYY-MM-DD');
};

export default function Dashboard() {
  // State for dashboard data
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([]);
  const [topVendors, setTopVendors] = useState<TopVendor[]>([]);

  // Date range state
  const [startDate, setStartDate] = useState<string>(() => {
    // Default to first day of current month using Moment.js
    const firstDay = moment().startOf('month').toDate();
    // Format as DD/MM/YYYY
    return formatDateForInput(firstDay);
  });
  const [endDate, setEndDate] = useState<string>(() => {
    // Default to current date using Moment.js
    const now = moment().toDate();
    // Format as DD/MM/YYYY
    return formatDateForInput(now);
  });

  // No longer need date picker visibility state

  // Loading states
  const [loadingStats, setLoadingStats] = useState(true);
  const [loadingOrders, setLoadingOrders] = useState(true);
  const [loadingVendors, setLoadingVendors] = useState(true);

  // Error states
  const [statsError, setStatsError] = useState(false);
  const [ordersError, setOrdersError] = useState(false);
  const [vendorsError, setVendorsError] = useState(false);

  // Fetch dashboard stats
  const fetchStats = async () => {
    try {
      setLoadingStats(true);
      setStatsError(false);

      // Convert DD/MM/YYYY to YYYY-MM-DD for API using Moment.js
      const apiStartDate = moment(startDate, 'DD/MM/YYYY').format('YYYY-MM-DD');
      const apiEndDate = moment(endDate, 'DD/MM/YYYY').format('YYYY-MM-DD');

      console.log("Fetching stats with date range:", { apiStartDate, apiEndDate });
      const url = `/dashboard/stats?startDate=${encodeURIComponent(apiStartDate)}&endDate=${encodeURIComponent(apiEndDate)}`;
      console.log("Request URL:", url);
      const { data } = await api.get(url);
      if (data.success) {
        setStats(data.data);
      } else {
        setStatsError(true);
        toast.error("Failed to load dashboard statistics");
      }
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
      setStatsError(true);
      toast.error("Failed to load dashboard statistics");
    } finally {
      setLoadingStats(false);
    }
  };

  // Fetch recent orders
  const fetchRecentOrders = async () => {
    try {
      setLoadingOrders(true);
      setOrdersError(false);

      // Convert DD/MM/YYYY to YYYY-MM-DD for API using Moment.js
      const apiStartDate = moment(startDate, 'DD/MM/YYYY').format('YYYY-MM-DD');
      const apiEndDate = moment(endDate, 'DD/MM/YYYY').format('YYYY-MM-DD');

      console.log("Fetching recent orders with date range:", { apiStartDate, apiEndDate });
      // Make sure the URL is correctly constructed
      const url = `/dashboard/recent-orders?startDate=${encodeURIComponent(apiStartDate)}&endDate=${encodeURIComponent(apiEndDate)}`;
      console.log("Request URL:", url);
      const { data } = await api.get(url);
      console.log("Recent orders response:", data);
      if (data.success) {
        setRecentOrders(data.data);
      } else {
        setOrdersError(true);
        toast.error("Failed to load recent orders");
      }
    } catch (error) {
      console.error("Error fetching recent orders:", error);
      setOrdersError(true);
      toast.error("Failed to load recent orders");
    } finally {
      setLoadingOrders(false);
    }
  };

  // Fetch top vendors
  const fetchTopVendors = async () => {
    try {
      setLoadingVendors(true);
      setVendorsError(false);

      // Convert DD/MM/YYYY to YYYY-MM-DD for API using Moment.js
      const apiStartDate = moment(startDate, 'DD/MM/YYYY').format('YYYY-MM-DD');
      const apiEndDate = moment(endDate, 'DD/MM/YYYY').format('YYYY-MM-DD');

      console.log("Fetching top vendors with date range:", { apiStartDate, apiEndDate });
      const url = `/dashboard/top-vendors?startDate=${encodeURIComponent(apiStartDate)}&endDate=${encodeURIComponent(apiEndDate)}`;
      console.log("Request URL:", url);
      const { data } = await api.get(url);
      if (data.success) {
        setTopVendors(data.data);
      } else {
        setVendorsError(true);
        toast.error("Failed to load top vendors");
      }
    } catch (error) {
      console.error("Error fetching top vendors:", error);
      setVendorsError(true);
      toast.error("Failed to load top vendors");
    } finally {
      setLoadingVendors(false);
    }
  };

  // Fetch all dashboard data
  const fetchDashboardData = () => {
    fetchStats();
    fetchRecentOrders();
    fetchTopVendors();
  };

  // Handle date filter apply
  const handleApplyDateFilter = () => {
    fetchDashboardData();
  };

  // Load data on component mount
  useEffect(() => {
    fetchDashboardData();
  }, []);

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <ToastContainer position="top-right" autoClose={3000} />

      {/* Date Filter and Refresh buttons */}
      <div className="flex justify-end mb-4 gap-4">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <label htmlFor="startDate" className="text-sm text-gray-600">From:</label>
            <input
              type="date"
              id="startDate"
              value={formatDateForAPI(startDate)}
              onChange={(e) => {
                // Convert from YYYY-MM-DD to DD/MM/YYYY for display
                if (e.target.value) {
                  const formattedDate = moment(e.target.value).format('DD/MM/YYYY');
                  setStartDate(formattedDate);
                }
              }}
              className="border border-gray-300 rounded px-2 py-1 text-sm"
            />
          </div>

          <div className="flex items-center gap-2">
            <label htmlFor="endDate" className="text-sm text-gray-600">To:</label>
            <input
              type="date"
              id="endDate"
              value={formatDateForAPI(endDate)}
              onChange={(e) => {
                // Convert from YYYY-MM-DD to DD/MM/YYYY for display
                if (e.target.value) {
                  const formattedDate = moment(e.target.value).format('DD/MM/YYYY');
                  setEndDate(formattedDate);
                }
              }}
              className="border border-gray-300 rounded px-2 py-1 text-sm"
            />
          </div>

          <button
            onClick={handleApplyDateFilter}
            className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors text-sm"
          >
            <FiSearch className="h-4 w-3" />
            Apply Filter
          </button>
        </div>

        <button
          onClick={fetchDashboardData}
          className="flex items-center gap-2 bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 transition-colors"
        >
          <FiRefreshCw className="h-4 w-4" />
        </button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <StatCard
          title={`Vendors (${stats?.period?.name || "This Month"})`}
          value={stats ? stats.vendors.current.toLocaleString() : "..."}
          icon={<FiUsers className="text-blue-500" />}
          change={stats ? `${stats.vendors.growth > 0 ? "+" : ""}${stats.vendors.growth}% ${stats.comparison || ""}` : "..."}
          loading={loadingStats}
          error={statsError}
        />
        <StatCard
          title={`Products (${stats?.period?.name || "This Month"})`}
          value={stats ? stats.products.current.toLocaleString() : "..."}
          icon={<FiPackage className="text-green-500" />}
          change={stats ? `${stats.products.growth > 0 ? "+" : ""}${stats.products.growth}% ${stats.comparison || ""}` : "..."}
          loading={loadingStats}
          error={statsError}
        />
        <StatCard
          title={`Orders (${stats?.period?.name || "This Month"})`}
          value={stats ? stats.orders.current.toLocaleString() : "..."}
          icon={<FiShoppingCart className="text-purple-500" />}
          change={stats ? `${stats.orders.growth > 0 ? "+" : ""}${stats.orders.growth}% ${stats.comparison || ""}` : "..."}
          loading={loadingStats}
          error={statsError}
        />
        <StatCard
          title={`Revenue (${stats?.period?.name || "This Month"})`}
          value={stats ? `₹${stats.revenue.formatted}` : "..."}
          icon={<FiDollarSign className="text-yellow-500" />}
          change={stats ? `${stats.revenue.growth > 0 ? "+" : ""}${stats.revenue.growth}% ${stats.comparison || ""}` : "..."}
          loading={loadingStats}
          error={statsError}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">Recent Orders</h2>
          {loadingOrders ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : ordersError ? (
            <div className="text-center py-8 text-red-500">
              Failed to load recent orders. Please try again.
            </div>
          ) : recentOrders.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No recent orders found.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Order ID</th>
                    <th className="text-left py-2">Vendor</th>
                    <th className="text-left py-2">Amount</th>
                    <th className="text-left py-2">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {recentOrders.map((order) => (
                    <OrderRow
                      key={order.id}
                      id={order.id.substring(0, 8)}
                      vendor={order.vendor}
                      amount={order.amount}
                      status={order.status}
                    />
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Top Vendors */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">Top Vendors</h2>
          {loadingVendors ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : vendorsError ? (
            <div className="text-center py-8 text-red-500">
              Failed to load top vendors. Please try again.
            </div>
          ) : topVendors.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No vendor data available.
            </div>
          ) : (
            <div className="space-y-4">
              {topVendors.map((vendor) => (
                <VendorCard
                  key={vendor.id}
                  name={vendor.name}
                  orders={vendor.orders}
                  revenue={vendor.revenue}
                  performance={vendor.performance}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  change: string;
  loading?: boolean;
  error?: boolean;
}

function StatCard({ title, value, icon, change, loading = false, error = false }: StatCardProps) {
  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-start">
        <div>
          <p className="text-gray-500 text-sm">{title}</p>
          {loading ? (
            <div className="h-8 mt-1 flex items-center">
              <div className="animate-pulse bg-gray-200 h-6 w-16 rounded"></div>
            </div>
          ) : error ? (
            <h3 className="text-2xl font-bold mt-1 text-red-500">Error</h3>
          ) : (
            <h3 className="text-2xl font-bold mt-1">{value}</h3>
          )}
          {loading ? (
            <div className="h-5 mt-2 flex items-center">
              <div className="animate-pulse bg-gray-200 h-4 w-24 rounded"></div>
            </div>
          ) : error ? (
            <p className="text-red-400 text-sm mt-2">Failed to load</p>
          ) : (
            <p className="text-green-500 text-sm mt-2">{change}</p>
          )}
        </div>
        <div className="text-2xl">{icon}</div>
      </div>
    </div>
  );
}

interface OrderRowProps {
  id: string;
  vendor: string;
  amount: string;
  status: string;
}

function OrderRow({ id, vendor, amount, status }: OrderRowProps) {
  const statusColor: Record<string, string> = {
    PROCESSING: "text-yellow-500",
    PENDING: "text-red-500",
    DELIVERED: "text-green-500",
    SHIPPED: "text-blue-500",
    APPROVED: "text-blue-500",
    REJECTED: "text-red-500",
    CANCELLED: "text-gray-500",
  };

  // Format the status for display
  const formatStatus = (status: string) => {
    // Handle case where status might be lowercase
    const upperStatus = status.toUpperCase();

    // Capitalize the first letter of each word in the status
    return upperStatus.charAt(0) + upperStatus.slice(1).toLowerCase();
  };

  const formattedStatus = formatStatus(status);

  return (
    <tr className="border-b hover:bg-gray-50">
      <td className="py-2">{id}</td>
      <td className="py-2">{vendor}</td>
      <td className="py-2">{amount}</td>
      <td className={`py-2 ${statusColor[status.toUpperCase()] || "text-gray-500"}`}>{formattedStatus}</td>
    </tr>
  );
}

interface VendorCardProps {
  name: string;
  orders: string;
  revenue: string;
  performance: string;
}

function VendorCard({ name, orders, revenue, performance }: VendorCardProps) {
  return (
    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
      <div>
        <h3 className="font-medium">{name}</h3>
        <p className="text-sm text-gray-500">{orders} orders</p>
      </div>
      <div className="text-right">
        <p className="font-medium">{revenue}</p>
        <p className="text-sm text-green-500">{performance}</p>
      </div>
    </div>
  );
}
