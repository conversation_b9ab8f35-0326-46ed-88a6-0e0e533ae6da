import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the auth token from cookies
    const authToken = request.cookies.get("authToken");

    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized - No token", success: false },
        { status: 401 }
      );
    }

    try {
      // Verify the token
      const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
        id: string;
        email: string;
        role: string;
      };

      // Check if the user is an admin
      if (decoded.role !== "ADMIN") {
        return NextResponse.json(
          { message: "Unauthorized - Not an admin", success: false },
          { status: 401 }
        );
      }
    } catch (jwtError) {
      console.error("JWT verification error:", jwtError);
      return NextResponse.json(
        { message: "Unauthorized - Invalid token", success: false },
        { status: 401 }
      );
    }

    const { id } = params;
    console.log("Fetching order details for ID:", id);

    // Fetch the order with all related data
    const order = await prisma.order.findUnique({
      where: {
        id: id,
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
                description: true,
                basePrice: true,
                gstPercentage: true,
                packagingSize: true,
                packagingUnit: true,
                images: true,
                imageAspectRatio: true,
                category: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
                brand: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
                material: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
                size: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
        vendor: {
          select: {
            id: true,
            businessName: true,
            email: true,
            phoneNumber: true,
            contactPerson: true,
            address: true,
            street: true,
            city: true,
            state: true,
            postalCode: true,
            country: true,
            taxId: true,
            maxCredit: true,
            isCreditGiven: true,
            status: true,
            createdAt: true,
          },
        },
      },
    });

    if (!order) {
      console.log("Order not found with ID:", id);
      return NextResponse.json(
        { message: "Order not found", success: false },
        { status: 404 }
      );
    }

    console.log("Order found:", order.id);

    // Calculate order summary
    const orderSummary = {
      subtotal: order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0),
      totalItems: order.items.reduce((sum, item) => sum + item.quantity, 0),
      totalProducts: order.items.length,
      gstAmount: 0, // Will be calculated based on individual product GST
    };

    // Calculate GST for each item
    const itemsWithGst = order.items.map(item => {
      const gstAmount = (item.price * item.quantity * (item.product.gstPercentage / 100));
      orderSummary.gstAmount += gstAmount;
      
      return {
        ...item,
        gstAmount,
        totalWithGst: (item.price * item.quantity) + gstAmount,
      };
    });

    const enrichedOrder = {
      ...order,
      items: itemsWithGst,
      summary: {
        ...orderSummary,
        totalWithGst: orderSummary.subtotal + orderSummary.gstAmount,
      },
    };

    return NextResponse.json({
      message: "Order details fetched successfully",
      success: true,
      data: enrichedOrder,
    });
  } catch (error) {
    console.error("Error fetching order details:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch order details",
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the auth token from cookies
    const authToken = request.cookies.get("authToken");

    if (!authToken?.value) {
      return NextResponse.json(
        { message: "Unauthorized - No token", success: false },
        { status: 401 }
      );
    }

    try {
      // Verify the token
      const decoded = jwt.verify(authToken.value, process.env.JWT_KEY!) as {
        id: string;
        email: string;
        role: string;
      };

      // Check if the user is an admin
      if (decoded.role !== "ADMIN") {
        return NextResponse.json(
          { message: "Unauthorized - Not an admin", success: false },
          { status: 401 }
        );
      }
    } catch (jwtError) {
      console.error("JWT verification error:", jwtError);
      return NextResponse.json(
        { message: "Unauthorized - Invalid token", success: false },
        { status: 401 }
      );
    }

    const { id } = params;
    console.log("Deleting order with ID:", id);

    // Check if the order exists
    const existingOrder = await prisma.order.findUnique({
      where: { id },
      include: {
        vendor: {
          select: {
            businessName: true,
          },
        },
      },
    });

    if (!existingOrder) {
      return NextResponse.json(
        { message: "Order not found", success: false },
        { status: 404 }
      );
    }

    // Delete the order (OrderItems will be deleted automatically due to cascade)
    await prisma.order.delete({
      where: { id },
    });

    console.log("Order deleted successfully:", id);

    return NextResponse.json({
      message: "Order deleted successfully",
      success: true,
      data: {
        deletedOrderId: id,
        vendorName: existingOrder.vendor.businessName,
      },
    });
  } catch (error) {
    console.error("Error deleting order:", error);
    return NextResponse.json(
      {
        message: "Failed to delete order",
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
