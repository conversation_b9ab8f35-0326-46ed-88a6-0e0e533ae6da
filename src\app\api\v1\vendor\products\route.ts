import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const category = searchParams.get("category");
    const brand = searchParams.get("brand");
    const sort = searchParams.get("sort") || "newest";
    const minPrice = searchParams.get("minPrice");
    const maxPrice = searchParams.get("maxPrice");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");
    const skip = (page - 1) * limit;

    let where: any = {
      isActive: true,
      isPublished: true, // Only show published products to vendors
    };

    if (category) {
      where.categoryId = category;
    }

    if (brand) {
      where.brandId = brand;
    }

    if (minPrice) {
      where.basePrice = {
        ...where.basePrice,
        gte: parseFloat(minPrice),
      };
    }

    if (maxPrice) {
      where.basePrice = {
        ...where.basePrice,
        lte: parseFloat(maxPrice),
      };
    }

    let orderBy: any = {};
    if (sort === "newest") {
      orderBy.createdAt = "desc";
    } else if (sort === "price_asc") {
      orderBy.basePrice = "asc";
    } else if (sort === "price_desc") {
      orderBy.basePrice = "desc";
    }

    // Get total count for pagination
    const totalProducts = await prisma.product.count({ where });

    const products = await prisma.product.findMany({
      where,
      orderBy,
      skip,
      take: limit,
      select: {
        id: true,
        name: true,
        description: true,
        basePrice: true,
        packagingSize: true,
        packagingUnit: true,
        images: true,
        category: {
          select: {
            name: true,
          },
        },
        brand: {
          select: {
            name: true,
            imageAspectRatio: true,
          },
        },
        material: {
          select: {
            name: true,
          },
        },
        size: {
          select: {
            name: true,
          },
        },
      },
    });

    console.log("Fetched products:", products);

    const totalPages = Math.ceil(totalProducts / limit);

    return NextResponse.json({
      message: "Products fetched successfully",
      success: true,
      data: products,
      pagination: {
        total: totalProducts,
        page,
        limit,
        totalPages
      }
    });
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json(
      {
        message: "Failed to fetch products",
        success: false,
      },
      { status: 500 }
    );
  }
}
